// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBSgGnWiNjZRG_YoD2dZ0g-6OXJ1y_m_2A',
    appId: '1:363604231725:web:a4dc7fbd14ea38b29e1aa4',
    messagingSenderId: '363604231725',
    projectId: 'skillapp-eea7c',
    authDomain: 'skillapp-eea7c.firebaseapp.com',
    storageBucket: 'skillapp-eea7c.appspot.com',
    measurementId: 'G-BY6Y9QKKJH',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB1n-VJLjjaxR6l4DEc9uxqTOjUzf14hus',
    appId: '1:363604231725:android:625a6ddf0c63a4b49e1aa4',
    messagingSenderId: '363604231725',
    projectId: 'skillapp-eea7c',
    storageBucket: 'skillapp-eea7c.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBbY97q5wDwbc5p2b7E7FW2UrY5fq6zO_I',
    appId: '1:363604231725:ios:a1735fc236935cd69e1aa4',
    messagingSenderId: '363604231725',
    projectId: 'skillapp-eea7c',
    storageBucket: 'skillapp-eea7c.appspot.com',
    iosClientId: '363604231725-eaa7am155dcs8us0108g4l5k9noli6bf.apps.googleusercontent.com',
    iosBundleId: 'com.example.skillapp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBbY97q5wDwbc5p2b7E7FW2UrY5fq6zO_I',
    appId: '1:363604231725:ios:a1735fc236935cd69e1aa4',
    messagingSenderId: '363604231725',
    projectId: 'skillapp-eea7c',
    storageBucket: 'skillapp-eea7c.appspot.com',
    iosClientId: '363604231725-eaa7am155dcs8us0108g4l5k9noli6bf.apps.googleusercontent.com',
    iosBundleId: 'com.example.skillapp',
  );
}
