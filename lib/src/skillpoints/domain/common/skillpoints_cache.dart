import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';
import 'package:skillapp/src/skillpoints/domain/entities/skillpoints.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_config_repo.dart';

class SkillpointsCache {
  final List<SkillpointsConfig> _skillPointsConfigList = [];
  final SkillpointsConfigRepo _skillPointsConfigRepo;

  SkillpointsCache(this._skillPointsConfigRepo) {
    initConfig();
  }

  void initConfig() {
    final result = _skillPointsConfigRepo.populateSkillPointsCache();

    result.then((value) {
      value.fold((l) => null, (r) {
        _skillPointsConfigList.addAll(r);
      });
    });
  }

  void clear() {
    _skillPointsConfigList.clear();
  }

  SkillpointsConfig getSkillPointsConfigData(SkillEventType skillEventType) {
    return _skillPointsConfigList
        .firstWhere((element) => element.skillPointEventType == skillEventType);
  }

  int getMaxPointsAllowedPerDay(SkillEventType skillEventType) {
    return _skillPointsConfigList
        .firstWhere((element) => element.skillPointEventType == skillEventType)
        .maxPointsAllowedPerDay;
  }

  int getEligiblePoints(SkillEventType skillEventType) {
    return _skillPointsConfigList
        .firstWhere((element) => element.skillPointEventType == skillEventType)
        .eligiblePoints;
  }
}
