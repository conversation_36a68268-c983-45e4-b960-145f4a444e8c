import 'package:skillapp/core/errors/exceptions.dart';

enum SkillEventType {
  practiceQuestionSuccess,
  streakComplete,
  /*practiceQuestionSuccessFirstAttempt,
  practiceQuestionSuccessSecondAttempt,
  streakDayComplete,
  streakWeekComplete,
  mockTest90Percentage,
  mocktest80Percentage,
  mocktest70Percentage,*/
}

SkillEventType stringToSkillEventType(String value) {
  switch (value) {
    case 'practice_question_success_event':
      return SkillEventType.practiceQuestionSuccess;
    case 'streak_complete_event':
      return SkillEventType.streakComplete;
    /*
    case 'practiceQuestionSuccessFirstAttempt':
      return SkillEventType.practiceQuestionSuccessFirstAttempt;
    case 'practiceQuestionSuccessSecondAttempt':
      return SkillEventType.practiceQuestionSuccessSecondAttempt;
    case 'streakDayComplete':
      return SkillEventType.streakDayComplete;
    case 'streakWeekComplete':
      return SkillEventType.streakWeekComplete;
    case 'mockTest90Percentage':
      return SkillEventType.mockTest90Percentage;
    case 'mocktest80Percentage':
      return SkillEventType.mocktest80Percentage;
    case 'mocktest70Percentage':
      return SkillEventType.mocktest70Percentage;
    */
    default:
      throw ServerException(
          message: 'Invalid SkillEventType $value', statusCode: '500');
  }
}

String skillEventTypeToString(SkillEventType value) {
  switch (value) {
    case SkillEventType.practiceQuestionSuccess:
      return 'practice_question_success_event';
    case SkillEventType.streakComplete:
      return 'streak_complete_event';
    /*
    case SkillEventType.practiceQuestionSuccessFirstAttempt:
      return 'practiceQuestionSuccessFirstAttempt';
    case SkillEventType.practiceQuestionSuccessSecondAttempt:
      return 'practiceQuestionSuccessSecondAttempt';
    case SkillEventType.streakDayComplete:
      return 'streakDayComplete';
    case SkillEventType.streakWeekComplete:
      return 'streakWeekComplete';
    case SkillEventType.mockTest90Percentage:
      return 'mockTest90Percentage';
    case SkillEventType.mocktest80Percentage:
      return 'mocktest80Percentage';
    case SkillEventType.mocktest70Percentage:
      return 'mocktest70Percentage';
    */
    default:
      throw ServerException(
          message: 'Invalid SkillEventType $value', statusCode: '500');
  }
}
