import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_repo.dart';

class UpdateSkillpoints
    extends FutureUsecaseWithParams<void, UpdateSkillpointsParams> {
  final SkillpointsRepo _skillPointsRepo;

  UpdateSkillpoints({required SkillpointsRepo skillPointsRepo})
      : _skillPointsRepo = skillPointsRepo;

  @override
  ResultFuture call(params) => _skillPointsRepo.addSkillPointsEntry(
      params.skillEventType, params.description);
}

class UpdateSkillpointsParams {
  final SkillEventType skillEventType;
  final String description;

  UpdateSkillpointsParams(
      {required this.skillEventType, required this.description});
}
