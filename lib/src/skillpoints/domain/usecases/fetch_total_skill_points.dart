import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_repo.dart';

class FetchTotalSkillpoints extends FutureUsecaseWithoutParams<int> {
  final SkillpointsRepo _skillPointsRepo;

  FetchTotalSkillpoints({required SkillpointsRepo skillPointsRepo})
      : _skillPointsRepo = skillPointsRepo;

  @override
  ResultFuture<int> call() => _skillPointsRepo.fetchTotalSkillPoints();
}
