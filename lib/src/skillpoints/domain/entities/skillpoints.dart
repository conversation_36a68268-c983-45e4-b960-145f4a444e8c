import 'package:equatable/equatable.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';

class SkillpointsConfig extends Equatable {
  final int eligiblePoints;
  final int maxPointsAllowedPerDay;
  final SkillEventType skillPointEventType;

  const SkillpointsConfig({
    required this.eligiblePoints,
    required this.maxPointsAllowedPerDay,
    required this.skillPointEventType,
  });

  @override
  List<Object?> get props =>
      [eligiblePoints, maxPointsAllowedPerDay, skillPointEventType];
}

class SkillpointsSummary extends Equatable {
  final int availablePoints;
  final int totalPoints;
  final int pointsEarnedToday;
  final int pointsEarnedThisWeek;
  final int pointsEarnedThisMonth;
  final int pointsEarnedThisYear;

  const SkillpointsSummary({
    required this.availablePoints,
    required this.totalPoints,
    this.pointsEarnedToday = -1,
    this.pointsEarnedThisWeek = -1,
    this.pointsEarnedThisMonth = -1,
    this.pointsEarnedThisYear = -1,
  });

  @override
  List<Object?> get props => [
        availablePoints,
        totalPoints,
        pointsEarnedToday,
        pointsEarnedThisWeek,
        pointsEarnedThisMonth,
        pointsEarnedThisYear,
      ];

  SkillpointsSummary empty() {
    return const SkillpointsSummary(
      availablePoints: -1,
      totalPoints: -1,
    );
  }

  bool isEmpty() {
    return this == empty();
  }
}

class SkillpointEntry extends Equatable {
  final String id;
  final int pointsEarned;
  final SkillEventType skillPointEventType;
  final String skillPointEventDescription;
  final String skillPointEventDate;

  const SkillpointEntry({
    required this.id,
    required this.pointsEarned,
    required this.skillPointEventType,
    required this.skillPointEventDescription,
    required this.skillPointEventDate,
  });

  @override
  List<Object?> get props => [
        id,
        pointsEarned,
        skillPointEventType,
        skillPointEventDescription,
        skillPointEventDate,
      ];
}

class SkillpointsDetails extends Equatable {
  final List<SkillpointEntry> skillPointEntries;
  final SkillpointsSummary skillPointsSummary;

  const SkillpointsDetails({
    required this.skillPointEntries,
    required this.skillPointsSummary,
  });

  @override
  List<Object?> get props => [skillPointEntries, skillPointsSummary];
}
