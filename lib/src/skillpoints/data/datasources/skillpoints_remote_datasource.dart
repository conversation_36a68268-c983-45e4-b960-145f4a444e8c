import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';
import 'package:skillapp/src/skillpoints/domain/entities/skillpoints.dart';

abstract class SkillpointsRemoteDatasource {
  Future<List<SkillpointsConfig>> fetchSkillPointsConfig();
  Future<void> updatedSkillPointEntry(String profileId,
      SkillEventType skillEventType, String description, int points);
  Future<int> fetchTotalPoints(String profileId);
}

class SkillpointsRemoteDatasourceImpl implements SkillpointsRemoteDatasource {
  final FirebaseFirestore _firebaseFirestore;

  SkillpointsRemoteDatasourceImpl({
    required FirebaseFirestore firebaseFirestore,
  }) : _firebaseFirestore = firebaseFirestore;

  @override
  Future<List<SkillpointsConfig>> fetchSkillPointsConfig() async {
    try {
      QuerySnapshot questionBundleSnapshot =
          await _firebaseFirestore.collection('skill_point_config').get();

      return questionBundleSnapshot.docs.map((config) {
        return SkillpointsConfig(
            eligiblePoints: config['eligible_points'],
            maxPointsAllowedPerDay: config['max_limit_per_day'],
            skillPointEventType: stringToSkillEventType(config.id));
      }).toList();
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<void> updatedSkillPointEntry(String profileId,
      SkillEventType skillEventType, String description, int points) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> profileDoc =
          await _firebaseFirestore
              .collection('skill_points_audit')
              .doc(profileId)
              .get();
      if (!profileDoc.exists) {
        await _firebaseFirestore
            .collection('skill_points_audit')
            .doc(profileId)
            .set({
          'redeemed_points': 0,
          'total_points': points,
        });
      } else {
        //fetch the current balance
        int totalPoints = profileDoc['total_points'];

        await _firebaseFirestore
            .collection('skill_points_audit')
            .doc(profileId)
            .update({'total_points': totalPoints + points});
      }

      _firebaseFirestore
          .collection('skill_points_audit/$profileId/points_history')
          .doc()
          .set(
        {
          'additional_history': 'description',
          'event': skillEventTypeToString(skillEventType),
          'points_earned': points,
          'cre_time': DateTime.now()
        },
      );
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  //Fetch total_points
  @override
  Future<int> fetchTotalPoints(String profileId) async {
    DocumentSnapshot<Map<String, dynamic>> profileDoc = await _firebaseFirestore
        .collection('skill_points_audit')
        .doc(profileId)
        .get();
    if (profileDoc.exists) {
      return profileDoc['total_points'];
    } else {
      return 0;
    }
  }
}
