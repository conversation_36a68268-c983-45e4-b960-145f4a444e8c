import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/skillpoints/data/datasources/skillpoints_remote_datasource.dart';
import 'package:skillapp/src/skillpoints/domain/entities/skillpoints.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_config_repo.dart';

class SkillpointsConfigRepoImpl implements SkillpointsConfigRepo {
  final SkillpointsRemoteDatasource _skillPointsRemoteDatasource;

  SkillpointsConfigRepoImpl(
      {required SkillpointsRemoteDatasource skillPointsRemoteDatasource})
      : _skillPointsRemoteDatasource = skillPointsRemoteDatasource;

  @override
  ResultFuture<List<SkillpointsConfig>> populateSkillPointsCache() async {
    try {
      final result =
          await _skillPointsRemoteDatasource.fetchSkillPointsConfig();

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }
}
