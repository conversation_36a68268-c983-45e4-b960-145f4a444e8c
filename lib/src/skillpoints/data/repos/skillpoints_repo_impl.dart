import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/skillpoints/data/datasources/skillpoints_remote_datasource.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoints_cache.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_repo.dart';

class SkillPointsRepoImpl extends SkillpointsRepo {
  final SkillpointsRemoteDatasource _skillPointsRemoteDatasource;
  final SkillpointsCache _skillPointsCache;
  final CacheContext _cacheContext;

  SkillPointsRepoImpl(
      {required SkillpointsRemoteDatasource skillPointsRemoteDatasource,
      required SkillpointsCache skillPointsCache,
      required CacheContext cacheContext})
      : _skillPointsRemoteDatasource = skillPointsRemoteDatasource,
        _skillPointsCache = skillPointsCache,
        _cacheContext = cacheContext;

  @override
  ResultFuture<void> addSkillPointsEntry(
      SkillEventType skillEventType, String description) async {
    try {
      //TODO-Add check to see if the maximum points for the day has been reached already

      await _skillPointsRemoteDatasource.updatedSkillPointEntry(
          _cacheContext.profileId,
          skillEventType,
          description,
          _skillPointsCache.getEligiblePoints(skillEventType));
      return const Right(null);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<int> fetchTotalSkillPoints() async {
    try {
      final result = await _skillPointsRemoteDatasource.fetchTotalPoints(
        _cacheContext.profileId,
      );
      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }
}
