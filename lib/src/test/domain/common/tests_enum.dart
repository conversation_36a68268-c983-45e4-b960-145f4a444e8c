enum TestTypes {
  sectional,
  fullMocktest,
  daily,
  notAvailable;
}

//convert the string to enum
TestTypes testTypesFromString(String value) {
  return TestTypes.values.firstWhere((e) => e.toString() == value);
}

//convert the enum to string
String testTypesToString(TestTypes value) {
  return value.toString().split('.').last;
}

enum TestStatus {
  notStarted,
  started,
  inProgress,
  completed,
  abandoned,
  none;
}

//convert the string to enum
TestStatus testStatusFromString(String value) {
  return TestStatus.values.firstWhere((e) => e.toString() == value);
}

//convert the enum to string
String testStatusToString(TestStatus value) {
  return value.toString().split('.').last;
}

enum TestResultStatus {
  completed,
  failed,
  pending,
}
