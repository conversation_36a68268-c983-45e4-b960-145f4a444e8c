import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test_config.dart';
import 'package:skillapp/src/test/domain/repos/tests_configuration_repo.dart';

class TestsConfigCache {
  final List<TestConfig> _testsConfigList = [];
  final Map<TestTypes, List<TestConfig>> _testConfigMap = {};

  final TestsConfigurationRepo _testsConfigRepo;

  TestsConfigCache(this._testsConfigRepo) {
    initConfig();
  }

  Future<void> initConfig() async {
    try {
      final result = await _testsConfigRepo.fetchTestsConfugration();

      result.fold((l) => null, (r) {
        _testsConfigList.addAll(r);
      });

      // Add the data into the map with test type as key
      for (var element in _testsConfigList) {
        if (_testConfigMap.containsKey(element.type)) {
          _testConfigMap[element.type]!.add(element);
        } else {
          _testConfigMap[element.type] = [element];
        }
      }
    } catch (e) {
      print("Error initializing config: $e");
    }
  }

  void clear() {
    _testsConfigList.clear();
    _testConfigMap.clear();
  }

  List<TestConfig> getTestConfigList(TestTypes type) {
    return _testConfigMap[type] ?? [];
  }

  TestConfig getTestConfig(TestTypes type, String subject) {
    TestConfig? config = _testConfigMap[type]?.firstWhere(
        (element) => element.subject == subject,
        orElse: () => TestConfig.empty());

    if (config == null || config == TestConfig.empty()) {
      // throw Exception('TestConfig not found for $type and $subject');
      print("JB:TestConfig not found for $type and $subject");
      return TestConfig.empty();
    } else {
      return config;
    }
  }
}
