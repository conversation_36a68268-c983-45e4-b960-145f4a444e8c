import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';

abstract class TestsDataRepo {
  ResultFuture<SectionalOrDailyTestData> fetchTestsData(String testId);

  ResultFuture<Test> fetchNewSectionalTest(String subject);

  ResultFuture<void> markTestAsStarted(String testAttemptId, String testId,
      int numberOfQuestions, TestTypes type);

  ResultFuture<void> answerAQuestion(
      AnsweredQuestion answeredQuestion, String testAttemptId, TestTypes type);

  ResultFuture<bool> submitTest(String testAttemptId, TestTypes type);

  ResultFuture<TestAttemptEntry> fetchTestAttemptStatus(
      String params, TestTypes type);

  ResultFuture<List<QuestionAttemptData>> fetchTestAttemptDetails(
      String testAttemptId, TestTypes type);

  ResultFuture<List<SectionalTestAttemptSummary>> fetchTestAttemptHistory(
      TestTypes type);

  ResultFuture<List<GroupedSectionalTestAttempts>>
      fetchGroupedTestAttemptHistory(TestTypes type);

  ResultFuture<TestAttemptAnalysis> fetchTestAttemptAnalysis(
      String testAttemptId, TestTypes type);

  ResultFuture<TestResultSummary> fetchTestResultSummary(
      String testAttemptId, TestTypes type);

  ResultFuture<TestQuestionsWithAnswerStatus>
      fetchTestQuestionsWithAnswerStatus(String testAttemptId, TestTypes type);

  ResultFuture<Test> fetchTodaysDailyTest();

  ResultFuture<bool> isTodaysDailyTestAttempted();
}
