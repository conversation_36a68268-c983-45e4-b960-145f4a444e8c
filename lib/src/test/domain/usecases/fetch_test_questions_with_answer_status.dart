import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchTestQuestionsWithAnswerStatus extends FutureUsecaseWithParams<
    TestQuestionsWithAnswerStatus, FetchTestQuestionsWithAnswerStatusParams> {
  final TestsDataRepo _testsDataRepo;

  FetchTestQuestionsWithAnswerStatus({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<TestQuestionsWithAnswerStatus> call(
          FetchTestQuestionsWithAnswerStatusParams params) =>
      _testsDataRepo.fetchTestQuestionsWithAnswerStatus(
          params.testAttemptId, params.type);
}

class FetchTestQuestionsWithAnswerStatusParams {
  final String testAttemptId;
  final TestTypes type;

  FetchTestQuestionsWithAnswerStatusParams({
    required this.testAttemptId,
    required this.type,
  });
}
