import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class MarkTestAsStarted
    extends FutureUsecaseWithParams<void, MarkTestAsStartedParams> {
  final TestsDataRepo _testsDataRepo;

  MarkTestAsStarted({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<void> call(MarkTestAsStartedParams params) =>
      _testsDataRepo.markTestAsStarted(params.testAttemptId, params.testId,
          params.numberOfQuestions, params.testType);
}

class MarkTestAsStartedParams {
  final String testAttemptId;
  final String testId;
  final int numberOfQuestions;
  final TestTypes testType;

  const MarkTestAsStartedParams(
      {required this.testAttemptId,
      required this.testId,
      required this.numberOfQuestions,
      required this.testType});
}
