import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class AnswerAQuestion
    extends FutureUsecaseWithParams<void, AnswerAQuestionParams> {
  final TestsDataRepo _testsDataRepo;

  AnswerAQuestion({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<void> call(AnswerAQuestionParams params) =>
      _testsDataRepo.answerAQuestion(
          AnsweredQuestion(
            testId: params.testId,
            testType: params.testTypes,
            questionId: params.questionId,
            selectedAnswer: params.selectedAnswer,
            isCorrect: params.isCorrect,
          ),
          params.testAttemptId,
          params.testTypes);
}

class AnswerAQuestionParams {
  final String testAttemptId;
  final String testId;
  final String questionId;
  final String selectedAnswer;
  final bool isCorrect;
  final TestTypes testTypes;

  AnswerAQuestionParams({
    required this.testAttemptId,
    required this.testId,
    required this.questionId,
    required this.selectedAnswer,
    required this.isCorrect,
    required this.testTypes,
  });
}
