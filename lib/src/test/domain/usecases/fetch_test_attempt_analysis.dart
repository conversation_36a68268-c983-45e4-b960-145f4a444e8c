import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchTestAttemptAnalysis extends FutureUsecaseWithParams<
    TestAttemptAnalysis, TestAttemptAnalysisParams> {
  final TestsDataRepo _testsDataRepo;

  FetchTestAttemptAnalysis({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<TestAttemptAnalysis> call(TestAttemptAnalysisParams params) =>
      _testsDataRepo.fetchTestAttemptAnalysis(
          params.testAttemptId, params.testTypes);
}

class TestAttemptAnalysisParams {
  String testAttemptId;
  TestTypes testTypes;

  TestAttemptAnalysisParams({
    required this.testAttemptId,
    required this.testTypes,
  });
}
