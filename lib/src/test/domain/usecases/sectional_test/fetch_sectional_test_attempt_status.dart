import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchSectionalTestAttemptStatus
    extends FutureUsecaseWithParams<TestAttemptEntry, TestAttemptStatusParams> {
  final TestsDataRepo _testsDataRepo;

  FetchSectionalTestAttemptStatus({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<TestAttemptEntry> call(TestAttemptStatusParams params) =>
      _testsDataRepo.fetchTestAttemptStatus(
          params.testAttemptId, params.testTypes);
}

class TestAttemptStatusParams {
  String testAttemptId;
  TestTypes testTypes;

  TestAttemptStatusParams({
    required this.testAttemptId,
    required this.testTypes,
  });
}
