import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchSectionalTestAttemptHistory extends FutureUsecaseWithParams<
    List<SectionalTestAttemptSummary>, TestTypes> {
  final TestsDataRepo _testsDataRepo;

  FetchSectionalTestAttemptHistory({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<List<SectionalTestAttemptSummary>> call(params) =>
      _testsDataRepo.fetchTestAttemptHistory(params);
}
