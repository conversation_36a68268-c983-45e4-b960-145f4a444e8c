import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class SubmitTest extends FutureUsecaseWithParams<void, SubmitTestParams> {
  final TestsDataRepo _testsDataRepo;

  SubmitTest({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<void> call(SubmitTestParams params) =>
      _testsDataRepo.submitTest(params.testAttemptId, params.testType);
}

class SubmitTestParams {
  final String testAttemptId;
  final TestTypes testType;

  SubmitTestParams({
    required this.testAttemptId,
    required this.testType,
  });
}
