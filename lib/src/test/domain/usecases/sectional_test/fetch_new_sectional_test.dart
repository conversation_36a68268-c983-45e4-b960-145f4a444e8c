import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchNewSectionalTest extends FutureUsecaseWithParams<Test, String> {
  final TestsDataRepo _testsDataRepo;

  FetchNewSectionalTest({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<Test> call(String params) =>
      _testsDataRepo.fetchNewSectionalTest(params);
}
