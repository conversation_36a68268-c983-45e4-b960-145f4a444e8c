import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchTestAttemptDetails extends FutureUsecaseWithParams<
    List<QuestionAttemptData>, TestAttemptDetailsParams> {
  final TestsDataRepo _testsDataRepo;

  FetchTestAttemptDetails({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<List<QuestionAttemptData>> call(
          TestAttemptDetailsParams params) =>
      _testsDataRepo.fetchTestAttemptDetails(
          params.testAttemptId, params.testType);
}

class TestAttemptDetailsParams {
  final String testAttemptId;
  final TestTypes testType;

  TestAttemptDetailsParams({
    required this.testAttemptId,
    required this.testType,
  });
}
