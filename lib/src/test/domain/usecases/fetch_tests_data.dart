import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchTestsData
    extends FutureUsecaseWithParams<SectionalOrDailyTestData, String> {
  final TestsDataRepo _testsDataRepo;

  FetchTestsData({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<SectionalOrDailyTestData> call(String params) =>
      _testsDataRepo.fetchTestsData(params);
}
