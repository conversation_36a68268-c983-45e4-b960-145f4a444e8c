import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchTestResultSummary extends FutureUsecaseWithParams<TestResultSummary,
    TestResultSummaryParams> {
  final TestsDataRepo _testsDataRepo;

  FetchTestResultSummary({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<TestResultSummary> call(TestResultSummaryParams params) =>
      _testsDataRepo.fetchTestResultSummary(
          params.testAttemptId, params.testTypes);
}

class TestResultSummaryParams {
  String testAttemptId;
  TestTypes testTypes;

  TestResultSummaryParams({
    required this.testAttemptId,
    required this.testTypes,
  });
}
