import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class FetchTodaysDailyTest extends FutureUsecaseWithoutParams<Test> {
  final TestsDataRepo _testsDataRepo;

  FetchTodaysDailyTest({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<Test> call() => _testsDataRepo.fetchTodaysDailyTest();
}
