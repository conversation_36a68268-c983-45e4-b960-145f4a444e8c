import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class IsDailyTestAttempted extends FutureUsecaseWithoutParams<bool> {
  final TestsDataRepo _testsDataRepo;

  IsDailyTestAttempted({required TestsDataRepo testsDataRepo})
      : _testsDataRepo = testsDataRepo;

  @override
  ResultFuture<bool> call() => _testsDataRepo.isTodaysDailyTestAttempted();
}
