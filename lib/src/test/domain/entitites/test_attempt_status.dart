import 'package:skillapp/src/test/domain/common/tests_enum.dart';

class TestAttemptStatus {
  final String testAttemptId;
  final String testId;
  final String testDescription;
  final String subject;
  final String description;
  final TestTypes testType;
  final String testStatus;
  final DateTime? submittedAt;
  final int timeTakenInMinutes;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unattemptedQuestions;

  TestAttemptStatus({
    required this.testAttemptId,
    required this.testId,
    required this.subject,
    required this.description,
    this.testDescription = '',
    required this.testType,
    required this.testStatus,
    this.submittedAt,
    required this.timeTakenInMinutes,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.unattemptedQuestions,
  });

  factory TestAttemptStatus.fromJson(Map<String, dynamic> json) {
    return TestAttemptStatus(
      testAttemptId: json['testAttemptId'] ?? '',
      testId: json['testId'] ?? '',
      subject: json['subject'] ?? '',
      description: json['description'] ?? '',
      testDescription: json['testDescription'] ?? '',
      testType: _parseTestType(json['testType']),
      testStatus: _parseTestStatus(json['status']),
      submittedAt: json['submittedAt'] != null
          ? DateTime.parse(json['submittedAt'])
          : null,
      timeTakenInMinutes: json['timeTakenInMinutes'] ?? 0,
      correctAnswers: json['correctAnswers'] ?? 0,
      incorrectAnswers: json['incorrectAnswers'] ?? 0,
      unattemptedQuestions: json['unattemptedQuestions'] ?? 0,
    );
  }

  static TestTypes _parseTestType(String? typeStr) {
    if (typeStr == 'sectional') {
      return TestTypes.sectional;
    } else if (typeStr == 'fullMocktest') {
      return TestTypes.fullMocktest;
    } else if (typeStr == 'daily') {
      return TestTypes.daily;
    } else {
      return TestTypes.sectional; // Default
    }
  }

  static String _parseTestStatus(String? statusStr) {
    if (statusStr == 'completed') {
      return 'completed';
    } else if (statusStr == 'inProgress') {
      return 'inProgress';
    } else {
      return 'notStarted'; // Default
    }
  }
}
