class QuestionAttemptData {
  final String questionId;
  final String questionTitle;
  final String selectedAnswer;
  final String correctAnswer;
  final bool isCorrect;
  final int order;
  final bool attemptStatus;

  // Enhanced fields for question content
  final String? questionText;
  final String? questionDescription;
  final List<String>? answerOptions;
  final String? explanation;
  final String? questionType;
  final String? difficulty;
  final Map<String, dynamic>? questionData; // For complex question structures

  QuestionAttemptData({
    required this.questionId,
    required this.questionTitle,
    required this.selectedAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.order,
    this.attemptStatus = true,
    this.questionText,
    this.questionDescription,
    this.answerOptions,
    this.explanation,
    this.questionType,
    this.difficulty,
    this.questionData,
  });

  factory QuestionAttemptData.fromJson(Map<String, dynamic> json) {
    return QuestionAttemptData(
      questionId: json['questionId'] ?? '',
      questionTitle: json['questionTitle'] ?? 'Selective Ninja Entry Quiz',
      selectedAnswer: json['selectedAnswer'] ?? '',
      correctAnswer: json['correctAnswer'] ?? '',
      isCorrect: json['isCorrect'] ?? false,
      order: json['order'] ?? 0,
      attemptStatus:
          json['attemptStatus'] == 'attempted' || json['attemptStatus'] == true,
      questionText: json['questionText'],
      questionDescription: json['questionDescription'],
      answerOptions: json['answerOptions'] != null
          ? List<String>.from(json['answerOptions'])
          : null,
      explanation: json['explanation'],
      questionType: json['questionType'],
      difficulty: json['difficulty'],
      questionData: json['questionData'],
    );
  }

  // CopyWith method for creating enhanced instances
  QuestionAttemptData copyWith({
    String? questionId,
    String? questionTitle,
    String? selectedAnswer,
    String? correctAnswer,
    bool? isCorrect,
    int? order,
    bool? attemptStatus,
    String? questionText,
    String? questionDescription,
    List<String>? answerOptions,
    String? explanation,
    String? questionType,
    String? difficulty,
    Map<String, dynamic>? questionData,
  }) {
    return QuestionAttemptData(
      questionId: questionId ?? this.questionId,
      questionTitle: questionTitle ?? this.questionTitle,
      selectedAnswer: selectedAnswer ?? this.selectedAnswer,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      isCorrect: isCorrect ?? this.isCorrect,
      order: order ?? this.order,
      attemptStatus: attemptStatus ?? this.attemptStatus,
      questionText: questionText ?? this.questionText,
      questionDescription: questionDescription ?? this.questionDescription,
      answerOptions: answerOptions ?? this.answerOptions,
      explanation: explanation ?? this.explanation,
      questionType: questionType ?? this.questionType,
      difficulty: difficulty ?? this.difficulty,
      questionData: questionData ?? this.questionData,
    );
  }
}
