import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';

class QuestionWithOrder extends Equatable {
  final String questionId;
  final int order;

  const QuestionWithOrder({
    required this.questionId,
    required this.order,
  });

  @override
  List<Object?> get props => [questionId, order];
}

class Test extends Equatable {
  final String testId;
  final String description;
  final String subject;
  final List<QuestionWithOrder> questionWithOrderList;

  const Test({
    required this.testId,
    required this.description,
    required this.subject,
    required this.questionWithOrderList,
  });

  @override
  List<Object?> get props => [testId];
}

class TestQuestionEntry extends Equatable {
  final String questionId;
  final QuestionAndAnswers questionData;
  final bool attempted;
  final String selectedAnswer;
  final int attemptCount;
  final DateTime lastAttempted;
  final int order;

  const TestQuestionEntry({
    required this.questionId,
    required this.questionData,
    required this.attempted,
    required this.selectedAnswer,
    required this.attemptCount,
    required this.lastAttempted,
    required this.order,
  });

  @override
  List<Object?> get props =>
      [questionId, attempted, selectedAnswer, attemptCount, lastAttempted];

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TestQuestionEntry && other.questionId == questionId;
  }

  @override
  int get hashCode => questionId.hashCode;

  factory TestQuestionEntry.emptyTestQuestionEntry() {
    return TestQuestionEntry(
      questionId: '',
      questionData: QuestionAndAnswers.emptyQuestion(),
      attempted: false,
      selectedAnswer: '',
      attemptCount: 0,
      lastAttempted: DateTime.now(),
      order: 0,
    );
  }
}

class SectionalOrDailyTestData extends Equatable {
  final String testId;
  final String description;
  final String subject;
  final Set<TestQuestionEntry> testQuestionEntrySet;
  final TestTypes testType;

  const SectionalOrDailyTestData({
    required this.testId,
    required this.description,
    required this.subject,
    required this.testQuestionEntrySet,
    required this.testType,
  });

  @override
  List<Object?> get props => [testId];

  @override
  String toString() {
    return 'TestWithQuestionsData { testId: $testId, description: $description, subject: $subject, questionsCount: ${testQuestionEntrySet.length}';
  }
}

class AnsweredQuestion extends Equatable {
  final TestTypes testType;
  final String testId;
  final String questionId;
  final String selectedAnswer;
  final bool isCorrect;

  const AnsweredQuestion({
    required this.testType,
    required this.testId,
    required this.questionId,
    required this.selectedAnswer,
    required this.isCorrect,
  });

  @override
  List<Object?> get props => [testId, questionId, selectedAnswer];
}

class FlaggedTestEntry extends Equatable {
  final String questionId;
  final int order;
  final bool isFlagged;

  const FlaggedTestEntry({
    required this.questionId,
    required this.order,
    required this.isFlagged,
  });

  @override
  List<Object?> get props => [questionId, order, isFlagged];

  @override
  String toString() {
    return 'FlaggedTestEntry { questionId: $questionId, order: $order, isFlagged: $isFlagged }';
  }
}

class TestAttemptEntry extends Equatable {
  final String testAttemptId;
  final TestTypes testType;
  final String testId;
  final String subject;
  final String description;
  final String testStatus;
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unattemptedQuestions;

  const TestAttemptEntry({
    required this.testAttemptId,
    required this.testType,
    required this.testId,
    required this.subject,
    required this.description,
    required this.testStatus,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.unattemptedQuestions,
  });

  @override
  List<Object?> get props => [
        testAttemptId,
      ];

  @override
  String toString() {
    return 'TestStatus { testAttemptId: $testAttemptId, testType: $testType, testId: $testId, subject: $subject, description: $description, testStatus: $testStatus, totalQuestions: $totalQuestions, correctAnswers: $correctAnswers, incorrectAnswers: $incorrectAnswers, unattemptedQuestions: $unattemptedQuestions }';
  }
}

class QuestionAttemptData extends Equatable {
  final String questionId;
  final String selectedAnswer;
  final bool attemptStatus;
  final bool isCorrect;

  //constructor
  const QuestionAttemptData({
    required this.questionId,
    required this.selectedAnswer,
    required this.attemptStatus,
    required this.isCorrect,
  });

  @override
  List<Object?> get props => [questionId];

  @override
  String toString() {
    return 'QuestionAttemptData { questionId: $questionId, selectedAnswer: $selectedAnswer, isCorrect: $isCorrect, attemptStatus: $attemptStatus }';
  }
}

class TestAttemptSummary extends Equatable {
  final TestAttemptEntry testAttemptStatus;
  final List<QuestionAttemptData> questionAttemptDataList;

  const TestAttemptSummary({
    required this.testAttemptStatus,
    required this.questionAttemptDataList,
  });

  @override
  List<Object?> get props => [
        testAttemptStatus.testAttemptId,
      ];

  @override
  String toString() {
    return 'TestAttemptSummary { testStatus: $testAttemptStatus, questionAttemptDataList: ${questionAttemptDataList.length} }';
  }
}

class TestProgressSummary extends Equatable {
  final String questionId;
  final String questionStatus; //attempted, not_attempted, current, flagged
  final int order;

  const TestProgressSummary({
    required this.questionId,
    required this.questionStatus,
    required this.order,
  });
  @override
  List<Object?> get props => [questionId, questionStatus, order];
  @override
  String toString() {
    return 'TestProgressSummary { questionId: $questionId, questionStatus: $questionStatus, order: $order }';
  }
}

class SectionalTestAttemptSummary extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final DateTime attemptDate;
  final String duration;
  final double scorePercentage;
  final TestStatus status;
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unattemptedQuestions;

  const SectionalTestAttemptSummary({
    required this.testAttemptId,
    required this.testId,
    required this.testName,
    required this.subject,
    required this.attemptDate,
    required this.duration,
    required this.scorePercentage,
    required this.status,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.unattemptedQuestions,
  });

  @override
  List<Object?> get props => [testAttemptId, testId, attemptDate];

  @override
  String toString() {
    return 'SectionalTestAttemptSummary { testAttemptId: $testAttemptId, testName: $testName, subject: $subject, attemptDate: $attemptDate, scorePercentage: $scorePercentage, status: $status }';
  }
}

class GroupedSectionalTestAttempts extends Equatable {
  final String testId;
  final String testName;
  final String subject;
  final SectionalTestAttemptSummary latestAttempt;
  final List<SectionalTestAttemptSummary> previousAttempts;
  final int totalAttempts;

  const GroupedSectionalTestAttempts({
    required this.testId,
    required this.testName,
    required this.subject,
    required this.latestAttempt,
    required this.previousAttempts,
    required this.totalAttempts,
  });

  @override
  List<Object?> get props => [testId, latestAttempt.attemptDate];

  @override
  String toString() {
    return 'GroupedSectionalTestAttempts { testId: $testId, testName: $testName, totalAttempts: $totalAttempts, latestAttempt: ${latestAttempt.attemptDate}, previousAttempts: $previousAttempts }';
  }
}

class QuestionModuleAnalysis extends Equatable {
  final String questionType; // This will be the module name
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unattemptedQuestions;
  final double accuracy; // Percentage of correct answers

  const QuestionModuleAnalysis({
    required this.questionType,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.unattemptedQuestions,
    required this.accuracy,
  });

  @override
  List<Object?> get props => [questionType];

  @override
  String toString() {
    return 'QuestionModuleAnalysis { questionType: $questionType, totalQuestions: $totalQuestions, accuracy: ${accuracy.toStringAsFixed(1)}% }';
  }
}

class TestAttemptAnalysis extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final List<QuestionModuleAnalysis> moduleAnalysis;
  final int totalQuestions;
  final int totalCorrectAnswers;
  final int totalIncorrectAnswers;
  final int totalUnattemptedQuestions;
  final double overallAccuracy;

  const TestAttemptAnalysis({
    required this.testAttemptId,
    required this.testId,
    required this.testName,
    required this.subject,
    required this.moduleAnalysis,
    required this.totalQuestions,
    required this.totalCorrectAnswers,
    required this.totalIncorrectAnswers,
    required this.totalUnattemptedQuestions,
    required this.overallAccuracy,
  });

  @override
  List<Object?> get props => [testAttemptId];

  @override
  String toString() {
    return 'TestAttemptAnalysis { testAttemptId: $testAttemptId, testName: $testName, moduleAnalysis: ${moduleAnalysis.length} modules, overallAccuracy: ${overallAccuracy.toStringAsFixed(1)}% }';
  }
}

class TestResultSummary extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final double totalPercentage;
  final String timeSpent; // Format: "48 min, 56s"
  final int correctAnswers;
  final int incorrectAnswers;
  final int unansweredAnswers;
  final int totalQuestions;
  final DateTime? startTime;
  final DateTime? endTime;

  const TestResultSummary({
    required this.testAttemptId,
    required this.testId,
    required this.testName,
    required this.subject,
    required this.totalPercentage,
    required this.timeSpent,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.unansweredAnswers,
    required this.totalQuestions,
    this.startTime,
    this.endTime,
  });

  @override
  List<Object?> get props => [testAttemptId];

  @override
  String toString() {
    return 'TestResultSummary { testAttemptId: $testAttemptId, testName: $testName, totalPercentage: ${totalPercentage.toStringAsFixed(1)}%, timeSpent: $timeSpent }';
  }
}

enum QuestionAnswerStatus {
  correct,
  incorrect,
  unanswered,
}

class QuestionWithAnswerStatus extends Equatable {
  final String questionId;
  final int questionOrder;
  final String shortDescription;
  final String selectedAnswer;
  final String correctAnswer;
  final QuestionAnswerStatus answerStatus;
  final String module;
  final QuestionAndAnswers questionAndAnswers;

  const QuestionWithAnswerStatus({
    required this.questionId,
    required this.questionOrder,
    required this.shortDescription,
    required this.selectedAnswer,
    required this.correctAnswer,
    required this.answerStatus,
    required this.module,
    required this.questionAndAnswers,
  });

  @override
  List<Object?> get props => [questionId];

  @override
  String toString() {
    return 'QuestionWithAnswerStatus { questionId: $questionId, order: $questionOrder, status: $answerStatus, shortDescription: $shortDescription }';
  }
}

class TestQuestionsWithAnswerStatus extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final List<QuestionWithAnswerStatus> questions;
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unansweredQuestions;

  const TestQuestionsWithAnswerStatus({
    required this.testAttemptId,
    required this.testId,
    required this.testName,
    required this.subject,
    required this.questions,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.unansweredQuestions,
  });

  @override
  List<Object?> get props => [testAttemptId];

  @override
  String toString() {
    return 'TestQuestionsWithAnswerStatus { testAttemptId: $testAttemptId, testName: $testName, totalQuestions: $totalQuestions }';
  }
}
