import 'package:equatable/equatable.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';

class TestConfig extends Equatable {
  final TestTypes type;
  final String subject;
  final TestConfigParameters parameters;

  const TestConfig({
    required this.type,
    required this.subject,
    required this.parameters,
  });

  //create empty TestConfig object
  factory TestConfig.empty() {
    return const TestConfig(
      type: TestTypes.notAvailable,
      subject: '',
      parameters: TestConfigParameters(maxAllowedTime: 0),
    );
  }

  //isEmpty method to check if the object is empty
  bool get isEmpty => this == TestConfig.empty();

  @override
  List<Object?> get props => [type, subject];
}

class TestConfigParameters extends Equatable {
  final int maxAllowedTime;

  const TestConfigParameters({
    required this.maxAllowedTime,
  });

  @override
  List<Object?> get props => [maxAllowedTime];
}
