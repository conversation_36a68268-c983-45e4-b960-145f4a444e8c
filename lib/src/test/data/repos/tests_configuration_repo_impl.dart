import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/test/data/datasources/tests_config_remote_datasource.dart';
import 'package:skillapp/src/test/domain/entitites/test_config.dart';
import 'package:skillapp/src/test/domain/repos/tests_configuration_repo.dart';

class TestsConfigurationRepoImpl implements TestsConfigurationRepo {
  final TestsConfigRemoteDatasource _testsRemoteDatasource;

  TestsConfigurationRepoImpl({
    required TestsConfigRemoteDatasource remoteDatasource,
  }) : _testsRemoteDatasource = remoteDatasource;

  @override
  ResultFuture<List<TestConfig>> fetchTestsConfugration() async {
    try {
      final result = await _testsRemoteDatasource.fetchTestsConfugration();

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }
}
