import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/domain/repos/question_repo.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/test/data/datasources/tests_data_remote_datasource.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';

class TestsDataRepoImpl implements TestsDataRepo {
  final TestsDataRemoteDatasource _testsDataRemoteDatasource;
  final QuestionRepo _questionRepo;
  final CacheContext _cacheContext;

  TestsDataRepoImpl({
    required TestsDataRemoteDatasource testsDataRemoteDatasource,
    required QuestionRepo questionRepo,
    required CacheContext cacheContext,
  })  : _testsDataRemoteDatasource = testsDataRemoteDatasource,
        _questionRepo = questionRepo,
        _cacheContext = cacheContext;

  @override
  ResultFuture<SectionalOrDailyTestData> fetchTestsData(String testId) async {
    try {
      final testWithQuestionIds =
          await _testsDataRemoteDatasource.fetchTestsData(testId);

      final questionsAnswerData = await Future.wait(testWithQuestionIds
          .questionWithOrderList
          .map((questionWithOrder) async {
        return await _questionRepo.fetchSingleQuestionData(
            questionWithOrder.questionId, testId, testWithQuestionIds.subject);
      }));

      List<QuestionAndAnswers> questionAndAnswersList = [];
      List<String> failedQuestions = [];

      for (var result in questionsAnswerData) {
        if (result.isRight()) {
          questionAndAnswersList.add(result
              .getOrElse(() => throw Exception('Error in mapping question')));
        } else {
          // Log failed questions for debugging
          result.fold(
            (failure) {
              print("Failed to fetch question: ${failure.errorMessage}");
              failedQuestions.add(failure.errorMessage);
            },
            (success) {},
          );
        }
      }

      print(
          "Successfully fetched ${questionAndAnswersList.length} questions out of ${questionsAnswerData.length}");
      if (failedQuestions.isNotEmpty) {
        print("Failed questions: $failedQuestions");
      }

      List<TestQuestionEntry> testQuestionEntryList = [];

      for (var element in questionAndAnswersList) {
        testQuestionEntryList.add(
          TestQuestionEntry(
            questionId: element.id,
            questionData: element,
            attempted: false,
            selectedAnswer: '',
            attemptCount: 0,
            lastAttempted: DateTime.now(),
            order: _getOrderForQuestionId(
                testWithQuestionIds.questionWithOrderList, element.id),
          ),
        );
      }

      testQuestionEntryList.sort((a, b) => a.order.compareTo(b.order));

      return Right(SectionalOrDailyTestData(
        testId: testWithQuestionIds.testId,
        description: testWithQuestionIds.description,
        testQuestionEntrySet: testQuestionEntryList.toSet(),
        subject: testWithQuestionIds.subject,
        testType: TestTypes.notAvailable,
      ));
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<Test> fetchNewSectionalTest(String subject) async {
    try {
      print(
          "JW: Repository - calling remote datasource with level: ${_cacheContext.level}, subject: $subject");
      final Test newTest =
          await _testsDataRemoteDatasource.fetchNewSectionalTest(
              _cacheContext.level, subject, _cacheContext.profileId);

      print("JW: Repository - successfully fetched test: ${newTest.testId}");
      return Right(newTest);
    } on ServerException catch (e, s) {
      print("JW: Repository - ServerException caught: ${e.message}");
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    } catch (e, s) {
      print("JW: Repository - General exception caught: $e");
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure(
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  ResultFuture<void> markTestAsStarted(String testAttemptId, String testId,
      int numberOfQuestions, TestTypes type) async {
    try {
      await _testsDataRemoteDatasource.markTestAsStarted(
          _cacheContext.profileId,
          testAttemptId,
          testId,
          numberOfQuestions,
          type);
      return const Right(null);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  _getOrderForQuestionId(
      List<QuestionWithOrder> questionWithOrderList, String id) {
    return questionWithOrderList
        .firstWhere((element) => element.questionId == id)
        .order;
  }

  @override
  ResultFuture<void> answerAQuestion(AnsweredQuestion answeredQuestion,
      String testAttemptId, TestTypes type) async {
    try {
      await _testsDataRemoteDatasource.answerAQuestion(
          _cacheContext.profileId, answeredQuestion, testAttemptId, type);
      return const Right(null);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<bool> submitTest(String testAttemptId, TestTypes type) async {
    try {
      await _testsDataRemoteDatasource.submitTest(
          _cacheContext.profileId, testAttemptId, type);
      return const Right(true);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<TestAttemptEntry> fetchTestAttemptStatus(
      String testAttemptId, TestTypes type) async {
    try {
      var result = await _testsDataRemoteDatasource.fetchTestAttemptStatus(
        _cacheContext.profileId,
        testAttemptId,
        type,
      );
      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<List<QuestionAttemptData>> fetchTestAttemptDetails(
      String testAttemptId, TestTypes type) async {
    try {
      List<QuestionAttemptData> result =
          await _testsDataRemoteDatasource.fetchAttemptedQuestionsForATest(
              _cacheContext.profileId, testAttemptId, type);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<List<SectionalTestAttemptSummary>> fetchTestAttemptHistory(
      TestTypes type) async {
    try {
      List<SectionalTestAttemptSummary> result =
          await _testsDataRemoteDatasource.fetchSectionalTestAttemptHistory(
              _cacheContext.profileId, type);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<List<GroupedSectionalTestAttempts>>
      fetchGroupedTestAttemptHistory(TestTypes type) async {
    try {
      List<GroupedSectionalTestAttempts> result =
          await _testsDataRemoteDatasource
              .fetchGroupedSectionalTestAttemptHistory(
                  _cacheContext.profileId, type);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<TestAttemptAnalysis> fetchTestAttemptAnalysis(
      String testAttemptId, TestTypes testType) async {
    try {
      TestAttemptAnalysis result =
          await _testsDataRemoteDatasource.fetchTestAttemptAnalysis(
              _cacheContext.profileId, testAttemptId, testType);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<TestResultSummary> fetchTestResultSummary(
      String testAttemptId, TestTypes type) async {
    try {
      TestResultSummary result = await _testsDataRemoteDatasource
          .fetchTestResultSummary(_cacheContext.profileId, testAttemptId, type);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<TestQuestionsWithAnswerStatus>
      fetchTestQuestionsWithAnswerStatus(
          String testAttemptId, TestTypes type) async {
    try {
      TestQuestionsWithAnswerStatus result =
          await _testsDataRemoteDatasource.fetchTestQuestionsWithAnswerStatus(
              _cacheContext.profileId, testAttemptId, type);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<Test> fetchTodaysDailyTest() async {
    try {
      print(
          "JB: Repository - calling remote datasource with level: ${_cacheContext.level}");
      Test result = await _testsDataRemoteDatasource
          .fetchTodaysDailyTest(_cacheContext.level);

      print("JB: Repository - successfully fetched test: ${result.testId}");
      return Right(result);
    } on ServerException catch (e, s) {
      print("JB: Repository - ServerException caught: ${e.message}");
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    } catch (e, s) {
      print("JB: Repository - General exception caught: $e");
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure(
        message: e.toString(),
        statusCode: 500,
      ));
    }
  }

  @override
  ResultFuture<bool> isTodaysDailyTestAttempted() async {
    try {
      bool result = await _testsDataRemoteDatasource.isTodaysDailyTestAttempted(
          _cacheContext.profileId, _cacheContext.level);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }
}
