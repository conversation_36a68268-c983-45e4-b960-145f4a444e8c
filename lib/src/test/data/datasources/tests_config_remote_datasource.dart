import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test_config.dart';

abstract class TestsConfigRemoteDatasource {
  Future<List<TestConfig>> fetchTestsConfugration();
}

class TestsConfigRemoteDatasourceImpl implements TestsConfigRemoteDatasource {
  final FirebaseFirestore _firestore;

  TestsConfigRemoteDatasourceImpl({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<List<TestConfig>> fetchTestsConfugration() async {
    List<TestConfig> testConfigList = [];
    try {
      QuerySnapshot testConfigSnapshot =
          await _firestore.collection('tests_config/sectional/subjects').get();

      List<TestConfig> testConfigSectionalList =
          testConfigSnapshot.docs.map((config) {
        return TestConfig(
          type: TestTypes.sectional,
          subject: config.id,
          parameters: TestConfigParameters(
            maxAllowedTime: config['max_allowed_time'],
          ),
        );
      }).toList();

      testConfigList.addAll(testConfigSectionalList);
      testConfigSnapshot = await _firestore
          .collection('tests_config/full_mocktest/subjects')
          .get();

      List<TestConfig> testConfigFullMocktestList =
          testConfigSnapshot.docs.map((config) {
        return TestConfig(
          type: TestTypes.fullMocktest,
          subject: config.id,
          parameters: TestConfigParameters(
            maxAllowedTime: config['max_allowed_time'],
          ),
        );
      }).toList();

      testConfigList.addAll(testConfigFullMocktestList);

      testConfigSnapshot =
          await _firestore.collection('tests_config/daily/subjects').get();

      List<TestConfig> testConfigDailytestList =
          testConfigSnapshot.docs.map((config) {
        return TestConfig(
          type: TestTypes.daily,
          subject: config.id,
          parameters: TestConfigParameters(
            maxAllowedTime: config['max_allowed_time'],
          ),
        );
      }).toList();

      testConfigList.addAll(testConfigDailytestList);

      return testConfigList;
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching test configuration: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching test configuration: $e',
      );
    }
  }
}
