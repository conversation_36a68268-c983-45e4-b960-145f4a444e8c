import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/data/models/question.dart';
import 'package:skillapp/core/common/features/questions/data/repos/util/question_repo_util.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';

abstract class TestsDataRemoteDatasource {
  Future<Test> fetchTestsData(String testId);

  Future<Test> fetchNewSectionalTest(
      int level, String subject, String profileId);

  Future<void> markTestAsStarted(String profileId, String testAttemptId,
      String testId, int numberOfQuestions, TestTypes type);

  Future<void> answerAQuestion(String profileId,
      AnsweredQuestion answeredQuestion, String testAttemptId, TestTypes type);

  Future<void> submitTest(
      String profileId, String testAttemptId, TestTypes type);

  Future<TestAttemptEntry> fetchTestAttemptStatus(
      String profileId, String testAttemptId, TestTypes type);

  Future<List<QuestionAttemptData>> fetchAttemptedQuestionsForATest(
      String profileId, String testAttemptId, TestTypes type);

  Future<List<SectionalTestAttemptSummary>> fetchSectionalTestAttemptHistory(
      String profileId, TestTypes type);

  Future<List<GroupedSectionalTestAttempts>>
      fetchGroupedSectionalTestAttemptHistory(String profileId, TestTypes type);

  Future<TestAttemptAnalysis> fetchTestAttemptAnalysis(
      String profileId, String testAttemptId, TestTypes testType);

  Future<TestResultSummary> fetchTestResultSummary(
      String profileId, String testAttemptId, TestTypes type);

  Future<TestQuestionsWithAnswerStatus> fetchTestQuestionsWithAnswerStatus(
      String profileId, String testAttemptId, TestTypes type);

  Future<Test> fetchTodaysDailyTest(int userLevel);

  Future<bool> isTodaysDailyTestAttempted(String profileId, int userLevel);
}

class TestsDataRemoteDatasourceImpl implements TestsDataRemoteDatasource {
  final FirebaseFirestore _firestore;

  TestsDataRemoteDatasourceImpl({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<Test> fetchTestsData(String testId) async {
    try {
      print("fetchTestsData: testId = $testId");

//      testId = 'MATHS_$testId';

      DocumentSnapshot testSnapshot =
          await _firestore.collection('tests_master').doc(testId).get();
      if (!testSnapshot.exists) {
        throw Exception('Test with id $testId not found');
      }

      QuerySnapshot testQuestionSnapshot = await _firestore
          .collection('tests_master/$testId/question_ids')
          .get();

      print("fetchTestsData: testQuestionSnapshot = $testQuestionSnapshot");

      List<QuestionWithOrder> questionWithOrderList =
          testQuestionSnapshot.docs.map((config) {
        return QuestionWithOrder(
            questionId: config.id, order: config['order'] ?? 0);
      }).toList();

      print("fetchTestsData: questionWithOrderList = $questionWithOrderList");

      return Test(
          testId: testId,
          description: testSnapshot['name'],
          questionWithOrderList: questionWithOrderList,
          subject: testSnapshot['subject']);
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching test questions: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching test questions: $e',
      );
    }
  }

  // Helper method to fetch attempted sectional test IDs for a user
  Future<List<String>> _fetchAttemptedSectionalTestIds(
      String profileId, TestTypes testType) async {
    try {
      QuerySnapshot attemptsSnapshot = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${testType.name}/test_attempts')
          .get();

      if (attemptsSnapshot.docs.isEmpty) {
        return [];
      }

      // Extract test_id from each attempt
      List<String> attemptedTestIds = [];
      for (var doc in attemptsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data.containsKey('test_id')) {
          attemptedTestIds.add(data['test_id']);
        }
      }
      return attemptedTestIds;
    } catch (e) {
      print('Error fetching attempted sectional test ids: $e');
      return [];
    }
  }

  @override
  Future<Test> fetchNewSectionalTest(
      int level, String subject, String profileId) async {
    try {
      TestTypes testType = TestTypes.sectional;

      QuerySnapshot testIdsSnapshot = await _firestore
          .collection(
              'tests_mapping/sectional/levels/$level/subjects/$subject/test_ids')
          .get();

      if (testIdsSnapshot.docs.isEmpty) {
        throw Exception('No tests found for level $level and subject $subject');
      }

      // Fetch attempted test ids
      List<String> attemptedTestIds =
          await _fetchAttemptedSectionalTestIds(profileId, testType);

      // Filter out attempted tests
      var unattemptedDocs = testIdsSnapshot.docs
          .where((doc) => !attemptedTestIds.contains(doc.id))
          .toList();

      if (unattemptedDocs.isEmpty) {
        throw const ServerException(
            message: "All tests have been attempted for this section.",
            statusCode: '500');
        //throw Exception('All tests have been attempted for this section.');
      }

      // Pick the first unattempted test
      var firstUnattempted = unattemptedDocs.first;
      String testId = firstUnattempted.id;
      String name = firstUnattempted['name'] ?? testId;

      return Test(
          testId: testId,
          description: name,
          questionWithOrderList: const [],
          subject: subject);
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching next sectional question: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching next sectional question: $e',
      );
    }
  }

  @override
  Future<void> markTestAsStarted(String profileId, String testAttemptId,
      String testId, int numberOfQuestions, TestTypes testType) async {
    try {
      DocumentReference profileDocRef =
          _firestore.collection('tests_user_attempts').doc(profileId);

      // Check if profile document exists
      DocumentSnapshot profileSnapshot = await profileDocRef.get();

      if (!profileSnapshot.exists) {
        await profileDocRef.set({'created_at': DateTime.now()});
      }

      DocumentReference testDocRef = _firestore
          .collection('tests_user_attempts/$profileId/test_types')
          .doc(testType.name);

      // Check if sectional document exists
      DocumentSnapshot sectionalSnapshot = await testDocRef.get();

      if (!sectionalSnapshot.exists) {
        await testDocRef.set({'created_at': DateTime.now()});
      }
      var time = DateTime.now();
      return _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${testType.name}/test_attempts')
          .doc(testAttemptId)
          .set({
        'test_id': testId,
        'last_updated_time': time,
        'start_time': time,
        'status': 'started',
        'total_questions': numberOfQuestions,
      });
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching next sectional question: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching next sectional question: $e',
      );
    }
  }

  @override
  Future<void> answerAQuestion(
      String profileId,
      AnsweredQuestion answeredQuestion,
      String testAttemptId,
      TestTypes testType) async {
    try {
      return await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${testType.name}/test_attempts/$testAttemptId/question_ids')
          .doc(answeredQuestion.questionId)
          .set({
        'attempt_count': 1, //To be upodated later
        'selected_answer': answeredQuestion.selectedAnswer,
        'last_attempted': DateTime.now(),
        'is_correct': answeredQuestion.isCorrect,
      });
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching next sectional question: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching next sectional question: $e',
      );
    }
  }

  @override
  Future<void> submitTest(
      String profileId, String testAttemptId, TestTypes testType) async {
    try {
      //calculate the score

      //iterate over the list and get the question id count for isCorrect true and false.
      var questionWithStatusList = await fetchAttemptedQuestionsForATest(
          profileId, testAttemptId, testType);
      int correctCount = 0;
      int incorrectCount = 0;
      for (var element in questionWithStatusList) {
        if (element.isCorrect) {
          correctCount++;
        } else {
          incorrectCount++;
        }
      }

      return await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${testType.name}/test_attempts')
          .doc(testAttemptId)
          .update({
        'last_updated_time': DateTime.now(),
        'end_time': DateTime.now(),
        'status': 'completed',
        'correct_count': correctCount,
        'incorrect_count': incorrectCount,
      });
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fsubmitSectionalTest: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error submitSectionalTest: $e',
      );
    }
  }

  @override
  Future<List<QuestionAttemptData>> fetchAttemptedQuestionsForATest(
      String profileId, String testAttemptId, TestTypes type) async {
    try {
      // First, get the test attempt status to extract testId
      DocumentSnapshot testAttemptSnapshot = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${type.name}/test_attempts')
          .doc(testAttemptId)
          .get();

      if (!testAttemptSnapshot.exists) {
        throw Exception(
            'Test attempt not found for testAttemptId $testAttemptId');
      }

      String testId = testAttemptSnapshot['test_id'];
      String subject = _extractSubjectFromTestId(testId);

      // Get question attempt data
      QuerySnapshot testIdsSnapshot = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${type.name}/test_attempts/$testAttemptId/question_ids')
          .get();

      if (testIdsSnapshot.docs.isEmpty) {
        throw Exception(
            'No questions found for testAttemptId $testAttemptId and profileId $profileId');
      }

      // Create list to store enhanced question attempt data
      List<QuestionAttemptData> questionAttempts = [];

      // Iterate through each question attempt and fetch question content
      for (var doc in testIdsSnapshot.docs) {
        String questionId = doc.id;

        // Create basic question attempt data
        QuestionAttemptData basicAttemptData = QuestionAttemptData(
          questionId: questionId,
          //  questionTitle: 'Question $questionId',
          selectedAnswer: doc['selected_answer'] ?? '',
          // correctAnswer: '', // Will be populated from question content
          isCorrect: doc['is_correct'] ?? false,
          // order: 0, // Will be populated if available
          attemptStatus: true,
        );

        questionAttempts.add(basicAttemptData);

        // Fetch question content from subjects/{subject}/questions/{questionId}
      }

      return questionAttempts;
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching attempted question for test: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching attempted questions for a test: $e',
      );
    }
  }

  // Helper method to extract subject from testId
  String _extractSubjectFromTestId(String testId) {
    try {
      // Extract subject from testId like 'MATHS_T001' -> 'maths'
      return testId.split('_')[0].toLowerCase();
    } catch (e) {
      print('Error extracting subject from testId: $testId - $e');
      return 'unknown'; // Fallback subject
    }
  }

  @override
  Future<TestAttemptEntry> fetchTestAttemptStatus(
      String profileId, String testAttemptId, TestTypes type) async {
    try {
      DocumentSnapshot testSnapshot = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${type.name}/test_attempts')
          .doc(testAttemptId)
          .get();
      if (!testSnapshot.exists) {
        throw Exception('Test with id $testAttemptId not found');
      }

      print("testSnapshot = $testSnapshot");
      //populate TestAttemptStatus from testSnapshot
      return TestAttemptEntry(
        testAttemptId: testAttemptId,
        testType: type,
        testId: testSnapshot['test_id'],
        subject: 'NA',
        description: 'NA',
        testStatus: testSnapshot['status'],
        totalQuestions: testSnapshot['total_questions'] ?? 0,
        correctAnswers: testSnapshot['correct_count'] ?? 0,
        incorrectAnswers: testSnapshot['incorrect_count'] ?? 0,
        unattemptedQuestions: (testSnapshot['total_questions'] ?? 0) -
            (testSnapshot['correct_count'] ?? 0) -
            (testSnapshot['incorrect_count'] ?? 0),
      );
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching fetchTestAttemptStatus : ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching fetchTestAttemptStatus : $e',
      );
    }
  }

  @override
  Future<List<SectionalTestAttemptSummary>> fetchSectionalTestAttemptHistory(
      String profileId, TestTypes type) async {
    try {
      QuerySnapshot testAttemptsSnapshot = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${type.name}/test_attempts')
          .orderBy('start_time', descending: true)
          .get();

      if (testAttemptsSnapshot.docs.isEmpty) {
        return [];
      }

      List<SectionalTestAttemptSummary> attemptSummaries = [];

      for (var doc in testAttemptsSnapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

        // Get test details from tests_master collection
        String testId = data['test_id'] ?? '';
        DocumentSnapshot testSnapshot =
            await _firestore.collection('tests_master').doc(testId).get();

        String testName = 'Unknown Test';
        String subject = 'Unknown';

        if (testSnapshot.exists) {
          Map<String, dynamic> testData =
              testSnapshot.data() as Map<String, dynamic>;
          testName = testData['name'] ?? 'Unknown Test';
          subject = testData['subject'] ?? 'Unknown';
        }

        // Calculate duration
        Timestamp? startTime = data['start_time'] as Timestamp?;
        Timestamp? endTime = data['end_time'] as Timestamp?;

        print(
            "Start Time is-->${startTime?.toDate().hour} ${startTime?.toDate().minute}");
        print(
            "End Time is --> ${endTime?.toDate().hour} ${endTime?.toDate().minute}");

        String duration = '0:00';

        if (startTime != null && endTime != null) {
          Duration difference = endTime.toDate().difference(startTime.toDate());
          int hours = difference.inHours;
          int minutes = difference.inMinutes % 60;
          int seconds = difference.inSeconds % 60;

          if (hours > 0) {
            duration =
                '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
          } else {
            duration =
                '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
          }
        }

        print("Duration is --> $duration");
        // Calculate score percentage
        int totalQuestions = data['total_questions'] ?? 0;
        int correctAnswers = data['correct_count'] ?? 0;
        double scorePercentage =
            totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0.0;

        // Determine status
        String statusString = data['status'] ?? 'unknown';
        TestStatus status = TestStatus.none;

        switch (statusString.toLowerCase()) {
          case 'completed':
            status = scorePercentage >= 50
                ? TestStatus.completed
                : TestStatus.completed;
            break;
          case 'inprogress':
            status = TestStatus.inProgress;
            break;
          case 'started':
            status = TestStatus.started;
            break;
          default:
            status = TestStatus.none;
        }

        int incorrectAnswers = (data['incorrect_count'] ?? 0) as int;

        attemptSummaries.add(SectionalTestAttemptSummary(
          testAttemptId: doc.id,
          testId: testId,
          testName: testName,
          subject: subject,
          attemptDate: startTime?.toDate() ?? DateTime.now(),
          duration: duration,
          scorePercentage: scorePercentage,
          status: status,
          totalQuestions: totalQuestions,
          correctAnswers: correctAnswers,
          incorrectAnswers: incorrectAnswers,
          unattemptedQuestions:
              totalQuestions - correctAnswers - incorrectAnswers,
        ));
      }

      return attemptSummaries;
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching sectional test attempt history: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching sectional test attempt history: $e',
      );
    }
  }

  @override
  Future<List<GroupedSectionalTestAttempts>>
      fetchGroupedSectionalTestAttemptHistory(
          String profileId, TestTypes type) async {
    try {
      // First, get all test attempts (reuse existing method)
      List<SectionalTestAttemptSummary> allAttempts =
          await fetchSectionalTestAttemptHistory(profileId, type);

      if (allAttempts.isEmpty) {
        return [];
      }

      // Group attempts by testId
      Map<String, List<SectionalTestAttemptSummary>> groupedAttempts = {};

      for (var attempt in allAttempts) {
        if (groupedAttempts.containsKey(attempt.testId)) {
          groupedAttempts[attempt.testId]!.add(attempt);
        } else {
          groupedAttempts[attempt.testId] = [attempt];
        }
      }

      // Create grouped test attempts
      List<GroupedSectionalTestAttempts> groupedResults = [];

      groupedAttempts.forEach((testId, attempts) {
        // Sort attempts by date (most recent first)
        attempts.sort((a, b) => b.attemptDate.compareTo(a.attemptDate));

        // Latest attempt is the first one after sorting
        SectionalTestAttemptSummary latestAttempt = attempts.first;

        // Previous attempts are all except the first one
        List<SectionalTestAttemptSummary> previousAttempts =
            attempts.length > 1 ? attempts.sublist(1) : [];

        groupedResults.add(GroupedSectionalTestAttempts(
          testId: testId,
          testName: latestAttempt.testName,
          subject: latestAttempt.subject,
          latestAttempt: latestAttempt,
          previousAttempts: previousAttempts,
          totalAttempts: attempts.length,
        ));
      });

      // Sort grouped results by latest attempt date (most recent first)
      groupedResults.sort((a, b) =>
          b.latestAttempt.attemptDate.compareTo(a.latestAttempt.attemptDate));

      return groupedResults;
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching grouped sectional test attempt history: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching grouped sectional test attempt history: $e',
      );
    }
  }

  @override
  Future<TestAttemptAnalysis> fetchTestAttemptAnalysis(
      String profileId, String testAttemptId, TestTypes testType) async {
    try {
      // First, get the test attempt status to get basic test info
      TestAttemptEntry testAttemptStatus =
          await fetchTestAttemptStatus(profileId, testAttemptId, testType);
      print("testAttemptStatus = $testAttemptStatus");

      // Get all question attempt data for this test
      List<QuestionAttemptData> questionAttemptDataList =
          await fetchAttemptedQuestionsForATest(
              profileId, testAttemptId, testType);
      print("questionAttemptDataList = $questionAttemptDataList");

      // Get the test data to access question details with modules
      Test testData = await fetchTestsData(testAttemptStatus.testId);
      print("testData = $testData");

      // Fetch question details for all questions to get module information
      Map<String, String> questionModuleMap = {};

      for (var questionWithOrder in testData.questionWithOrderList) {
        try {
          print("questionWithOrder = $questionWithOrder");
          // Fetch question details from the questions collection
          DocumentSnapshot<Map<String, dynamic>> questionDoc = await _firestore
              .collection('subjects/${testData.subject}/questions')
              .doc(questionWithOrder.questionId)
              .get();
          print("questionDoc = $questionDoc");
          if (questionDoc.exists && questionDoc.data() != null) {
            print("questionDoc.data() = ${questionDoc.data()}");
            // The module field is inside the 'data' JSON field, not at document root
            String dataJson = questionDoc.data()!['data'] ?? '';
            if (dataJson.isNotEmpty) {
              try {
                Map<String, dynamic> dataMap = json.decode(dataJson);
                String module = dataMap['module'] ?? 'Unknown';
                print("module = $module");
                questionModuleMap[questionWithOrder.questionId] = module;
              } catch (e) {
                // If JSON parsing fails, mark as Unknown
                print(
                    "JSON parsing failed for question ${questionWithOrder.questionId}: $e");
                questionModuleMap[questionWithOrder.questionId] = 'Unknown';
              }
            } else {
              print(
                  "No data field found for question ${questionWithOrder.questionId}");
              questionModuleMap[questionWithOrder.questionId] = 'Unknown';
            }
          } else {
            questionModuleMap[questionWithOrder.questionId] = 'Unknown';
            print("questionModuleMap = $questionModuleMap");
          }
        } catch (e) {
          // If we can't fetch a specific question, mark it as Unknown
          questionModuleMap[questionWithOrder.questionId] = 'Unknown';
          print("questionModuleMap = $questionModuleMap");
          print("Error fetching question details: $e");
        }
      }

      // Group questions by module and calculate statistics
      Map<String, List<QuestionAttemptData>> moduleQuestions = {};

      // Initialize all modules from the test
      for (var entry in questionModuleMap.entries) {
        String module = entry.value;
        if (!moduleQuestions.containsKey(module)) {
          moduleQuestions[module] = [];
        }
      }

      // Add attempted questions to their respective modules
      for (var questionAttempt in questionAttemptDataList) {
        String module =
            questionModuleMap[questionAttempt.questionId] ?? 'Unknown';
        if (!moduleQuestions.containsKey(module)) {
          moduleQuestions[module] = [];
        }
        moduleQuestions[module]!.add(questionAttempt);
      }

      // Add unattempted questions to their respective modules
      for (var questionWithOrder in testData.questionWithOrderList) {
        bool isAttempted = questionAttemptDataList.any(
            (attempt) => attempt.questionId == questionWithOrder.questionId);

        if (!isAttempted) {
          String module =
              questionModuleMap[questionWithOrder.questionId] ?? 'Unknown';
          if (!moduleQuestions.containsKey(module)) {
            moduleQuestions[module] = [];
          }
          // Add a placeholder for unattempted question
          moduleQuestions[module]!.add(QuestionAttemptData(
            questionId: questionWithOrder.questionId,
            selectedAnswer: '',
            attemptStatus: false,
            isCorrect: false,
          ));
        }
      }

      // Calculate analysis for each module
      List<QuestionModuleAnalysis> moduleAnalysisList = [];

      moduleQuestions.forEach((module, questions) {
        int totalQuestions = questions.length;
        int correctAnswers =
            questions.where((q) => q.isCorrect && q.attemptStatus).length;
        int incorrectAnswers =
            questions.where((q) => !q.isCorrect && q.attemptStatus).length;
        int unattemptedQuestions =
            questions.where((q) => !q.attemptStatus).length;

        double accuracy =
            totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0.0;

        moduleAnalysisList.add(QuestionModuleAnalysis(
          questionType: module,
          totalQuestions: totalQuestions,
          correctAnswers: correctAnswers,
          incorrectAnswers: incorrectAnswers,
          unattemptedQuestions: unattemptedQuestions,
          accuracy: accuracy,
        ));
      });

      // Sort modules by name for consistent display
      moduleAnalysisList
          .sort((a, b) => a.questionType.compareTo(b.questionType));

      // Calculate overall statistics
      double overallAccuracy = testAttemptStatus.totalQuestions > 0
          ? (testAttemptStatus.correctAnswers /
                  testAttemptStatus.totalQuestions) *
              100
          : 0.0;

      return TestAttemptAnalysis(
        testAttemptId: testAttemptId,
        testId: testAttemptStatus.testId,
        testName: testAttemptStatus.description,
        subject: testAttemptStatus.subject,
        moduleAnalysis: moduleAnalysisList,
        totalQuestions: testAttemptStatus.totalQuestions,
        totalCorrectAnswers: testAttemptStatus.correctAnswers,
        totalIncorrectAnswers: testAttemptStatus.incorrectAnswers,
        totalUnattemptedQuestions: testAttemptStatus.unattemptedQuestions,
        overallAccuracy: overallAccuracy,
      );
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching test attempt analysis: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching test attempt analysis: $e',
      );
    }
  }

  @override
  Future<TestResultSummary> fetchTestResultSummary(
      String profileId, String testAttemptId, TestTypes type) async {
    try {
      // Get the test attempt status to get basic test info
      TestAttemptEntry testAttemptStatus =
          await fetchTestAttemptStatus(profileId, testAttemptId, type);

      // Get test details from tests_master collection
      Test testData = await fetchTestsData(testAttemptStatus.testId);

      // Get all question attempt data for this test
      List<QuestionAttemptData> questionAttemptDataList =
          await fetchAttemptedQuestionsForATest(profileId, testAttemptId, type);

      // Get test attempt document to extract timing information
      DocumentSnapshot testAttemptDoc = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/${type.name}/test_attempts')
          .doc(testAttemptId)
          .get();

      // Calculate time spent
      String timeSpent = '0 min, 0s';
      DateTime? startTime;
      DateTime? endTime;

      if (testAttemptDoc.exists) {
        Map<String, dynamic> data =
            testAttemptDoc.data() as Map<String, dynamic>;

        Timestamp? startTimestamp = data['start_time'] as Timestamp?;
        Timestamp? endTimestamp = data['end_time'] as Timestamp?;

        if (startTimestamp != null) {
          startTime = startTimestamp.toDate();
        }

        if (endTimestamp != null) {
          endTime = endTimestamp.toDate();
        }

        if (startTime != null && endTime != null) {
          Duration duration = endTime.difference(startTime);
          int totalMinutes = duration.inMinutes;
          int seconds = duration.inSeconds % 60;
          timeSpent = '$totalMinutes min, ${seconds}s';
        }
      }

      // Calculate statistics
      int totalQuestions = testData.questionWithOrderList.length;
      int correctAnswers =
          questionAttemptDataList.where((q) => q.isCorrect).length;
      int incorrectAnswers =
          questionAttemptDataList.where((q) => !q.isCorrect).length;
      int unansweredAnswers = totalQuestions - questionAttemptDataList.length;

      // Calculate total percentage
      double totalPercentage =
          totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0.0;

      return TestResultSummary(
        testAttemptId: testAttemptId,
        testId: testAttemptStatus.testId,
        testName: testData.description,
        subject: testData.subject,
        totalPercentage: totalPercentage,
        timeSpent: timeSpent,
        correctAnswers: correctAnswers,
        incorrectAnswers: incorrectAnswers,
        unansweredAnswers: unansweredAnswers,
        totalQuestions: totalQuestions,
        startTime: startTime,
        endTime: endTime,
      );
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching test result summary: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching test result summary: $e',
      );
    }
  }

  @override
  Future<TestQuestionsWithAnswerStatus> fetchTestQuestionsWithAnswerStatus(
      String profileId, String testAttemptId, TestTypes type) async {
    try {
      // Get the test attempt status to get basic test info
      TestAttemptEntry testAttemptStatus =
          await fetchTestAttemptStatus(profileId, testAttemptId, type);

      // Get test details from tests_master collection
      Test testData = await fetchTestsData(testAttemptStatus.testId);

      // Get all question attempt data for this test
      List<QuestionAttemptData> questionAttemptDataList =
          await fetchAttemptedQuestionsForATest(profileId, testAttemptId, type);

      // Create a map for quick lookup of attempted questions
      Map<String, QuestionAttemptData> attemptedQuestionsMap = {};
      for (var attempt in questionAttemptDataList) {
        attemptedQuestionsMap[attempt.questionId] = attempt;
      }

      // Fetch question details for all questions to get module and short description
      List<QuestionWithAnswerStatus> questionsWithStatus = [];

      for (var questionWithOrder in testData.questionWithOrderList) {
        try {
          // Fetch question details from the questions collection
          DocumentSnapshot<Map<String, dynamic>> questionDoc = await _firestore
              .collection('subjects/${testData.subject}/questions')
              .doc(questionWithOrder.questionId)
              .get();

          String shortDescription = 'Question ${questionWithOrder.order}';
          String correctAnswer = '';
          String module = 'Unknown';
          QuestionAndAnswers questionAndAnswersData = QuestionAndAnswers
              .emptyQuestion(); // Provide a default initialization

          if (questionDoc.exists && questionDoc.data() != null) {
            // The question data is inside the 'data' JSON field
            String dataJson = questionDoc.data()!['data'] ?? '';
            if (dataJson.isNotEmpty) {
              try {
                Map<String, dynamic> dataMap = json.decode(dataJson);
                shortDescription = dataMap['shortDescription'] ??
                    'Question ${questionWithOrder.order}';
                correctAnswer = dataMap['correctAnswer'] ?? '';
                module = dataMap['module'] ?? 'Unknown';
                QuestionModel questionModel = QuestionModel(
                    id: questionWithOrder.questionId,
                    data: questionDoc['data'],
                    bundleId: '');
                questionAndAnswersData = QuestionDataUtil()
                    .convertQuestionModelToEntity(questionModel);
              } catch (e) {
                // If JSON parsing fails, use defaults
                print(
                    "JSON parsing failed for question ${questionWithOrder.questionId}: $e");
              }
            }
          }

          // Determine answer status
          QuestionAnswerStatus answerStatus;
          String selectedAnswer = '';

          if (attemptedQuestionsMap.containsKey(questionWithOrder.questionId)) {
            QuestionAttemptData attempt =
                attemptedQuestionsMap[questionWithOrder.questionId]!;
            selectedAnswer = attempt.selectedAnswer;
            answerStatus = attempt.isCorrect
                ? QuestionAnswerStatus.correct
                : QuestionAnswerStatus.incorrect;
          } else {
            answerStatus = QuestionAnswerStatus.unanswered;
          }

          questionsWithStatus.add(QuestionWithAnswerStatus(
            questionId: questionWithOrder.questionId,
            questionOrder: questionWithOrder.order,
            shortDescription: shortDescription,
            selectedAnswer: selectedAnswer,
            correctAnswer: correctAnswer,
            answerStatus: answerStatus,
            module: module,
            questionAndAnswers:
                questionAndAnswersData, // Ensure this variable is initialized
          ));
        } catch (e) {
          // If we can't fetch a specific question, add it with minimal info
          QuestionAnswerStatus answerStatus;
          String selectedAnswer = '';

          if (attemptedQuestionsMap.containsKey(questionWithOrder.questionId)) {
            QuestionAttemptData attempt =
                attemptedQuestionsMap[questionWithOrder.questionId]!;
            selectedAnswer = attempt.selectedAnswer;
            answerStatus = attempt.isCorrect
                ? QuestionAnswerStatus.correct
                : QuestionAnswerStatus.incorrect;
          } else {
            answerStatus = QuestionAnswerStatus.unanswered;
          }

          questionsWithStatus.add(QuestionWithAnswerStatus(
            questionId: questionWithOrder.questionId,
            questionOrder: questionWithOrder.order,
            shortDescription: 'Question ${questionWithOrder.order}',
            selectedAnswer: selectedAnswer,
            correctAnswer: '',
            answerStatus: answerStatus,
            module: 'Unknown',
            questionAndAnswers: QuestionAndAnswers.emptyQuestion(),
          ));

          print(
              "Error fetching question details for ${questionWithOrder.questionId}: $e");
        }
      }

      // Sort questions by order
      questionsWithStatus
          .sort((a, b) => a.questionOrder.compareTo(b.questionOrder));

      // Calculate statistics
      int correctAnswers = questionsWithStatus
          .where((q) => q.answerStatus == QuestionAnswerStatus.correct)
          .length;
      int incorrectAnswers = questionsWithStatus
          .where((q) => q.answerStatus == QuestionAnswerStatus.incorrect)
          .length;
      int unansweredQuestions = questionsWithStatus
          .where((q) => q.answerStatus == QuestionAnswerStatus.unanswered)
          .length;

      return TestQuestionsWithAnswerStatus(
        testAttemptId: testAttemptId,
        testId: testAttemptStatus.testId,
        testName: testData.description,
        subject: testData.subject,
        questions: questionsWithStatus,
        totalQuestions: questionsWithStatus.length,
        correctAnswers: correctAnswers,
        incorrectAnswers: incorrectAnswers,
        unansweredQuestions: unansweredQuestions,
      );
    } on FirebaseException catch (e) {
      throw Exception(
        'Error fetching test questions with answer status: ${e.message}',
      );
    } catch (e) {
      throw Exception(
        'Error fetching test questions with answer status: $e',
      );
    }
  }

  @override
  Future<Test> fetchTodaysDailyTest(int userLevel) async {
    try {
      // Format today's date as ddmmyyyy
      DateTime now = DateTime.now();
      String todayDate =
          '${now.day.toString().padLeft(2, '0')}${now.month.toString().padLeft(2, '0')}${now.year}';

      print(
          "fetchTodaysDailyTest: userLevel = $userLevel, todayDate = $todayDate");

      // Fetch today's test schedule from test_schedule > daily > levels > {level} > schedules > {todayDate}
      DocumentSnapshot<Map<String, dynamic>> scheduleDoc = await _firestore
          .collection('tests_mapping/daily/levels/$userLevel/dates')
          .doc(todayDate)
          .get();

      if (!scheduleDoc.exists || scheduleDoc.data() == null) {
        throw ServerException(
            message:
                'No daily test scheduled for today ($todayDate) at level $userLevel',
            statusCode: '404');
      }

      String testId = scheduleDoc.data()!['test_id'];
      if (testId.isEmpty) {
        throw ServerException(
            message: 'Invalid test_id in schedule for date $todayDate',
            statusCode: '400');
      }

      print("fetchTodaysDailyTest: Found testId = $testId");
      String testName = scheduleDoc.data()!['test_name'];
      String subject = scheduleDoc.data()!['subject'];

      return Test(
          testId: testId,
          description: testName,
          questionWithOrderList: const [],
          subject: subject);
    } on FirebaseException catch (e) {
      throw ServerException(
        message: 'Error fetching today\'s daily test: ${e.message}',
        statusCode: e.code,
      );
    } catch (e) {
      throw ServerException(
        message: 'Error fetching today\'s daily test: $e',
        statusCode: '500',
      );
    }
  }

  @override
  Future<bool> isTodaysDailyTestAttempted(
      String profileId, int userLevel) async {
    try {
      // Format today's date as ddmmyyyy
      DateTime now = DateTime.now();
      String todayDate =
          '${now.day.toString().padLeft(2, '0')}${now.month.toString().padLeft(2, '0')}${now.year}';

      // Fetch today's test id from schedule
      DocumentSnapshot<Map<String, dynamic>> scheduleDoc = await _firestore
          .collection('tests_mapping/daily/levels/$userLevel/dates')
          .doc(todayDate)
          .get();

      if (!scheduleDoc.exists || scheduleDoc.data() == null) {
        return false;
      }

      String testId = scheduleDoc.data()!['test_id'];
      if (testId.isEmpty) {
        return false;
      }

      // Check if user has an attempt for this testId
      QuerySnapshot attemptsSnapshot = await _firestore
          .collection(
              'tests_user_attempts/$profileId/test_types/daily/test_attempts')
          .where('test_id', isEqualTo: testId)
          .get();

      return attemptsSnapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking if today\'s daily test is attempted: $e');
      return false;
    }
  }
}
