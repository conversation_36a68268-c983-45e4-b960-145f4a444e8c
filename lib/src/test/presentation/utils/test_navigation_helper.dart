import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';

/// Helper class for test-related navigation
class TestNavigationHelper {
  /// Navigate to the test instructions screen
  ///
  /// This method should be called when a user wants to start a test.
  /// It will navigate to the instructions screen before the actual test begins.
  static void navigateToTestInstructions(
    BuildContext context, {
    required Test test,
    required String testAttemptId,
    required String subject,
    required int questionCount,
    required int durationMinutes,
  }) {
    context.push('/test-instructions', extra: {
      'test': test,
      'testAttemptId': testAttemptId,
      'subject': subject,
      'questionCount': questionCount,
      'durationMinutes': durationMinutes,
    });
  }
}
