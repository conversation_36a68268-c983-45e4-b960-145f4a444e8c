part of 'tests_bloc.dart';

class TestsStateInitial extends Equatable {
  const TestsStateInitial();

  @override
  List<Object?> get props => [];
}

class TestsLoadingState extends TestsStateInitial {}

class NewTestFetchedState extends TestsStateInitial {
  final Test test;
  final String testAttemptId;
  final TestTypes testType;
  const NewTestFetchedState({
    required this.test,
    required this.testAttemptId,
    required this.testType,
  }) : super();

  @override
  List<Object?> get props => [test];

  @override
  String toString() {
    return 'Test { Test: $test }';
  }
}

class NewTestFetchErrorState extends TestsStateInitial {
  final String message;

  const NewTestFetchErrorState({required this.message}) : super();

  @override
  List<Object?> get props => [message];
}

class TestsDataState extends TestsStateInitial {
  final String testAttemptId;
  final Test test;
  final TestStatus status;
  final SectionalOrDailyTestData testWithQuestionsData;
  final TestQuestionEntry currentQuestion;
  final bool isLastQuestion;
  final bool isFirstQuestion;
  final List<FlaggedTestEntry> flaggedQuestions;
  final TestTypes testType;

  // Timer-related fields
  // final DateTime? testStartTime;
//  final Duration? remainingTime;
  // final bool isTimerRunning;

  const TestsDataState({
    required this.testAttemptId,
    required this.test,
    required this.status,
    required this.testWithQuestionsData,
    required this.currentQuestion,
    required this.isLastQuestion,
    required this.isFirstQuestion,
    required this.flaggedQuestions,
    required this.testType,
  }) : super();

  /*
  // Add proper equality implementation
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TestsDataState &&
        other.testAttemptId == testAttemptId &&
        other.test == test &&
        other.status == status &&
        other.currentQuestion.questionId == currentQuestion.questionId &&
        other.currentQuestion.selectedAnswer == currentQuestion.selectedAnswer;
    // Note: We're not comparing all fields for efficiency
  }

  @override
  int get hashCode => Object.hash(
        testAttemptId,
        test,
        status,
        currentQuestion.questionId,
        currentQuestion.selectedAnswer,
      );
*/
  //Add a copy with constructor
  TestsDataState copyWith({
    TestQuestionEntry? currentQuestion,
    bool? isLastQuestion,
    List<FlaggedTestEntry>? flaggedQuestions,
    DateTime? testStartTime,
    Duration? remainingTime,
    bool? isTimerRunning,
  }) {
    return TestsDataState(
      testAttemptId: testAttemptId,
      test: test,
      status: status,
      testWithQuestionsData: testWithQuestionsData,
      currentQuestion: currentQuestion ?? this.currentQuestion,
      isLastQuestion: isLastQuestion ?? this.isLastQuestion,
      flaggedQuestions: flaggedQuestions ?? this.flaggedQuestions,
      isFirstQuestion: isFirstQuestion,
      testType: testType,
    );
  }

  @override
  List<Object?> get props => [
        testWithQuestionsData.testId,
        status,
        currentQuestion.questionId,
        currentQuestion.selectedAnswer
      ];

  @override
  String toString() {
    return 'TestWithQuestionsData { TestWithQuestionsData: $testWithQuestionsData, status: $status, current question: $currentQuestion, isLastQuestion: $isLastQuestion, flaggedQuestions: $flaggedQuestions }';
  }
}

class TestDataErrorState extends TestsStateInitial {
  final String message;

  const TestDataErrorState({required this.message}) : super();

  @override
  List<Object?> get props => [message];
}

class TestStartedState extends TestsDataState {
  const TestStartedState({
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    required super.testType,
  });

  @override
  String toString() {
    return 'TestStartedState { TestWithQuestionsData: $testWithQuestionsData, status: $status }';
  }
}

class TestStartedErrorState extends TestsDataState {
  final String message;
  const TestStartedErrorState({
    required super.test,
    required this.message,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    required super.testType,
  });

  @override
  String toString() {
    return 'TestStartedErrorState { TestWithQuestionsData: $testWithQuestionsData, status: $status }';
  }
}

class NextQuestionFetchedState extends TestsDataState {
  const NextQuestionFetchedState({
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    required super.testType,
  });

  @override
  String toString() {
    return 'NextQuestionFetchedState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion }';
  }
}

class AtLastQuestionState extends TestsDataState {
  AtLastQuestionState({
    required TestsDataState testDataState,
  }) : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          isLastQuestion: true,
          isFirstQuestion: testDataState.isFirstQuestion,
          testAttemptId: testDataState.testAttemptId,
          flaggedQuestions: testDataState.flaggedQuestions,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'AtLastQuestionState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion , isLastQuestion: $isLastQuestion }';
  }
}

class AtFirstQuestionState extends TestsDataState {
  AtFirstQuestionState({
    required TestsDataState testDataState,
  }) : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: true,
          testAttemptId: testDataState.testAttemptId,
          flaggedQuestions: testDataState.flaggedQuestions,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'AtFirstQuestionState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion , isLastQuestion: $isLastQuestion }';
  }
}

class AnswererCapturedState extends TestsDataState {
  final String message;

  const AnswererCapturedState({
    required this.message,
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    required super.testType,
  });

  @override
  String toString() {
    return 'AnswererCapturedState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion , message: $message}';
  }
}

class AnswererCaptureErrorState extends TestsDataState {
  final String message;

  const AnswererCaptureErrorState({
    required this.message,
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    required super.testType,
  });

  @override
  String toString() {
    return 'AnswererCapturedState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion , message: $message}';
  }
}

class TestProgressState extends TestsDataState {
  final List<TestProgressSummary> testProgressSummaryList;

  final int timestamp;

  const TestProgressState({
    required this.testProgressSummaryList,
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    this.timestamp = 0,
    required super.testType,
  });

  @override
  List<Object?> get props => [testProgressSummaryList, timestamp];

  @override
  String toString() {
    return 'TestProgressState { testProgressSummaryList: $testProgressSummaryList, currentesttQuestion: $test, timestamp: $timestamp }';
  }
}

class TestSummarySuccessState extends TestsDataState {
  TestSummarySuccessState(TestsDataState testDataState)
      : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          testAttemptId: testDataState.testAttemptId,
          flaggedQuestions: testDataState.flaggedQuestions,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: testDataState.isFirstQuestion,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'TestSummarySuccessState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion}';
  }
}

class TestUnexpectedErrorState extends TestsDataState {
  final String message;
  TestUnexpectedErrorState(TestsDataState testDataState, this.message)
      : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          testAttemptId: testDataState.testAttemptId,
          flaggedQuestions: testDataState.flaggedQuestions,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: testDataState.isFirstQuestion,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'TestUnexpectedErrorState { TestWithQuestionsData: $testWithQuestionsData, message: $message}';
  }
}

class QuestionFlagSuccessState extends TestsDataState {
  final String message;

  QuestionFlagSuccessState(TestsDataState testDataState, this.message)
      : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          testAttemptId: testDataState.testAttemptId,
          flaggedQuestions: testDataState.flaggedQuestions,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: testDataState.isFirstQuestion,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'QuestionFlagSuccessState { TestWithQuestionsData: $testWithQuestionsData, flaggedQuestions: $flaggedQuestions, currentQuestion: $currentQuestion , message: $message}';
  }
}

class QuestionFlagErrorState extends TestsDataState {
  final String message;

  QuestionFlagErrorState(TestsDataState testDataState, this.message)
      : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          testAttemptId: testDataState.testAttemptId,
          flaggedQuestions: testDataState.flaggedQuestions,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: testDataState.isFirstQuestion,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'QuestionFlagErrorState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion , message: $message}';
  }
}

class FlaggedQuestionsViewState extends TestsDataState {
  FlaggedQuestionsViewState(
    TestsDataState testDataState,
  ) : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: testDataState.currentQuestion,
          flaggedQuestions: testDataState.flaggedQuestions,
          testAttemptId: testDataState.testAttemptId,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: testDataState.isFirstQuestion,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'FlaggedQuestionsViewState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion, flaggedQuestions: $flaggedQuestions}';
  }
}

//SelectedQuestionViewState

class SelectedQuestionViewState extends TestsDataState {
  SelectedQuestionViewState(
    TestsDataState testDataState,
    TestQuestionEntry selectedQuestion,
  ) : super(
          test: testDataState.test,
          status: testDataState.status,
          testWithQuestionsData: testDataState.testWithQuestionsData,
          currentQuestion: selectedQuestion,
          flaggedQuestions: testDataState.flaggedQuestions,
          testAttemptId: testDataState.testAttemptId,
          isLastQuestion: testDataState.isLastQuestion,
          isFirstQuestion: testDataState.isFirstQuestion,
          testType: testDataState.testType,
        );

  @override
  String toString() {
    return 'SelectedQuestionViewState { TestWithQuestionsData: $testWithQuestionsData, currentQuestion: $currentQuestion}';
  }
}

class SubmitTestSuccessState extends TestsStateInitial {
  final String testAttemptId;
  final String testId;
  final String description;
  final TestTypes testType;
  final TestAttemptEntry status;
  final int timeTakenInMinutes; // Add time taken field

  const SubmitTestSuccessState({
    required this.testAttemptId,
    required this.testId,
    required this.description,
    required this.testType,
    required this.status,
    required this.timeTakenInMinutes,
  }) : super();
}

class FetchTestAttemptStatusState extends TestsStateInitial {
  final TestAttemptEntry testAttemptStatus;

  const FetchTestAttemptStatusState({
    required this.testAttemptStatus,
  }) : super();
}

class FetchTestAttemptDetailsState extends TestsStateInitial {
  final TestAttemptEntry testAttemptStatus;
  final List<QuestionAttemptData> questionAttemptDataList;

  const FetchTestAttemptDetailsState({
    required this.testAttemptStatus,
    required this.questionAttemptDataList,
  }) : super();
}

class TestProgressSummary {
  final String questionId;
  final int order;
  final String
      questionStatus; // 'attempted', 'not_attempted', 'current', 'flagged'

  TestProgressSummary({
    required this.questionId,
    required this.order,
    required this.questionStatus,
  });
}

class TestFinishSummaryState extends TestsDataState {
  const TestFinishSummaryState({
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
    required super.testType,
  });

  @override
  String toString() {
    return 'TestFinishSummaryState { testName: ${test.description}, testSubject: ${test.subject} }';
  }
}

class QuestionDetailState extends TestsStateInitial {
  final detailed.QuestionAttemptData questionDetail;
  final int currentIndex;
  final int totalQuestions;
  final String testAttemptId;
  final List<detailed.QuestionAttemptData> allQuestions;

  const QuestionDetailState({
    required this.questionDetail,
    required this.currentIndex,
    required this.totalQuestions,
    required this.testAttemptId,
    required this.allQuestions,
  });

  @override
  List<Object?> get props => [
        questionDetail.questionId,
        currentIndex,
        totalQuestions,
        testAttemptId,
      ];

  @override
  String toString() {
    return 'QuestionDetailState { questionId: ${questionDetail.questionId}, currentIndex: $currentIndex, totalQuestions: $totalQuestions }';
  }
}

class FetchTestAttemptHistoryState extends TestsStateInitial {
  final List<SectionalTestAttemptSummary> testAttemptHistory;
  const FetchTestAttemptHistoryState({
    required this.testAttemptHistory,
  }) : super();
  @override
  List<Object?> get props => [testAttemptHistory];
  @override
  String toString() {
    return 'FetchSectionalTestAttemptHistoryState { testAttemptHistory: $testAttemptHistory  }';
  }
}

class FetchTestAttemptHistoryErrorState extends TestsStateInitial {
  final String message;
  const FetchTestAttemptHistoryErrorState({required this.message}) : super();
  @override
  List<Object?> get props => [message];
  @override
  String toString() {
    return 'FetchSectionalTestAttemptHistoryErrorState { message: $message }';
  }
}

class FetchGroupedSectionalTestAttemptHistoryState extends TestsStateInitial {
  final List<GroupedSectionalTestAttempts> groupedTestAttemptHistory;
  const FetchGroupedSectionalTestAttemptHistoryState({
    required this.groupedTestAttemptHistory,
  }) : super();
  @override
  List<Object?> get props => [groupedTestAttemptHistory];
  @override
  String toString() {
    return 'FetchGroupedSectionalTestAttemptHistoryState { groupedTestAttemptHistory: ${groupedTestAttemptHistory.length} groups }';
  }
}

class FetchGroupedSectionalTestAttemptHistoryErrorState
    extends TestsStateInitial {
  final String message;
  const FetchGroupedSectionalTestAttemptHistoryErrorState(
      {required this.message})
      : super();
  @override
  List<Object?> get props => [message];
  @override
  String toString() {
    return 'FetchGroupedSectionalTestAttemptHistoryErrorState { message: $message }';
  }
}

class FetchTestAttemptAnalysisState extends TestsStateInitial {
  final TestAttemptAnalysis testAttemptAnalysis;
  const FetchTestAttemptAnalysisState({
    required this.testAttemptAnalysis,
  }) : super();
  @override
  List<Object?> get props => [testAttemptAnalysis];
  @override
  String toString() {
    return 'FetchTestAttemptAnalysisState { testAttemptAnalysis: ${testAttemptAnalysis.testAttemptId} }';
  }
}

class FetchTestAttemptAnalysisErrorState extends TestsStateInitial {
  final String message;
  const FetchTestAttemptAnalysisErrorState({required this.message}) : super();
  @override
  List<Object?> get props => [message];
  @override
  String toString() {
    return 'FetchTestAttemptAnalysisErrorState { message: $message }';
  }
}

class FetchTestResultSummaryState extends TestsStateInitial {
  final TestResultSummary testResultSummary;
  const FetchTestResultSummaryState({
    required this.testResultSummary,
  }) : super();
  @override
  List<Object?> get props => [testResultSummary];
  @override
  String toString() {
    return 'FetchTestResultSummaryState { testResultSummary: ${testResultSummary.testAttemptId} }';
  }
}

class FetchTestResultSummaryErrorState extends TestsStateInitial {
  final String message;
  const FetchTestResultSummaryErrorState({required this.message}) : super();
  @override
  List<Object?> get props => [message];
  @override
  String toString() {
    return 'FetchTestResultSummaryErrorState { message: $message }';
  }
}

class FetchTestQuestionsWithAnswerStatusState extends TestsStateInitial {
  final TestQuestionsWithAnswerStatus testQuestionsWithAnswerStatus;
  const FetchTestQuestionsWithAnswerStatusState({
    required this.testQuestionsWithAnswerStatus,
  }) : super();
  @override
  List<Object?> get props => [testQuestionsWithAnswerStatus];
  @override
  String toString() {
    return 'FetchTestQuestionsWithAnswerStatusState { testQuestionsWithAnswerStatus: ${testQuestionsWithAnswerStatus.testAttemptId} }';
  }
}

class FetchTestQuestionsWithAnswerStatusErrorState extends TestsStateInitial {
  final String message;
  const FetchTestQuestionsWithAnswerStatusErrorState({required this.message})
      : super();
  @override
  List<Object?> get props => [message];
  @override
  String toString() {
    return 'FetchTestQuestionsWithAnswerStatusErrorState { message: $message }';
  }
}

class IsDailyTestAttemptedState extends TestsStateInitial {
  final bool isAttempted;
  const IsDailyTestAttemptedState({required this.isAttempted});

  @override
  List<Object?> get props => [isAttempted];
}
