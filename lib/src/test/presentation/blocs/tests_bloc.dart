import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/domain/entitites/question_attempt_data.dart'
    as detailed;
import 'package:skillapp/src/test/domain/usecases/answer_a_question.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_new_sectional_test.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_sectional_test_attempt_details.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_grouped_sectional_test_attempt_history.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_sectional_test_attempt_history.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_sectional_test_attempt_status.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_test_attempt_analysis.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_test_questions_with_answer_status.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_test_result_summary.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_tests_data.dart';
import 'package:skillapp/src/test/domain/usecases/daily_test/fetch_todays_daily_test.dart';
import 'package:skillapp/src/test/domain/usecases/mark_test_as_started.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/submit_sectional_test.dart';
import 'package:skillapp/src/test/domain/usecases/daily_test/is_daily_test_attempted.dart';
import 'package:uuid/uuid.dart';

part 'tests_event.dart';
part 'tests_state.dart';

class TestsBloc extends Bloc<TestsEvent, TestsStateInitial> {
  final FetchTestsData _fetchTestData;
  final FetchNewSectionalTest _fetchNewSectionalTest;
  final MarkTestAsStarted _markTestAsStarted;
  final AnswerAQuestion _answerAQuestion;
  final SubmitTest _submitSectionalTest;
  final FetchSectionalTestAttemptStatus _fetchTestAttemptStatus;
  final FetchTestAttemptDetails _fetchTestAttemptDetails;
  final FetchSectionalTestAttemptHistory _fetchTestAttemptHistory;
  final FetchGroupedSectionalTestAttemptHistory _fetchGroupedTestAttemptHistory;
  final FetchTestAttemptAnalysis _fetchTestAttemptAnalysis;
  final FetchTestResultSummary _fetchTestResultSummary;
  final FetchTestQuestionsWithAnswerStatus _fetchTestQuestionsWithAnswerStatus;
  final FetchTodaysDailyTest _fetchTodaysDailyTest;
  final IsDailyTestAttempted _isDailyTestAttempted;

  // Timer-related fields
  Timer? _timer;

  TestsBloc({
    required FetchTestsData fetchTestData,
    required FetchNewSectionalTest fetchNewSectionalTest,
    required MarkTestAsStarted markTestAsStarted,
    required AnswerAQuestion answerAQuestion,
    required SubmitTest submitSectionalTest,
    required FetchSectionalTestAttemptStatus fetchSectionalTestAttemptStatus,
    required FetchTestAttemptDetails fetchSectionalTestAttemptDetails,
    required FetchSectionalTestAttemptHistory fetchSectionalTestAttemptHistory,
    required FetchGroupedSectionalTestAttemptHistory
        fetchGroupedSectionalTestAttemptHistory,
    required FetchTestAttemptAnalysis fetchTestAttemptAnalysis,
    required FetchTestResultSummary fetchTestResultSummary,
    required FetchTestQuestionsWithAnswerStatus
        fetchTestQuestionsWithAnswerStatus,
    required FetchTodaysDailyTest fetchTodaysDailyTest,
    required IsDailyTestAttempted isDailyTestAttempted,
  })  : _fetchTestData = fetchTestData,
        _fetchNewSectionalTest = fetchNewSectionalTest,
        _markTestAsStarted = markTestAsStarted,
        _answerAQuestion = answerAQuestion,
        _submitSectionalTest = submitSectionalTest,
        _fetchTestAttemptStatus = fetchSectionalTestAttemptStatus,
        _fetchTestAttemptDetails = fetchSectionalTestAttemptDetails,
        _fetchTestAttemptHistory = fetchSectionalTestAttemptHistory,
        _fetchGroupedTestAttemptHistory =
            fetchGroupedSectionalTestAttemptHistory,
        _fetchTestAttemptAnalysis = fetchTestAttemptAnalysis,
        _fetchTestResultSummary = fetchTestResultSummary,
        _fetchTestQuestionsWithAnswerStatus =
            fetchTestQuestionsWithAnswerStatus,
        _fetchTodaysDailyTest = fetchTodaysDailyTest,
        _isDailyTestAttempted = isDailyTestAttempted,
        super(const TestsStateInitial()) {
    on<FetchTestDataEvent>(_fetchTestDataEventHandler);
    on<FetchNewSectionalTestEvent>(_fetchNewSectionalTestEventHandler);
    on<MarkTestAsStartedEvent>(_markTestAsStartedEventHandler);
    on<FetchNextQuestionEvent>(_fetchNextQuestionEventHandler);
    on<FetchPreviousQuestionEvent>(_fetchPreviousQuestionEventHandler);
    on<AnswerAQuestionEvent>(_answerAQuestionEventHandler);
    on<FlagAQuestionEvent>(_flagAQuestionEventHandler);
    on<ViewFlaggedQuestionsEvent>(_viewFlaggedQuestionsEventHandler);
    on<ViewSelectedQuestionEvent>(_viewSelectedQuestionEventHandler);
    on<ClearDataUnitTesting>(_clearDataUnitTestingEventHandler);
    on<SubmitTestEvent>(_submitSectionalTestEventHandler);
    on<FetchTestAttemptStatusEvent>(_fetchTestAttemptStatusEventHandler);
    on<FetchTestAttemptDetailsEvent>(_fetchTestAttemptDetailsEventHandler);
    on<FetchTestAttemptHistoryEvent>(_fetchTestAttemptHistoryEventHandler);
    on<FetchGroupedTestAttemptHistoryEvent>(
        _fetchGroupedTestAttemptHistoryEventHandler);
    on<FetchTestAttemptAnalysisEvent>(_fetchTestAttemptAnalysisEventHandler);
    on<FetchTestResultSummaryEvent>(_fetchTestResultSummaryEventHandler);
    on<FetchTestQuestionsWithAnswerStatusEvent>(
        _fetchTestQuestionsWithAnswerStatusEventHandler);
    on<FetchTodaysDailyTestEvent>(_fetchTodaysDailyTestEventHandler);
    on<ViewTestProgressEvent>(_viewTestProgressEventHandler);
    on<ViewTestFinishSummaryEvent>(_viewTestFinishSummaryEventHandler);
    on<FetchQuestionDetailEvent>(_fetchQuestionDetailEventHandler);
    on<FlagQuestionInResultEvent>(_flagQuestionInResultEventHandler);
    on<CheckIsDailyTestAttemptedEvent>(_checkIsDailyTestAttemptedEventHandler);
    on<RetakeSectionalTestEvent>(_retakeSectionalTestEventHandler);
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  @override
  void onChange(Change<TestsStateInitial> change) {
    super.onChange(change);
    print('TestsBloc: onChange called');
    print('  - currentState: ${change.currentState.runtimeType}');
    print('  - nextState: ${change.nextState.runtimeType}');

    if (change.nextState is TestProgressState) {
      print(
          '  - nextState is TestProgressState with timestamp: ${(change.nextState as TestProgressState).timestamp}');
    }
  }

  FutureOr<void> _fetchTestDataEventHandler(
      FetchTestDataEvent event, Emitter<TestsStateInitial> emit) async {
    final result = await _fetchTestData(event.testId);
    print("result=$result");
    NewTestFetchedState currentState = state as NewTestFetchedState;
    print(currentState);
    result.fold(
      (failure) => emit(TestDataErrorState(message: failure.errorMessage)),
      (testData) {
        if (testData.testQuestionEntrySet.isEmpty) {
          emit(TestDataErrorState(
            message:
                "No questions found for this test. Please try again later.",
          ));
          return;
        }

        emit(
          TestsDataState(
            testAttemptId: currentState.testAttemptId,
            status: TestStatus.notStarted,
            testWithQuestionsData: testData,
            currentQuestion: testData.testQuestionEntrySet.first,
            test: currentState.test,
            isLastQuestion: false,
            isFirstQuestion: true,
            flaggedQuestions: const [],
            testType: currentState.testType,
          ),
        );
      },
    );
  }

  FutureOr<void> _fetchNewSectionalTestEventHandler(
      FetchNewSectionalTestEvent event, Emitter<TestsStateInitial> emit) async {
    try {
      print("JW: Starting fetchNewSectionalTest for subject: ${event.subject}");

      final result = await _fetchNewSectionalTest(event.subject);

      print("JW:_fetchNewSectionalTestEventHandler: result=$result");

      result.fold(
        (failure) {
          print("JW: Failure occurred: ${failure.errorMessage}");
          emit(NewTestFetchErrorState(message: failure.errorMessage));
        },
        (test) {
          print("JW: Success - Test fetched: ${test.testId}");
          emit(
            NewTestFetchedState(
              test: test,
              testAttemptId: const Uuid().v4(),
              testType: TestTypes.sectional,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      print("JW: Exception caught in handler: $e");
      print("JW: StackTrace: $stackTrace");
      emit(NewTestFetchErrorState(
          message: "Failed to fetch sectional test: $e"));
    }
  }

  FutureOr<void> _retakeSectionalTestEventHandler(
      RetakeSectionalTestEvent event, Emitter<TestsStateInitial> emit) async {
    try {
      print("JW: Starting retake sectional test for subject: ${event.subject}");
      print("Jw: Starting retake sectional test for test id: ${event.testId}");

      emit(
        NewTestFetchedState(
          test: Test(
            testId: event.testId,
            description: event.testName,
            subject: event.subject,
            questionWithOrderList: [],
          ),
          testAttemptId: const Uuid().v4(),
          testType: TestTypes.sectional,
        ),
      );
    } catch (e, stackTrace) {
      print("JW: Exception caught in handler: $e");
      print("JW: StackTrace: $stackTrace");
      emit(NewTestFetchErrorState(
          message: "Failed to fetch sectional test: $e"));
    }
  }

  FutureOr<void> _markTestAsStartedEventHandler(
      MarkTestAsStartedEvent event, Emitter<TestsStateInitial> emit) async {
    print(
        '_markTestAsStartedEventHandler current state is ${state.runtimeType}');
    TestsDataState currentState = state as TestsDataState;
    // NewTestFetchedState currentState= state as NewTestFetchedState;
    final result = await _markTestAsStarted(MarkTestAsStartedParams(
      testAttemptId: currentState.testAttemptId,
      testId: currentState.test.testId,
      numberOfQuestions:
          currentState.testWithQuestionsData.testQuestionEntrySet.length,
      testType: event.testType,
    ));

    result.fold(
        (failure) => emit(
              TestStartedErrorState(
                testAttemptId: currentState.testAttemptId,
                message: failure.errorMessage,
                status: TestStatus.notStarted,
                testWithQuestionsData: currentState.testWithQuestionsData,
                currentQuestion: currentState.currentQuestion,
                test: currentState.test,
                isLastQuestion: currentState.isLastQuestion,
                isFirstQuestion: currentState.isFirstQuestion,
                flaggedQuestions: currentState.flaggedQuestions,
                testType: currentState.testType,
              ),
            ),
        (_) => emit(TestStartedState(
              testAttemptId: currentState.testAttemptId,
              status: TestStatus.started,
              testWithQuestionsData: currentState.testWithQuestionsData,
              currentQuestion: currentState.currentQuestion,
              test: currentState.test,
              isLastQuestion: currentState.isLastQuestion,
              isFirstQuestion: currentState.isFirstQuestion,
              flaggedQuestions: currentState.flaggedQuestions,
              testType: currentState.testType,
            )));
  }

  // Event for fetching the next question
  FutureOr<void> _fetchNextQuestionEventHandler(
      FetchNextQuestionEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    if (event.currentOrder <
        currentState.testWithQuestionsData.testQuestionEntrySet.length) {
      final nextQuestion = currentState
          .testWithQuestionsData.testQuestionEntrySet
          .firstWhere((element) => element.order == event.currentOrder + 1);
      emit(TestsDataState(
          testAttemptId: currentState.testAttemptId,
          testType: currentState.testType,
          test: currentState.test,
          status: currentState.status,
          testWithQuestionsData: currentState.testWithQuestionsData,
          currentQuestion: nextQuestion,
          flaggedQuestions: currentState.flaggedQuestions,
          isFirstQuestion: nextQuestion.order == 1,
          isLastQuestion: nextQuestion.order ==
              currentState.testWithQuestionsData.testQuestionEntrySet.length));
    } else {
      emit(AtLastQuestionState(
        testDataState: currentState,
      ));
    }
  }

  FutureOr<void> _fetchPreviousQuestionEventHandler(
      FetchPreviousQuestionEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    if (event.currentOrder > 1) {
      final prevQuestion = currentState
          .testWithQuestionsData.testQuestionEntrySet
          .firstWhere((element) => element.order == event.currentOrder - 1);
      emit(TestsDataState(
        testAttemptId: currentState.testAttemptId,
        testType: currentState.testType,
        test: currentState.test,
        status: currentState.status,
        testWithQuestionsData: currentState.testWithQuestionsData,
        currentQuestion: prevQuestion,
        flaggedQuestions: currentState.flaggedQuestions,
        isLastQuestion: prevQuestion.order ==
            currentState.testWithQuestionsData.testQuestionEntrySet.length,
        isFirstQuestion: prevQuestion.order == 1,
      ));
    } else {
      emit(AtFirstQuestionState(
        testDataState: currentState,
      ));
    }
  }

  FutureOr<void> _answerAQuestionEventHandler(
      AnswerAQuestionEvent event, Emitter<TestsStateInitial> emit) async {
    print("Answer event published is $event");

    TestsDataState currentState = state as TestsDataState;
    print("CurentState of question in answerEvent $currentState");

    final updatedTestData = _updateCurrentQuestion(
      currentState.testWithQuestionsData,
      event.questionId,
      event.selectedAnswer,
    );

    // Find the updated question in the updated test data
    final updatedCurrentQuestion = updatedTestData.testQuestionEntrySet
        .firstWhere(
            (q) => q.questionId == currentState.currentQuestion.questionId);

    AnswerAQuestionParams params = AnswerAQuestionParams(
      testAttemptId: currentState.testAttemptId,
      testTypes: currentState.testType,
      questionId: event.questionId,
      selectedAnswer: event.selectedAnswer,
      testId: currentState.testWithQuestionsData.testId,
      isCorrect: event.selectedAnswer ==
          currentState.testWithQuestionsData.testQuestionEntrySet
              .firstWhere((element) => element.questionId == event.questionId)
              .questionData
              .correctAnswer,
    );
    final result = await _answerAQuestion(params);

    print(result);

    result.fold(
        (failure) => emit(
              AnswererCaptureErrorState(
                testAttemptId: currentState.testAttemptId,
                testType: currentState.testType,
                currentQuestion: currentState.currentQuestion,
                message: failure.errorMessage,
                test: currentState.test,
                status: currentState.status,
                testWithQuestionsData: currentState.testWithQuestionsData,
                isLastQuestion: currentState.isLastQuestion,
                isFirstQuestion: currentState.isFirstQuestion,
                flaggedQuestions: currentState.flaggedQuestions,
              ),
            ),
        (_) => emit(
              AnswererCapturedState(
                testAttemptId: currentState.testAttemptId,
                testType: currentState.testType,
                //  currentQuestion: currentState.currentQuestion,
                test: currentState.test,
                status: currentState.status,
                /* testWithQuestionsData: _updateCurrentQuestion(
                  currentState.testWithQuestionsData,
                  event.questionId,
                  event.selectedAnswer,
                ),*/
                testWithQuestionsData: updatedTestData,
                currentQuestion: updatedCurrentQuestion,
                message: 'success',
                isLastQuestion: currentState.isLastQuestion,
                isFirstQuestion: currentState.isFirstQuestion,
                flaggedQuestions: currentState.flaggedQuestions,
              ),

              /*  TestsDataState(
                testAttemptId: currentState.testAttemptId,
                test: currentState.test,
                status: currentState.status,
                testWithQuestionsData: _updateCurrentQuestion(
                  currentState.testWithQuestionsData,
                  event.questionId,
                  event.selectedAnswer,
                ),
                currentQuestion: currentState.currentQuestion,
                isLastQuestion: currentState.isLastQuestion,
                isFirstQuestion: currentState.isFirstQuestion,
                flaggedQuestions: currentState.flaggedQuestions,
              ),*/
            ));
  }

  SectionalOrDailyTestData _updateCurrentQuestion(
      SectionalOrDailyTestData testWithQuestionsData,
      String questionId,
      String selectedAnswer) {
    Set<TestQuestionEntry> testQuestionEntrySet = {};
    for (var question in testWithQuestionsData.testQuestionEntrySet) {
      if (question.questionId == questionId) {
        testQuestionEntrySet.add(TestQuestionEntry(
          questionId: question.questionId,
          order: question.order,
          attempted: true,
          questionData: question.questionData,
          selectedAnswer: selectedAnswer,
          attemptCount: question.attemptCount,
          lastAttempted: question.lastAttempted,
        ));
      } else {
        testQuestionEntrySet.add(question);
      }
    }

    print('updated Test entry Question Set $testQuestionEntrySet');

    return SectionalOrDailyTestData(
        testId: testWithQuestionsData.testId,
        testType: testWithQuestionsData.testType,
        description: testWithQuestionsData.description,
        subject: testWithQuestionsData.subject,
        testQuestionEntrySet: testQuestionEntrySet);
  }

  FutureOr<void> _flagAQuestionEventHandler(
      FlagAQuestionEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    print("_flagAQuestionEventHandler: event.questionId=${event.questionId}");
    //use copyWith to create a new instance of the current state
    //and add the flagged question to the list

    List<FlaggedTestEntry> entryList = currentState.flaggedQuestions
        .where((element) => element.questionId != event.questionId)
        .toList();

    print("Inside flagged question: entryList=$entryList");

    entryList.add(FlaggedTestEntry(
      questionId: event.questionId,
      order: currentState.currentQuestion.order,
      isFlagged: true,
    ));

    print("Inside flagged question: updated entryList=$entryList");

    currentState = currentState.copyWith(
      flaggedQuestions: entryList,
    );

    print("Inside flagged question: updated currentState=$currentState");

    emit(QuestionFlagSuccessState(currentState, 'message'));
  }

  FutureOr<void> _viewFlaggedQuestionsEventHandler(
      ViewFlaggedQuestionsEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    emit(FlaggedQuestionsViewState(currentState));
  }

  FutureOr<void> _viewSelectedQuestionEventHandler(
      ViewSelectedQuestionEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    final selectedQuestion = currentState
        .testWithQuestionsData.testQuestionEntrySet
        .firstWhere((element) => element.questionId == event.questionId);

    emit(SelectedQuestionViewState(
      currentState,
      selectedQuestion,
    ));
  }

  FutureOr<void> _clearDataUnitTestingEventHandler(
      ClearDataUnitTesting event, Emitter<TestsStateInitial> emit) {
    emit(const TestsStateInitial());
  }

  FutureOr<void> _submitSectionalTestEventHandler(
      SubmitTestEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    final result = await _submitSectionalTest(SubmitTestParams(
      testAttemptId: currentState.testAttemptId,
      testType: currentState.testType,
    ));
    print('result - $result');

    result.fold(
        (failure) => emit(
              AnswererCaptureErrorState(
                testAttemptId: currentState.testAttemptId,
                testType: currentState.testType,
                currentQuestion: currentState.currentQuestion,
                message: failure.errorMessage,
                test: currentState.test,
                status: currentState.status,
                testWithQuestionsData: currentState.testWithQuestionsData,
                isLastQuestion: currentState.isLastQuestion,
                isFirstQuestion: currentState.isFirstQuestion,
                flaggedQuestions: currentState.flaggedQuestions,
              ),
            ), (_) {
      // First emit the answer captured state
      emit(
        AnswererCapturedState(
          testAttemptId: currentState.testAttemptId,
          testType: currentState.testType,
          currentQuestion: currentState.currentQuestion,
          test: currentState.test,
          status: currentState.status,
          testWithQuestionsData: currentState.testWithQuestionsData,
          message: 'submitted',
          isLastQuestion: currentState.isLastQuestion,
          isFirstQuestion: currentState.isFirstQuestion,
          flaggedQuestions: currentState.flaggedQuestions,
        ),
      );
    });
  }

  FutureOr<void> _fetchTestAttemptStatusEventHandler(
      FetchTestAttemptStatusEvent event,
      Emitter<TestsStateInitial> emit) async {
    //TestsDataState currentState = state as TestsDataState;

    final result = await _fetchTestAttemptStatus(TestAttemptStatusParams(
        testAttemptId: event.testAttemptId, testTypes: event.testType));
    print("result=$result");

    result.fold(
      (failure) {
        print("_fetchSectionalTestAttemptStatusEventHandler: failure=$failure");
      },
      (testAttemptStatus) => emit(
        FetchTestAttemptStatusState(testAttemptStatus: testAttemptStatus),
      ),
    );
  }

  FutureOr<void> _fetchTestAttemptDetailsEventHandler(
      FetchTestAttemptDetailsEvent event,
      Emitter<TestsStateInitial> emit) async {
    // First emit loading state
    emit(TestsLoadingState());

    final result = await _fetchTestAttemptStatus(TestAttemptStatusParams(
        testAttemptId: event.testAttemptId, testTypes: event.testType));
    print("result=$result");

    TestAttemptEntry? testAttemptStatus = result.fold((failure) {
      print("_fetchSectionalTestAttemptDetailsEventHandler: failure2=$failure");
      return null;
    }, (status) => status);

    if (testAttemptStatus != null) {
      final result = await _fetchTestAttemptDetails(TestAttemptDetailsParams(
          testAttemptId: event.testAttemptId, testType: event.testType));
      print("result=$result");
      result.fold(
        (failure) {
          print(
              "_fetchSectionalTestAttemptDetailsEventHandler: failure1=$failure");
        },
        (testAttemptDetailsList) => emit(FetchTestAttemptDetailsState(
          questionAttemptDataList: testAttemptDetailsList,
          testAttemptStatus: testAttemptStatus,
        )),
      );
    } else {
      print("Unable to fetch the test attempt status");
    }
    /*} else {
      print("Unable to fetch the test attempt status");

      // For demo purposes, create mock data if we can't fetch real data
      final mockTestAttemptStatus = TestAttemptStatus(
        testAttemptId: event.testAttemptId,
        testId: 'mock-test-id',
        subject: 'Mathematics',
        description: 'Full Mock Test',
        testType: TestTypes.fullMocktest,
        testStatus: 'completed',
        submittedAt: DateTime.now(),
        timeTakenInMinutes: 25,
        correctAnswers: 7,
        incorrectAnswers: 28,
        unattemptedQuestions: 0,
      );

      // Create mock question attempt data
      final mockQuestionAttemptDataList = List.generate(
        11,
        (index) => QuestionAttemptData(
          questionId: 'q-$index',
          questionTitle: 'Selective Ninja Entry Quiz',
          selectedAnswer: 'A',
          correctAnswer: index == 1 || index == 6 ? 'A' : 'B',
          isCorrect:
              index == 1 || index == 6, // Only questions 2 and 7 are correct
          order: index + 1,
          attemptStatus: true,
        ),
      );

      emit(FetchTestAttemptDetailsState(
        questionAttemptDataList: mockQuestionAttemptDataList,
        testAttemptStatus: mockTestAttemptStatus,
      ));
    }*/
  }

  FutureOr<void> _viewTestProgressEventHandler(
      ViewTestProgressEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    List<TestProgressSummary> testProgressSummaryList = _getTestProgressSummary(
        currentState.currentQuestion,
        currentState.testWithQuestionsData.testQuestionEntrySet,
        currentState.flaggedQuestions);

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    print("TestsBloc: Emitting TestProgressState with timestamp: $timestamp");

    print("TestsBloc: Current test status is: ${currentState.status}");

    /* final progressState = TestsDataState(
      currentQuestion: currentState.currentQuestion,
      testAttemptId: currentState.testAttemptId,
      test: currentState.test,
      status: TestStatus.inProgress,
      testWithQuestionsData: currentState.testWithQuestionsData,
      isLastQuestion: currentState.isLastQuestion,
      isFirstQuestion: currentState.isFirstQuestion,
      flaggedQuestions: currentState.flaggedQuestions,
    ); */

    // Create the state before emitting to log it
    final progressState = TestProgressState(
      testProgressSummaryList: testProgressSummaryList,
      currentQuestion: currentState.currentQuestion,
      testAttemptId: currentState.testAttemptId,
      testType: currentState.testType,
      test: currentState.test,
      status: currentState.status,
      testWithQuestionsData: currentState.testWithQuestionsData,
      isLastQuestion: currentState.isLastQuestion,
      isFirstQuestion: currentState.isFirstQuestion,
      flaggedQuestions: currentState.flaggedQuestions,
      timestamp: timestamp,
    );

    print("TestsBloc: Created TestProgressState: $progressState");
    print("TestsBloc: Current state before emit: ${state.runtimeType}");

    emit(progressState);

    print("TestsBloc: After emit, state is now: ${state.runtimeType}");
  }

  FutureOr<void> _viewTestFinishSummaryEventHandler(
      ViewTestFinishSummaryEvent event, Emitter<TestsStateInitial> emit) async {
    TestsDataState currentState = state as TestsDataState;

    emit(
      TestFinishSummaryState(
        currentQuestion: currentState.currentQuestion,
        testAttemptId: currentState.testAttemptId,
        testType: currentState.testType,
        test: currentState.test,
        status: currentState.status,
        testWithQuestionsData: currentState.testWithQuestionsData,
        isLastQuestion: currentState.isLastQuestion,
        isFirstQuestion: currentState.isFirstQuestion,
        flaggedQuestions: currentState.flaggedQuestions,
      ),
    );
  }

  List<TestProgressSummary> _getTestProgressSummary(
      TestQuestionEntry currentQuestion,
      Set<TestQuestionEntry> testQuestionEntrySet,
      List<FlaggedTestEntry> flaggedQuestions) {
    List<TestProgressSummary> testProgressSummaryList = [];
    for (var question in testQuestionEntrySet) {
      String questionStatus =
          question.attempted ? 'attempted' : 'not_attempted';

      //add logic for flagged questions
      for (var flaggedQuestion in flaggedQuestions) {
        if (flaggedQuestion.questionId == question.questionId) {
          questionStatus = 'flagged';
          break;
        }
      }

      questionStatus = question.questionId == currentQuestion.questionId
          ? 'current'
          : questionStatus;

      TestProgressSummary testProgressSummary = TestProgressSummary(
          questionId: question.questionId,
          order: question.order,
          questionStatus: questionStatus);
      testProgressSummaryList.add(testProgressSummary);
    }
    return testProgressSummaryList;
  }

  FutureOr<void> _fetchTestAttemptHistoryEventHandler(
      FetchTestAttemptHistoryEvent event,
      Emitter<TestsStateInitial> emit) async {
    final result = await _fetchTestAttemptHistory(event.testType);

    result.fold(
      (failure) => emit(
          FetchTestAttemptHistoryErrorState(message: failure.errorMessage)),
      (testAttemptHistory) => emit(
          FetchTestAttemptHistoryState(testAttemptHistory: testAttemptHistory)),
    );
  }

  FutureOr<void> _fetchGroupedTestAttemptHistoryEventHandler(
      FetchGroupedTestAttemptHistoryEvent event,
      Emitter<TestsStateInitial> emit) async {
    final result = await _fetchGroupedTestAttemptHistory(event.testType);

    result.fold(
      (failure) => emit(FetchGroupedSectionalTestAttemptHistoryErrorState(
          message: failure.errorMessage)),
      (groupedTestAttemptHistory) => emit(
          FetchGroupedSectionalTestAttemptHistoryState(
              groupedTestAttemptHistory: groupedTestAttemptHistory)),
    );
  }

  FutureOr<void> _fetchTestAttemptAnalysisEventHandler(
      FetchTestAttemptAnalysisEvent event,
      Emitter<TestsStateInitial> emit) async {
    final result = await _fetchTestAttemptAnalysis(TestAttemptAnalysisParams(
        testAttemptId: event.testAttemptId, testTypes: event.testType));

    result.fold(
      (failure) => emit(
          FetchTestAttemptAnalysisErrorState(message: failure.errorMessage)),
      (testAttemptAnalysis) => emit(FetchTestAttemptAnalysisState(
          testAttemptAnalysis: testAttemptAnalysis)),
    );
  }

  FutureOr<void> _fetchTestResultSummaryEventHandler(
      FetchTestResultSummaryEvent event,
      Emitter<TestsStateInitial> emit) async {
    final result = await _fetchTestResultSummary(TestResultSummaryParams(
        testAttemptId: event.testAttemptId, testTypes: event.testType));

    result.fold(
      (failure) =>
          emit(FetchTestResultSummaryErrorState(message: failure.errorMessage)),
      (testResultSummary) => emit(
          FetchTestResultSummaryState(testResultSummary: testResultSummary)),
    );
  }

  FutureOr<void> _fetchTestQuestionsWithAnswerStatusEventHandler(
      FetchTestQuestionsWithAnswerStatusEvent event,
      Emitter<TestsStateInitial> emit) async {
    final result = await _fetchTestQuestionsWithAnswerStatus(
        FetchTestQuestionsWithAnswerStatusParams(
            testAttemptId: event.testAttemptId, type: event.testType));

    result.fold(
      (failure) => emit(FetchTestQuestionsWithAnswerStatusErrorState(
          message: failure.errorMessage)),
      (testQuestionsWithAnswerStatus) => emit(
          FetchTestQuestionsWithAnswerStatusState(
              testQuestionsWithAnswerStatus: testQuestionsWithAnswerStatus)),
    );
  }

  FutureOr<void> _fetchQuestionDetailEventHandler(
      FetchQuestionDetailEvent event, Emitter<TestsStateInitial> emit) async {
    // emit(TestsLoadingState());

    // Get the test attempt details from the previous state or fetch them
    if (state is FetchTestAttemptDetailsState) {
      final currentState = state as FetchTestAttemptDetailsState;

      // Find the question at the specified index
      if (event.questionIndex > 0 &&
          event.questionIndex <= currentState.questionAttemptDataList.length) {
        final questionDetail = _convertToDetailedQuestionAttemptData(
            currentState.questionAttemptDataList[event.questionIndex - 1]);

        emit(QuestionDetailState(
          questionDetail: questionDetail,
          currentIndex: event.questionIndex,
          totalQuestions: currentState.questionAttemptDataList.length,
          testAttemptId: event.testAttemptId,
          allQuestions: currentState.questionAttemptDataList
              .map(_convertToDetailedQuestionAttemptData)
              .toList(),
        ));
      } else {
        emit(const TestDataErrorState(message: 'Question not found'));
      }
    } else {
      // If we don't have the test attempt details, fetch them first
      final result = await _fetchTestAttemptStatus(TestAttemptStatusParams(
          testAttemptId: event.testAttemptId, testTypes: event.testType));

      result.fold(
        (failure) => emit(TestDataErrorState(message: failure.errorMessage)),
        (testAttemptStatus) async {
          final detailsResult = await _fetchTestAttemptDetails(
              TestAttemptDetailsParams(
                  testAttemptId: event.testAttemptId,
                  testType: event.testType));

          detailsResult.fold(
            (failure) =>
                emit(TestDataErrorState(message: failure.errorMessage)),
            (questionAttemptDataList) {
              if (event.questionIndex > 0 &&
                  event.questionIndex <= questionAttemptDataList.length) {
                final questionDetail = _convertToDetailedQuestionAttemptData(
                    questionAttemptDataList[event.questionIndex - 1]);

                emit(QuestionDetailState(
                  questionDetail: questionDetail,
                  currentIndex: event.questionIndex,
                  totalQuestions: questionAttemptDataList.length,
                  testAttemptId: event.testAttemptId,
                  allQuestions: questionAttemptDataList
                      .map(_convertToDetailedQuestionAttemptData)
                      .toList(),
                ));
              } else {
                emit(TestDataErrorState(message: 'Question not found'));
              }
            },
          );
        },
      );
    }
  }

  FutureOr<void> _flagQuestionInResultEventHandler(
      FlagQuestionInResultEvent event, Emitter<TestsStateInitial> emit) async {
    // For now, just emit a success state
    // In a real implementation, this would update the flag status in the backend
    if (state is QuestionDetailState) {
      final currentState = state as QuestionDetailState;
      // Re-emit the same state for now
      emit(currentState);
    }
  }

  // Helper method to convert from simple QuestionAttemptData to detailed QuestionAttemptData
  detailed.QuestionAttemptData _convertToDetailedQuestionAttemptData(
      QuestionAttemptData simple) {
    return detailed.QuestionAttemptData(
      questionId: simple.questionId,
      questionTitle: 'Selective Ninja Entry Quiz', // Default title
      selectedAnswer: simple.selectedAnswer,
      correctAnswer: 'C', // Default correct answer for demo
      isCorrect: simple.isCorrect,
      order: 1, // Default order for demo
      attemptStatus: simple.attemptStatus,
    );
  }

  FutureOr<void> _fetchTodaysDailyTestEventHandler(
      FetchTodaysDailyTestEvent event, Emitter<TestsStateInitial> emit) async {
    try {
      final result = await _fetchTodaysDailyTest();

      result.fold(
        (failure) {
          emit(NewTestFetchErrorState(message: failure.errorMessage));
        },
        (test) {
          emit(
            NewTestFetchedState(
              test: test,
              testAttemptId: const Uuid().v4(),
              testType: TestTypes.daily,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      print("JB: Exception caught in _fetchTodaysDailyTestEventHandler: $e");
      print("JB: StackTrace: $stackTrace");
      emit(NewTestFetchErrorState(message: "Failed to fetch daily test: $e"));
    }
  }

  FutureOr<void> _checkIsDailyTestAttemptedEventHandler(
      CheckIsDailyTestAttemptedEvent event,
      Emitter<TestsStateInitial> emit) async {
    final result = await _isDailyTestAttempted();
    result.fold(
      (failure) => emit(TestDataErrorState(message: failure.errorMessage)),
      (isAttempted) =>
          emit(IsDailyTestAttemptedState(isAttempted: isAttempted)),
    );
  }
}
