part of 'tests_bloc.dart';

abstract class TestsEvent extends Equatable {
  const TestsEvent();
  @override
  List<Object> get props => [];
}

class TestsWithTypeEvent extends TestsEvent {
  final TestTypes testType;
  const TestsWithTypeEvent({required this.testType});

  @override
  List<Object> get props => [testType];
}

class FetchNewSectionalTestEvent extends TestsEvent {
  final String subject;
  const FetchNewSectionalTestEvent({required this.subject});

  @override
  List<Object> get props => [subject];
}

class RetakeSectionalTestEvent extends TestsEvent {
  final String subject;
  final String testId;
  final String testName;
  const RetakeSectionalTestEvent(
      {required this.subject, required this.testId, required this.testName});

  @override
  List<Object> get props => [subject, testId];
}

class FetchTodaysDailyTestEvent extends TestsEvent {
  const FetchTodaysDailyTestEvent();

  @override
  List<Object> get props => [];

  @override
  String toString() {
    return 'FetchTodaysDailyTestEvent';
  }
}

class MarkTestAsStartedEvent extends TestsWithTypeEvent {
  final String testAttemptId;

  const MarkTestAsStartedEvent({
    required super.testType,
    required this.testAttemptId,
  });

  @override
  List<Object> get props => [testType, testAttemptId];
}

class FetchTestDataEvent extends TestsEvent {
  final String testId;
  const FetchTestDataEvent({required this.testId});

  @override
  List<Object> get props => [testId];
}

class FetchNextQuestionEvent extends TestsEvent {
  final int currentOrder;
  const FetchNextQuestionEvent({
    required this.currentOrder,
  });

  @override
  List<Object> get props => [currentOrder];
}

class FetchPreviousQuestionEvent extends TestsEvent {
  final int currentOrder;
  const FetchPreviousQuestionEvent({
    required this.currentOrder,
  });

  @override
  List<Object> get props => [currentOrder];
}

class AnswerAQuestionEvent extends TestsEvent {
  final String questionId;
  final String selectedAnswer;
  const AnswerAQuestionEvent({
    required this.questionId,
    required this.selectedAnswer,
  });

  @override
  List<Object> get props => [questionId, selectedAnswer];
}

class FlagAQuestionEvent extends TestsEvent {
  final String questionId;
  const FlagAQuestionEvent({required this.questionId});

  @override
  List<Object> get props => [questionId];
}

class ViewFlaggedQuestionsEvent extends TestsEvent {}

class ViewSelectedQuestionEvent extends TestsEvent {
  final String questionId;
  const ViewSelectedQuestionEvent({required this.questionId});

  @override
  List<Object> get props => [questionId];
}

class SubmitTestEvent extends TestsEvent {}

class FetchTestAttemptStatusEvent extends TestsWithTypeEvent {
  final String testAttemptId;
  const FetchTestAttemptStatusEvent(
      {required this.testAttemptId, required super.testType});
}

//marked for past test
class FetchTestAttemptDetailsEvent extends TestsWithTypeEvent {
  final String testAttemptId;
  const FetchTestAttemptDetailsEvent(
      {required this.testAttemptId, required super.testType});
}

class ViewTestProgressEvent extends TestsEvent {}

class ViewTestFinishSummaryEvent extends TestsEvent {
  final Test test;

  const ViewTestFinishSummaryEvent({required this.test});

  @override
  List<Object> get props => [test];
}

class FetchQuestionDetailEvent extends TestsWithTypeEvent {
  final String testAttemptId;
  final int questionIndex;

  const FetchQuestionDetailEvent({
    required this.testAttemptId,
    required this.questionIndex,
    required super.testType,
  });

  @override
  List<Object> get props => [testAttemptId, questionIndex];
}

class FlagQuestionInResultEvent extends TestsEvent {
  final String questionId;

  const FlagQuestionInResultEvent({
    required this.questionId,
  });

  @override
  List<Object> get props => [questionId];
}

class TestEventWithType extends TestsEvent {
  final TestTypes testType;

  const TestEventWithType({required this.testType});

  @override
  List<Object> get props => [testType];
}

class FetchTestAttemptHistoryEvent extends TestEventWithType {
  const FetchTestAttemptHistoryEvent({required super.testType});
}

// marked for past test
class FetchGroupedTestAttemptHistoryEvent extends TestsWithTypeEvent {
  const FetchGroupedTestAttemptHistoryEvent({required super.testType});

  @override
  List<Object> get props => [testType];

  @override
  String toString() {
    return 'FetchGroupedTestAttemptHistoryEvent { testType: $testType }';
  }
}

class FetchTestAttemptAnalysisEvent extends TestEventWithType {
  final String testAttemptId;

  const FetchTestAttemptAnalysisEvent(
      {required this.testAttemptId, required super.testType});

  @override
  List<Object> get props => [testAttemptId];

  @override
  String toString() {
    return 'FetchTestAttemptAnalysisEvent { testAttemptId: $testAttemptId }';
  }
}

class FetchTestResultSummaryEvent extends TestsWithTypeEvent {
  final String testAttemptId;

  const FetchTestResultSummaryEvent(
      {required this.testAttemptId, required super.testType});

  @override
  List<Object> get props => [testAttemptId];

  @override
  String toString() {
    return 'FetchTestResultSummaryEvent { testAttemptId: $testAttemptId }';
  }
}

class FetchTestQuestionsWithAnswerStatusEvent extends TestsWithTypeEvent {
  final String testAttemptId;

  const FetchTestQuestionsWithAnswerStatusEvent(
      {required this.testAttemptId, required super.testType});

  @override
  List<Object> get props => [testAttemptId];

  @override
  String toString() {
    return 'FetchTestQuestionsWithAnswerStatusEvent { testAttemptId: $testAttemptId }';
  }
}

class ClearDataUnitTesting extends TestsEvent {}

class CheckIsDailyTestAttemptedEvent extends TestsEvent {}
