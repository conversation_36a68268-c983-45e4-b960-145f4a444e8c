import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';

import 'package:skillapp/core/common/widgets/question_image.dart';
import 'package:skillapp/core/common/widgets/question_image_animated.dart';
import 'package:skillapp/core/configs/configs.dart';

class QuestionDetailAnswerOptions extends StatelessWidget {
  final AnswerOptions answerOptions;
  final String correctAnswer;
  final String selectedAnswer;
  final bool isCorrect;

  const QuestionDetailAnswerOptions({
    super.key,
    required this.answerOptions,
    required this.correctAnswer,
    required this.selectedAnswer,
    required this.isCorrect,
  });

  @override
  Widget build(BuildContext context) {
    // Mock answer options based on the screenshot
    /*  final answerOptions = [
      {'id': 'A', 'text': '875 g'},
      {'id': 'B', 'text': '1175 g'},
      {'id': 'C', 'text': '1425 g'},
      {'id': 'D', 'text': '1525 g'},
      {'id': 'E', 'text': '2425 g'},


    ];*/

    /*  if (answerOptions.alignOptionsInRow != true) {
      return ListView.builder(
        itemCount: answerOptions.entries.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var answerOption = answerOptions.entries[index];
          return AnswerOptionsViewWidget(
            optionEntry: answerOption,
            distanceBetweenDisplayIdAndAnswer: 30,
          );
        },
      );
    }*/

    /* // Get the answer options
    for (var i = 0; i < questionData.answerOptions.entries.length; i++) {
      final option = questionData.answerOptions.entries[i];
      // Process option data

      String optionText = '';

      // Get the option text from the description data
      if (option.descAndImgDataWrapper.descAndImgDataList.isNotEmpty) {
        final descList = option.descAndImgDataWrapper.descAndImgDataList.first
            .descAndImgDataEntryList;
        if (descList.isNotEmpty) {
          optionText = descList.first.description;
        }
      }

      answerOptions.add(AnswerOption(
        id: option.id,
        //  label: String.fromCharCode(65 + i), // A, B, C, D, etc.
        label: option.displayId,
        text: optionText,
      ));
    }*/
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...answerOptions.entries.map((option) {
          final optionId = option.displayId;
          final optionText = option.descAndImgDataWrapper.descAndImgDataList
              .first.descAndImgDataEntryList.first.description;

          // Determine the state of this option

          final isUserSelected = selectedAnswer == optionId;
          final isCorrectAnswer = correctAnswer == optionId;
          final isUserCorrect = isCorrect;
          final isUserIncorrect = !isCorrect && isUserSelected;

          print("Option id: $optionId ");
          print("Is user selected: $isUserSelected ");
          print("Is correct answer: $isCorrectAnswer ");
          print("Is user correct: $isUserCorrect ");
          print("Is user incorrect: $isUserIncorrect ");
          print("************************************`");

          return QuestionDetailAnswerOptionItem(
            answerOption: option,
            optionId: optionId,
            optionText: optionText,
            isUserSelected: isUserSelected,
            isCorrectAnswer: isCorrectAnswer,
            isUserCorrect: isUserCorrect,
            isUserIncorrect: isUserIncorrect,
          );
        }),

        const SizedBox(height: 24),

        // Result indicator
        _buildResultIndicator(),
      ],
    );
  }

  Widget _buildResultIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      width: 300,
      decoration: BoxDecoration(
        //color: isCorrect ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCorrect ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isCorrect ? Icons.check_circle : Icons.cancel,
            color: isCorrect ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            isCorrect ? 'Correct' : 'Incorrect',
            style: TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class QuestionDetailAnswerOptionItem extends StatelessWidget {
  final OptionEntry answerOption;
  final String optionId;
  final String optionText;
  final bool isUserSelected;
  final bool isCorrectAnswer;
  final bool isUserCorrect;
  final bool isUserIncorrect;

  const QuestionDetailAnswerOptionItem({
    super.key,
    required this.optionId,
    required this.optionText,
    required this.isUserSelected,
    required this.isCorrectAnswer,
    required this.isUserCorrect,
    required this.isUserIncorrect,
    required this.answerOption,
  });

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color borderColor;
    Widget? trailingIcon;

    if (isCorrectAnswer) {
      backgroundColor = Color(0xFFD8EFD2);
      borderColor = Color(0xFF297A14);
      trailingIcon = const Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 20,
      );
    } else if (isUserSelected) {
      // User's selection (but not showing as incorrect, so must be correct)
      backgroundColor = Colors.white;
      borderColor = kSecondaryColor;
      trailingIcon = const Icon(
        Icons.cancel,
        color: Colors.red,
        size: 20,
      );
    } /*else if (isUserCorrect) {
      backgroundColor = Color(0xFFD8EFD2);
      borderColor = Color(0xFF297A14);
      trailingIcon = const Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 20,
      );
    } */
    else {
      // Default option
      backgroundColor = Colors.grey[100]!;
      borderColor = Colors.grey[300]!;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(
          color: borderColor,
          width: isCorrectAnswer || isUserSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Option label circle
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFFCAC6E1),
              border: Border.all(
                color: const Color(0xFFCAC6E1),
              ),
            ),
            child: Center(
              child: Text(
                optionId,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          Expanded(
            child: Container(
                child: answerOption.descAndImgDataWrapper.descAndImgDataList[0]
                            .descAndImgDataEntryList[0].type ==
                        'text'
                    ? Text(
                        answerOption.descAndImgDataWrapper.descAndImgDataList[0]
                            .descAndImgDataEntryList[0].description,
                        // textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: isCorrectAnswer || isUserSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      )
                    : answerOption.descAndImgDataWrapper.descAndImgDataList[0]
                                .descAndImgDataEntryList[0].isAnimated ==
                            true
                        ? Center(
                            child: ImageAnimatedWidget(
                              image1: answerOption
                                  .descAndImgDataWrapper
                                  .descAndImgDataList[0]
                                  .descAndImgDataEntryList[0]
                                  .imageDataList[0]
                                  .path,
                              image2: answerOption
                                  .descAndImgDataWrapper
                                  .descAndImgDataList[0]
                                  .descAndImgDataEntryList[0]
                                  .imageDataList[1]
                                  .path,
                              toolTip: answerOption
                                  .descAndImgDataWrapper
                                  .descAndImgDataList[0]
                                  .descAndImgDataEntryList[0]
                                  .imageDataList[0]
                                  .toolTip,
                            ),
                          )
                        : Center(
                            child: ImageWidget(
                              image: answerOption
                                  .descAndImgDataWrapper
                                  .descAndImgDataList[0]
                                  .descAndImgDataEntryList[0]
                                  .imageDataList[0]
                                  .path,
                              toolTip: answerOption
                                  .descAndImgDataWrapper
                                  .descAndImgDataList[0]
                                  .descAndImgDataEntryList[0]
                                  .imageDataList[0]
                                  .toolTip,
                            ),
                          )),
          ),

          // Trailing icon for correct/incorrect indication
          if (trailingIcon != null) ...[
            const SizedBox(width: 12),
            trailingIcon,
          ],
        ],
      ),
    );
  }
}
