import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class QuestionDetailFooter extends StatelessWidget {
  final int currentIndex;
  final int totalQuestions;
  final String testAttemptId;
  final String questionId;
  final List<QuestionWithAnswerStatus>? questionAttemptDataList;

  const QuestionDetailFooter({
    super.key,
    required this.currentIndex,
    required this.totalQuestions,
    required this.testAttemptId,
    required this.questionId,
    this.questionAttemptDataList,
    List<QuestionWithAnswerStatus>? allQuestions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 32,
            offset: Offset(0, 0),
            spreadRadius: 0,
          )
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous Button
          SizedBox(
            child: Row(
              children: [
                currentIndex > 1
                    ? SizedBox(
                        height: 40, // Increased height to 40
                        child: ElevatedButton(
                          onPressed: currentIndex > 1
                              ? () {
                                  if (questionAttemptDataList != null) {
                                    print(
                                        "CurrentIndex of the question is-->$currentIndex");
                                    print(
                                        "Next question index is -->${currentIndex - 1}");

                                    context.go(
                                        '/test-result/$testAttemptId/question/${currentIndex - 1}',
                                        extra: questionAttemptDataList);
                                  } else {
                                    context.go(
                                        '/test-result/$testAttemptId/question/${currentIndex - 1}');
                                  }
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            padding: const EdgeInsets.fromLTRB(16, 0, 24, 0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(28),
                              side: const BorderSide(color: kPrimaryColor),
                            ),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.arrow_back, // Leading icon
                                color: kPrimaryColor,
                              ),
                              SizedBox(width: 8), // Space between icon and text
                              Text(
                                'Previous',
                                style: TextStyle(
                                  color: kPrimaryColor,
                                  fontSize: 16,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : const SizedBox(width: 0),
                currentIndex > 1
                    ? const SizedBox(width: 60)
                    : const SizedBox(width: 0),
                currentIndex < totalQuestions
                    ? SizedBox(
                        height: 40, // Increased height to 40
                        child: ElevatedButton(
                          onPressed: currentIndex < totalQuestions
                              ? () {
                                  if (questionAttemptDataList != null) {
                                    context.go(
                                        '/test-result/$testAttemptId/question/${currentIndex + 1}',
                                        extra: questionAttemptDataList);
                                  } else {
                                    context.go(
                                        '/test-result/$testAttemptId/question/${currentIndex + 1}');
                                  }
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            padding: const EdgeInsets.fromLTRB(24, 0, 16, 0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(28),
                              side: const BorderSide(color: kPrimaryColor),
                            ),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Space between icon and text
                              Text(
                                'Next',
                                style: TextStyle(
                                  color: kPrimaryColor,
                                  fontSize: 16,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(width: 8),
                              Icon(
                                Icons.arrow_forward, // Leading icon
                                color: kPrimaryColor,
                              ),
                            ],
                          ),
                        ),
                      )
                    : const SizedBox(width: 0),
              ],
            ),
          ),

          // Next Button
          SizedBox(
            child: Row(
              children: [
                // Back to Results Button
                SizedBox(
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () {
                      context.go('/test-result/$testAttemptId');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kPrimaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(28), // Added black border
                      ),
                    ),
                    child: const Text(
                      'Back to Results',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
