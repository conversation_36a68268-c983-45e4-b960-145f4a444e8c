import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/src/test/domain/common/tests_config_cache.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/test_timer_widget.dart';

class TestScreenHeader extends StatelessWidget {
  final int currentQuestion;
  final int totalQuestions;
  final VoidCallback? onFullscreenToggle;
  final GlobalKey<TestTimerWidgetState>? timerKey;
  final String subject;
  final TestTypes testType;
  final bool isTestFlow;

  const TestScreenHeader({
    super.key,
    required this.currentQuestion,
    required this.totalQuestions,
    required this.subject,
    required this.testType,
    this.onFullscreenToggle,
    this.timerKey,
    this.isTestFlow = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 opacity = 26/255 alpha
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo and app name
          Row(
            children: [
              SvgPicture.asset(
                'assets/images/logo/logo.svg',
                height: 40,
                width: 40,
              ),
              const SizedBox(width: 12),
              const Text(
                "Selective Ninja",
                style: TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: kPrimaryColor,
                ),
              ),
            ],
          ),

          // Timer
          isTestFlow
              ? TestTimerWidget(
                  key: timerKey,

                  durationInMinutes: () {
                    final testsConfigCache = sl<TestsConfigCache>();

                    if (testsConfigCache
                        .getTestConfig(testType, subject)
                        .isEmpty) {
                      print(
                          "Alert: No data available in testconfig for testType $testType and Subject @$subject");
                      return 10;
                    } else {
                      return testsConfigCache
                          //  .getTestConfig(TestTypes.sectional, 'maths')
                          .getTestConfig(testType, subject)
                          .parameters
                          .maxAllowedTime;
                    }
                  }(),

                  //getTestConfig ,
                  // durationInMinutes: 60, //30, // Get from config
                  onTimerComplete: () {
                    // Auto-submit the test
                    final testsBloc = BlocProvider.of<TestsBloc>(context);
                    testsBloc.add(SubmitTestEvent());

                    // Get the current state to access testAttemptId
                    if (testsBloc.state is TestsDataState) {
                      final currentState = testsBloc.state as TestsDataState;
                      // Navigate to test result screen
                      context.go('/test-result/${currentState.testAttemptId}');
                    }
                  },
                )
              : Container(),

          // Question counter and fullscreen toggle
          Row(
            children: [
              Text(
                "Question $currentQuestion of $totalQuestions",
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(width: 16),
              IconButton(
                icon: const Icon(
                  Icons.list_alt,
                  color: Colors.black54,
                ),
                onPressed: onFullscreenToggle,
                tooltip:
                    isTestFlow ? 'View progress summary' : 'Navigate questions',
              ),
            ],
          ),
        ],
      ),
    );
  }
}
