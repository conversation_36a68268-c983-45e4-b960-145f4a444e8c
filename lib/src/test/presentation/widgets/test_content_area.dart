import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/common/widgets/web/web_description_image_display.dart';
import 'package:skillapp/src/test/presentation/widgets/answer_option.dart';
import 'package:skillapp/src/test/presentation/widgets/answer_options_widget.dart';

class TestContentArea extends StatelessWidget {
  final List<DescriptionAndImage> questionFullContext;
  final String questionContext;
  final String questionPrompt;
  final List<AnswerOption> answerOptions;
  final Widget? questionImage;
  final Function(String)? onAnswerSelected;
  final String?
      selectedAnswerId; // Not used anymore but kept for backward compatibility
  final bool showBulletPoints;
  final List<String> bulletPoints;

  const TestContentArea({
    super.key,
    required this.questionContext,
    required this.questionPrompt,
    required this.answerOptions,
    this.questionImage,
    this.onAnswerSelected,
    this.selectedAnswerId,
    this.showBulletPoints = false,
    this.bulletPoints = const [
      'Package 1 has a mass of 850g.',
      'Package 2 has a mass of 225 g.'
    ],
    required this.questionFullContext,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[100],
      padding: const EdgeInsets.all(16),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1600),
          child: Card(
            color: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Left column - Question context and image
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question context

                        WebDescriptionAndImageFormatDisplay(
                            descriptionAndImageDataList: questionFullContext),
                        // Show bullet points if enabled
                        if (showBulletPoints) ...[
                          const SizedBox(height: 24),
                          ...bulletPoints.map((point) {
                            return Column(
                              children: [
                                _buildBulletPoint(point, Colors.blue),
                                if (point != bulletPoints.last)
                                  const SizedBox(height: 8),
                              ],
                            );
                          }),
                        ]
                      ],
                    ),
                  ),
                ),

                // Vertical divider
                const VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: Colors.grey,
                ),

                // Right column - Question prompt and answer options
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question prompt
                        Text(
                          questionPrompt,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Answer options using the optimized widget
                        AnswerOptionsWidget(
                          answerOptions: answerOptions,
                          onAnswerSelected: (answerId) {
                            // if (onAnswerSelected != null) {
                            onAnswerSelected!(answerId);
                            // }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text, Color bulletColor) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 6, right: 8),
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: bulletColor,
            shape: BoxShape.circle,
          ),
        ),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // _buildAnswerOption method removed as it's replaced by AnswerOptionsWidget
}
