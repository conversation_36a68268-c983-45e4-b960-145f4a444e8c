import 'package:flutter/material.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class TestProgressSummaryModal extends StatelessWidget {
  final List<TestProgressSummary> testProgressSummaryList;
  final VoidCallback onClose;
  final Function(String) onQuestionSelected;

  const TestProgressSummaryModal({
    super.key,
    required this.testProgressSummaryList,
    required this.onClose,
    required this.onQuestionSelected,
  });

  @override
  Widget build(BuildContext context) {
    // Determine if we're in a dialog/panel or a full page based on the available width
    final isInPanel = MediaQuery.of(context).size.width > 1000;

    return Container(
      width: isInPanel ? 400 : double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isInPanel
            ? const BorderRadius.only(
                topLeft: Radius.circular(8),
                bottomLeft: Radius.circular(8),
              )
            : BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 10,
            offset: Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with close button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Progress Summary',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

          // Status legend
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildLegendItem('Answered', Colors.green),
                _buildLegendItem('Unanswered', Colors.grey.shade300),
                _buildLegendItem('Current', Colors.purple),
                _buildLegendItem('Flagged', Colors.amber),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Question grid
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  childAspectRatio: 1,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: testProgressSummaryList.length,
                itemBuilder: (context, index) {
                  final question = testProgressSummaryList[index];
                  return _buildQuestionIndicator(
                    question.order,
                    question.questionStatus,
                    () => onQuestionSelected(question.questionId),
                  );
                },
              ),
            ),
          ),

          // Back button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: OutlinedButton(
                onPressed: onClose,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Back'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildQuestionIndicator(
    int questionNumber,
    String status,
    VoidCallback onTap,
  ) {
    Color backgroundColor;
    Color textColor = Colors.white;

    switch (status) {
      case 'attempted':
        backgroundColor = Colors.green;
        break;
      case 'not_attempted':
        backgroundColor = Colors.grey.shade300;
        textColor = Colors.black;
        break;
      case 'current':
        backgroundColor = Colors.purple;
        break;
      case 'flagged':
        backgroundColor = Colors.amber;
        textColor = Colors.black;
        break;
      default:
        backgroundColor = Colors.grey.shade300;
        textColor = Colors.black;
    }

    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            questionNumber.toString(),
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
