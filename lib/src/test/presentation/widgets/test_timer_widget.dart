import 'dart:async';
import 'package:flutter/material.dart';

class TestTimerWidget extends StatefulWidget {
  final int durationInMinutes;
  final Function() onTimerComplete;
  final Function(Duration)? onTimerTick;

  const TestTimerWidget({
    super.key,
    required this.durationInMinutes,
    required this.onTimerComplete,
    this.onTimerTick,
  });

  @override
  TestTimerWidgetState createState() => TestTimerWidgetState();
}

class TestTimerWidgetState extends State<TestTimerWidget> {
  Timer? _timer;
  late Duration _remainingTime;

  @override
  void initState() {
    super.initState();
    print("JB:TestTimerWidgetState: initState ${widget.durationInMinutes}");
    _remainingTime = Duration(minutes: widget.durationInMinutes);
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime.inSeconds > 0) {
          _remainingTime = _remainingTime - const Duration(seconds: 1);
          if (widget.onTimerTick != null) {
            widget.onTimerTick!(_remainingTime);
          }
        } else {
          stopTimer(); // Use the stopTimer method to ensure proper cleanup
          widget.onTimerComplete();
        }
      });
    });
  }

  void stopTimer() {
    _timer?.cancel();
  }

  Duration getRemainingTime() {
    return _remainingTime;
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hours = _remainingTime.inHours;
    // Calculate minutes by rounding up - this ensures we show the current minute
    // the user is in rather than completed minutes
    final minutes = ((_remainingTime.inSeconds + 59) ~/ 60).remainder(60);
    final seconds = _remainingTime.inSeconds.remainder(60);

    String timerText;
    Color timerColor = const Color(0xFF0052B3); // kPrimaryColor;

    //  if (hours > 0) {
    if (minutes > 0 && minutes <= 1) {
      timerText = "00 Min ${seconds.toString().padLeft(2, '0')} Sec ";
    } else {
      timerText =
          "${hours.toString().padLeft(2, '0')} hr ${minutes.toString().padLeft(2, '0')} Min";
    }
    //  } else {
    //   timerText = "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
    // }

    // Change color based on remaining time
    /* if (minutes < 5) {
      timerColor = Colors.red;
    } else if (minutes < 10) {
      timerColor = Colors.orange;
    }*/

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.timer,
            color: timerColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            timerText,
            style: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: timerColor == Colors.red ? Colors.red : timerColor,
            ),
          ),
        ],
      ),
    );
  }
}
