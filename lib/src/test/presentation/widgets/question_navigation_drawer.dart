import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';

class QuestionNavigationDrawer extends StatelessWidget {
  const QuestionNavigationDrawer({
    super.key,
    required this.questionAttemptDataList,
    required this.currentQuestionIndex,
    required this.testAttemptId,
    required this.onQuestionSelected,
    required GlobalKey<ScaffoldState> scaffoldKey,
  }) : _scaffoldKey = scaffoldKey;

  final List<QuestionWithAnswerStatus> questionAttemptDataList;
  final int currentQuestionIndex;
  final String testAttemptId;
  final Function(int) onQuestionSelected;
  final GlobalKey<ScaffoldState> _scaffoldKey;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final drawerWidth = screenWidth * 0.4; // 40% of screen width

    return Drawer(
      backgroundColor: Colors.white,
      elevation: 0.2,
      width: drawerWidth,
      child: Stack(
        children: [
          // Scrollable content with question grid
          SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(bottom: 24),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(width: 1, color: Color(0xFFCCCCCC)),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Question Navigation',
                          style: TextStyle(
                            color: Color(0xFF121212),
                            fontSize: 20,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            _scaffoldKey.currentState!.closeEndDrawer();
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Status legend
                  _buildStatusLegend(),

                  const SizedBox(height: 24),

                  // Question grid
                  _buildQuestionGrid(context),
                ],
              ),
            ),
          ),

          // Footer with back button
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Color(0x1E000000),
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      _scaffoldKey.currentState!.closeEndDrawer();
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: kPrimaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                    child: const Text(
                      'Back',
                      style: TextStyle(
                        color: kPrimaryColor,
                        fontFamily: 'Poppins',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildLegendItem('Current', kWebTestCurrentBgColor),
        _buildLegendItem('Correct', Colors.green.shade100),
        _buildLegendItem('Incorrect', Colors.red.shade100),
        _buildLegendItem('Unanswered', kWebTestUnAnsweredBgColor),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        mainAxisExtent: 44,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: questionAttemptDataList.length,
      itemBuilder: (context, index) {
        final question = questionAttemptDataList[index];
        return _buildQuestionItem(context, question, index + 1);
      },
    );
  }

  Widget _buildQuestionItem(
      BuildContext context, QuestionWithAnswerStatus question, int number) {
    Color backgroundColor;
    Color textColor;
    bool isCurrent = number == currentQuestionIndex;

    // Determine the background color based on the question status
    if (isCurrent) {
      backgroundColor = kWebTestCurrentBgColor;
      textColor = Colors.white;
    } else if (question.answerStatus == QuestionAnswerStatus.correct) {
      backgroundColor = Colors.green.shade100;
      textColor = Colors.green.shade800;
    } else if (question.answerStatus == QuestionAnswerStatus.incorrect) {
      backgroundColor = Colors.red.shade100;
      textColor = Colors.red.shade800;
    } else {
      backgroundColor = kWebTestUnAnsweredBgColor;
      textColor = const Color(0xFF808080);
    }

    return InkWell(
      onTap: () => onQuestionSelected(number),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            number.toString(),
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }
}
