import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/answer_option.dart';

class AnswerOptionsWidget extends StatelessWidget {
  final List<AnswerOption> answerOptions;
  final Function(String) onAnswerSelected;

  const AnswerOptionsWidget({
    super.key,
    required this.answerOptions,
    required this.onAnswerSelected,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TestsBloc, TestsStateInitial>(
      listener: (context, state) {
        print(
            "AnswerOptionsWidget: Listener called with state: ${state.runtimeType}");
      },
      builder: (context, state) {
        if (state is TestsDataState || state is AnswererCapturedState) {
          final currentQuestion = state is TestsDataState
              ? state.currentQuestion
              : (state as AnswererCapturedState).currentQuestion;

          // Get the selected answer
          final selectedAnswerId = currentQuestion.selectedAnswer.isNotEmpty
              ? currentQuestion.selectedAnswer
              : null;

          // Build the answer options
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: answerOptions.map((option) {
              final isSelected = option.id == selectedAnswerId;

              return AnswerOptionItem(
                option: option,
                isSelected: isSelected,
                onTap: () => onAnswerSelected(option.id),
              );
            }).toList(),
          );
        }

        // Fallback for other states
        return const SizedBox.shrink();
      },
    );
  }
}

class AnswerOptionItem extends StatelessWidget {
  final AnswerOption option;
  final bool isSelected;
  final VoidCallback onTap;

  const AnswerOptionItem({
    super.key,
    required this.option,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Implement the answer option UI with selection state
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.grey[100],
          border: Border.all(
            color:
                isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    isSelected ? Theme.of(context).primaryColor : Colors.white,
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.grey[400]!,
                ),
              ),
              child: Center(
                child: Text(
                  option.label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                option.text,
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
