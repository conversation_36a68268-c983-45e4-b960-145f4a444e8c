import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class TestFooter extends StatelessWidget {
  final VoidCallback? onBack;
  final VoidCallback? onFlag;
  final VoidCallback? onNext;
  final bool isLastQuestion;

  const TestFooter({
    super.key,
    this.onBack,
    this.onFlag,
    this.onNext,
    this.isLastQuestion = false,
  });

  @override
  Widget build(BuildContext context) {
    // Use Material widget with controlled elevation
    return Material(
      color: Colors.white,
      elevation: 2, // Reduced elevation for subtler shadow
      child: Container(
        width: double.infinity, // Takes width of parent (which is constrained)
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back button
            SizedBox(
              height: 48,
              child: OutlinedButton.icon(
                onPressed: onBack,
                icon: const Icon(
                  Icons.chevron_left,
                ),
                label: const Text(
                  'Back',
                  style: TextStyle(
                    color: kPrimaryColor,
                    fontSize: 16,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  // padding:const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  padding: const EdgeInsets.fromLTRB(16, 9, 24, 10),
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(
                      width: 1,
                      color: kPrimaryColor,
                    ),
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
              ),
            ),

            // Right side buttons
            Row(
              children: [
                // Flag button
                BlocBuilder<TestsBloc, TestsStateInitial>(
                  builder: (context, state) {
                    bool isFlagged = false;

                    //state is QuestionFlagSuccessState

                    if (state is TestsDataState) {
                      TestQuestionEntry currentQuestion = state.currentQuestion;
                      for (var flaggedQuestion in state.flaggedQuestions) {
                        if (flaggedQuestion.questionId ==
                            currentQuestion.questionId) {
                          isFlagged = flaggedQuestion.isFlagged;
                        }
                      }
                    }

                    return SizedBox(
                      height: 48,
                      child: OutlinedButton.icon(
                        onPressed: onFlag,
                        icon: isFlagged
                            ? const Icon(Icons.flag_rounded)
                            : const Icon(Icons.flag_outlined),
                        iconAlignment: IconAlignment.end,
                        label: Text(
                          isFlagged ? 'Unflag' : 'Flag',
                          style: const TextStyle(
                            color: kPrimaryColor,
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.fromLTRB(24, 9, 16, 10),
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(
                              width: 1,
                              color: kPrimaryColor,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(width: 32),

                // Next/Finish button
                SizedBox(
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: onNext,
                    iconAlignment: IconAlignment.end,
                    icon: Icon(
                      isLastQuestion ? Icons.check : Icons.chevron_right,
                      color: Colors.white,
                    ),
                    label: Text(
                      isLastQuestion ? 'Finish' : 'Next',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.fromLTRB(24, 9, 16, 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
