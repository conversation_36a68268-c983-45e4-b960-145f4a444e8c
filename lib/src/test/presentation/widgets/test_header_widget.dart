import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skillapp/core/configs/configs.dart';

class TestHeaderWidget extends StatelessWidget {
  final bool automaticallyImplyLeading;
  final List<Widget>? actions;

  const TestHeaderWidget({
    super.key,
    this.automaticallyImplyLeading = false,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: Padding(
        padding: const EdgeInsets.fromLTRB(24, 20, 24, 10),
        child: Row(
          children: [
            SvgPicture.asset(
              'assets/images/logo/logo.svg',
              height: 60,
              width: 50,
            ),
            const SizedBox(width: 12),
            const Text(
              "Selective Ninja",
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: kPrimaryColor,
              ),
            ),
          ],
        ),
      ),
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: actions,
    );
  }
}
