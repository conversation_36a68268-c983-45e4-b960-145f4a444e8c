import 'package:flutter/material.dart';
import 'package:skillapp/core/common/widgets/web/web_description_image_display.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/widgets/question_detail_answer_options.dart';

class QuestionDetailContentArea extends StatelessWidget {
  final QuestionWithAnswerStatus questionDetail;

  const QuestionDetailContentArea({
    super.key,
    required this.questionDetail,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[100],
      padding: const EdgeInsets.all(16),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1600),
          child: Card(
            color: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Left side - Question content (similar to TestContentArea)
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        WebDescriptionAndImageFormatDisplay(
                            descriptionAndImageDataList: questionDetail
                                .questionAndAnswers
                                .question
                                .descAndImgDataList),
                      ],
                    ),
                  ),
                ),

                // Vertical divider
                const VerticalDivider(
                  width: 1,
                  thickness: 1,
                  color: Colors.grey,
                ),

                // Right side - Question and answer options
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Question

                        const SizedBox(height: 24),

                        QuestionDetailAnswerOptions(
                          answerOptions:
                              questionDetail.questionAndAnswers.answerOptions,
                          correctAnswer: questionDetail.correctAnswer,
                          selectedAnswer: questionDetail.selectedAnswer,
                          isCorrect: questionDetail.answerStatus ==
                              QuestionAnswerStatus.correct,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
