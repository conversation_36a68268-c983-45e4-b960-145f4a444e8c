import 'package:flutter/material.dart';
import 'package:skillapp/src/test/presentation/widgets/test_header_widget.dart';

class TestLayoutWidget extends StatelessWidget {
  final Widget child;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;

  const TestLayoutWidget({
    super.key,
    required this.child,
    this.actions,
    this.automaticallyImplyLeading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: TestHeaderWidget(
          automaticallyImplyLeading: automaticallyImplyLeading,
          actions: actions,
        ),
      ),
      body: child,
    );
  }
}
