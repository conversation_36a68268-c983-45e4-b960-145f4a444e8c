import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class TestErrorWidget extends StatelessWidget {
  final String errorMessage;
  final String? title;
  final VoidCallback? onRetry;
  final String? retryButtonText;
  final String? homeButtonText;
  final bool showRetryButton;
  final bool showHomeButton;
  final IconData? errorIcon;
  final Color? errorIconColor;
  final double? iconSize;

  const TestErrorWidget({
    super.key,
    required this.errorMessage,
    this.title,
    this.onRetry,
    this.retryButtonText = 'Retry',
    this.homeButtonText = 'Return to Home',
    this.showRetryButton = false,
    this.showHomeButton = true,
    this.errorIcon = Icons.error_outline,
    this.errorIconColor = Colors.red,
    this.iconSize = 64,
  });

  /// Factory constructor for daily test errors
  factory TestErrorWidget.dailyTest({
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    return TestErrorWidget(
      errorMessage: errorMessage ??
          'Daily test not available currently. Please try after sometime.',
      title: 'Daily Test Not Available',
      onRetry: onRetry,
      showRetryButton: onRetry != null,
      showHomeButton: true,
    );
  }

  /// Factory constructor for sectional test errors
  factory TestErrorWidget.sectionalTest({
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    return TestErrorWidget(
      errorMessage: errorMessage ??
          'Sectional test not available currently. Please try after sometime.',
      title: 'Sectional Test Not Available',
      onRetry: onRetry,
      showRetryButton: onRetry != null,
      showHomeButton: true,
    );
  }

  /// Factory constructor for general test errors
  factory TestErrorWidget.general({
    required String errorMessage,
    String? title,
    VoidCallback? onRetry,
  }) {
    return TestErrorWidget(
      errorMessage: errorMessage,
      title: title ?? 'Test Error',
      onRetry: onRetry,
      showRetryButton: onRetry != null,
      showHomeButton: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        margin: const EdgeInsets.all(24),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 500,
          padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Error Icon
              Icon(
                errorIcon,
                color: errorIconColor,
                size: iconSize,
              ),
              const SizedBox(height: 16),

              // Title (if provided)
              if (title != null) ...[
                Text(
                  title!,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
              ],

              // Error Message
              Text(
                errorMessage,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Retry Button (if enabled and callback provided)
                  if (showRetryButton && onRetry != null) ...[
                    OutlinedButton(
                      onPressed: onRetry,
                      child: Text(retryButtonText!),
                    ),
                    if (showHomeButton) const SizedBox(width: 16),
                  ],

                  // Home Button (if enabled)
                  if (showHomeButton)
                    SizedBox(
                      height: 48,
                      child: ElevatedButton.icon(
                        onPressed: () => context.go('/home'),
                        iconAlignment: IconAlignment.end,
                        icon: const Icon(
                          Icons.home,
                          color: Colors.white,
                          size: 18,
                        ),
                        label: Text(
                          homeButtonText!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          padding: const EdgeInsets.fromLTRB(24, 9, 16, 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
