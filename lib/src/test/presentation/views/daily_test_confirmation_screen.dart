import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/services/injection_container.dart' as di;
import 'package:skillapp/src/test/domain/common/tests_config_cache.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/test_layout_widget.dart';
import 'package:skillapp/src/test/presentation/widgets/test_error_widget.dart';

class DailyTestConfirmationScreen extends StatelessWidget {
  const DailyTestConfirmationScreen({super.key});

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    return const DailyTestConfirmationScreen();
  }

  // Helper method to get test duration from config
  int _getTestDuration(String subject) {
    try {
      final testsConfigCache = di.sl<TestsConfigCache>();
      final testConfig = testsConfigCache.getTestConfig(
        TestTypes.daily,
        subject,
      );
      return testConfig.parameters.maxAllowedTime; // Convert to minutes
    } catch (e) {
      debugPrint('Error getting test config: $e');
      return 30; // Default fallback value for daily tests
    }
  }

  @override
  Widget build(BuildContext context) {
    return TestLayoutWidget(
      automaticallyImplyLeading: false,
      child: Container(
        color: Colors.grey[100],
        child: Center(
          child: BlocBuilder<TestsBloc, TestsStateInitial>(
            builder: (context, state) {
              // Loading state
              if (state is! NewTestFetchedState &&
                  state is! NewTestFetchErrorState) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Loading daily test...'),
                    ],
                  ),
                );
              }

              // Error state
              if (state is NewTestFetchErrorState) {
                print(
                    'DailyTestConfirmationScreen: Error state - ${state.message}');
                return TestErrorWidget.dailyTest(
                  errorMessage: state.message,
                  onRetry: () {
                    // Retry fetching the daily test
                    context
                        .read<TestsBloc>()
                        .add(const FetchTodaysDailyTestEvent());
                  },
                );
              }

              // Success state - show confirmation screen
              if (state is NewTestFetchedState &&
                  state.testType == TestTypes.daily) {
                final test = state.test;
                final testAttemptId = state.testAttemptId;
                final durationMinutes = _getTestDuration(test.subject);

                return Card(
                  margin: const EdgeInsets.all(24),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Container(
                    width: 500,
                    padding: const EdgeInsets.symmetric(
                        vertical: 40, horizontal: 24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Daily test icon
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.orange[50],
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.today,
                            size: 40,
                            color: Colors.orange[600],
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Test title
                        const Text(
                          'Daily Practice Test',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),

                        // Test name and subject
                        Text(
                          test.description.isNotEmpty
                              ? test.description
                              : 'Daily ${test.subject.toUpperCase()} Test',
                          style: const TextStyle(
                            fontSize: 18,
                            color: Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),

                        // Subject badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.blue[200]!),
                          ),
                          child: Text(
                            'Subject: ${test.subject.toUpperCase()}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Test details
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'Questions:',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  Text(
                                    '${test.questionWithOrderList.length}',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'Duration:',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  Text(
                                    '$durationMinutes minutes',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),
                        const Divider(height: 1),
                        const SizedBox(height: 24),

                        // Confirmation message

                        const SizedBox(height: 16),

                        // Info message
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.orange[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange[200]!),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.orange[100],
                                ),
                                child: Icon(
                                  Icons.info_outline,
                                  color: Colors.orange[700],
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  'Complete your daily test to maintain your streak!',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            OutlinedButton(
                              onPressed: () {
                                // Go back to home
                                context.go('/home');
                              },
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                side: BorderSide(color: Colors.grey[400]!),
                              ),
                              child: const Text('Cancel',
                                  style: TextStyle(color: Colors.black87)),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              onPressed: () {
                                // Navigate to the actual test screen
                                context.go('/test/${test.testId}', extra: {
                                  'test': test,
                                  'testAttemptId': testAttemptId,
                                  'testType': TestTypes.daily,
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange[600],
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                              child: const Text(
                                'Start Test',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Fallback (should never reach here)
              return const Text('Unexpected state');
            },
          ),
        ),
      ),
    );
  }
}
