import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/test_screen_header.dart';
import 'package:skillapp/src/test/presentation/widgets/question_detail_content_area.dart';
import 'package:skillapp/src/test/presentation/widgets/question_detail_footer.dart';
import 'package:skillapp/src/test/presentation/widgets/question_navigation_drawer.dart';
import 'package:skillapp/src/test/presentation/widgets/test_error_widget.dart';

class QuestionDetailScreen extends StatefulWidget {
  final String testAttemptId;
  final int questionIndex;
  final List<QuestionWithAnswerStatus>? questionAttemptDataList;

  const QuestionDetailScreen({
    super.key,
    required this.testAttemptId,
    required this.questionIndex,
    this.questionAttemptDataList,
  });

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    final testAttemptId = state.pathParameters['testAttemptId']!;
    final questionIndex = int.parse(state.pathParameters['questionIndex']!);
    final questionAttemptDataList =
        state.extra as List<QuestionWithAnswerStatus>?;

    print('QuestionDetailScreen.routeBuilder called');
    print('testAttemptId: $testAttemptId');
    print('questionIndex: $questionIndex');
    print(
        'questionAttemptDataList length: ${questionAttemptDataList?.length ?? 'null'}');

    return QuestionDetailScreen(
      testAttemptId: testAttemptId,
      questionIndex: questionIndex,
      questionAttemptDataList: questionAttemptDataList,
    );
  }

  @override
  State<QuestionDetailScreen> createState() => _QuestionDetailScreenState();
}

class _QuestionDetailScreenState extends State<QuestionDetailScreen> {
  QuestionWithAnswerStatus? currentQuestion;
  List<QuestionWithAnswerStatus> allQuestions = [];

  // Scaffold key to control the drawer
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _updateCurrentQuestion();
  }

  @override
  void didUpdateWidget(QuestionDetailScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the question index or data has changed
    if (oldWidget.questionIndex != widget.questionIndex ||
        oldWidget.questionAttemptDataList != widget.questionAttemptDataList) {
      _updateCurrentQuestion();
    }
  }

  void _updateCurrentQuestion() {
    if (widget.questionAttemptDataList != null &&
        widget.questionAttemptDataList!.isNotEmpty) {
      // Use passed data directly
      allQuestions = widget.questionAttemptDataList!;

      // Validate question index
      if (widget.questionIndex >= 1 &&
          widget.questionIndex <= allQuestions.length) {
        currentQuestion = allQuestions[widget.questionIndex - 1];
        print(
            'Successfully loaded question ${widget.questionIndex} of ${allQuestions.length}');
      } else {
        print(
            'Invalid question index: ${widget.questionIndex}, total questions: ${allQuestions.length}');
        currentQuestion = null;
      }
    } else {
      // Fallback: Fetch question detail when screen loads
      print('No question data provided, fetching from bloc...');
      context.read<TestsBloc>().add(FetchQuestionDetailEvent(
            testAttemptId: widget.testAttemptId,
            questionIndex: widget.questionIndex,
            testType:
                TestTypes.sectional, // TODO - Need to change for daily test
          ));
    }
  }

  Widget _buildEndDrawer() {
    if (widget.questionAttemptDataList == null || allQuestions.isEmpty) {
      return Container(); // Empty drawer when no data
    }

    return QuestionNavigationDrawer(
      questionAttemptDataList: allQuestions,
      currentQuestionIndex: widget.questionIndex,
      testAttemptId: widget.testAttemptId,
      onQuestionSelected: (questionIndex) {
        // Navigate to the selected question
        context.go(
          '/test-result/${widget.testAttemptId}/question/$questionIndex',
          extra: widget.questionAttemptDataList,
        );

        // Close the drawer
        _scaffoldKey.currentState!.closeEndDrawer();
      },
      scaffoldKey: _scaffoldKey,
    );
  }

  @override
  Widget build(BuildContext context) {
    print('QuestionDetailScreen.build called');
    print(
        'questionAttemptDataList is null: ${widget.questionAttemptDataList == null}');
    print('allQuestions length: ${allQuestions.length}');
    print('currentQuestion is null: ${currentQuestion == null}');

    // If we have data directly, show it immediately
    if (widget.questionAttemptDataList != null) {
      return _buildQuestionDetailView();
    }

    // Otherwise, use BlocBuilder for fetched data
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocBuilder<TestsBloc, TestsStateInitial>(
        builder: (context, state) {
          if (state is TestsLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is TestDataErrorState) {
            return Center(
              child: Text('Error: ${state.message}'),
            );
          }

          if (state is QuestionDetailState) {
            return Column(
              children: [
                // Header without timer
                TestScreenHeader(
                  currentQuestion: state.currentIndex,
                  totalQuestions: state.totalQuestions,
                  isTestFlow: false, // No timer for result view
                  subject: 'NA',
                  testType: TestTypes.notAvailable,
                  onFullscreenToggle: () {
                    // For BlocBuilder path, we don't have question list data
                    // So we'll show a simple message or disable the functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Question navigation not available in this mode'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                ),

                // Content area
                Expanded(
                  child: Stack(children: [
                    Positioned.fill(
                      bottom: 80,
                      child: QuestionDetailContentArea(
                        questionDetail: _convertToDetailedQuestionAttemptData(
                            state.questionDetail),
                      ),
                    ),
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 3,
                      child: Container(
                        color: Colors.grey.shade100,
                        child: Center(
                          child: Container(
                            constraints: const BoxConstraints(maxWidth: 1600),
                            child: QuestionDetailFooter(
                              currentIndex: state.currentIndex,
                              totalQuestions: state.totalQuestions,
                              testAttemptId: state.testAttemptId,
                              questionId: state.questionDetail.questionId,
                              //    allQuestions: allQuestions,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ]),
                ),
                const SizedBox(
                  height: 80,
                )
                // Footer with navigation
              ],
            );
          }

          return const Center(
            child: Text('Loading question details...'),
          );
        },
      ),
    );
  }

  Widget _buildQuestionDetailView() {
    // Ensure we have valid data and index
    if (allQuestions.isEmpty ||
        widget.questionIndex < 1 ||
        widget.questionIndex > allQuestions.length ||
        currentQuestion == null) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: TestErrorWidget(
          errorMessage:
              'Invalid question index: ${widget.questionIndex}. Total questions: ${allQuestions.length}',
          title: 'Question Not Found',
          onRetry: () => context.go('/test-result/${widget.testAttemptId}'),
          retryButtonText: 'Back to Results',
          showRetryButton: true,
          showHomeButton: false,
        ),
      );
    }

    // Get the current question based on the current index
    final currentQuestionData = currentQuestion!;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      endDrawer: _buildEndDrawer(),
      body: Column(
        children: [
          // Header without timer
          TestScreenHeader(
            currentQuestion: widget.questionIndex,
            totalQuestions: allQuestions.length,
            isTestFlow: false,
            subject: 'NA',
            testType: TestTypes.notAvailable,
            onFullscreenToggle: () {
              // Open the question navigation drawer
              _scaffoldKey.currentState!.openEndDrawer();
            }, // No timer for result view
          ),

          // Content area
          Expanded(
            child: Stack(children: [
              Positioned.fill(
                bottom: 80,
                child: QuestionDetailContentArea(
                  questionDetail: _convertToDetailedQuestionAttemptData(
                      currentQuestionData),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  color: Colors.grey.shade100,
                  child: Center(
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 1600),
                      child: QuestionDetailFooter(
                        currentIndex: widget.questionIndex,
                        totalQuestions: allQuestions.length,
                        testAttemptId: widget.testAttemptId,
                        questionId: currentQuestionData.questionId,
                        questionAttemptDataList: widget.questionAttemptDataList,
                      ),
                    ),
                  ),
                ),
              ),
            ]),
          ),
        ],
      ),
    );
  }

  // Helper method to convert simple QuestionAttemptData to detailed format for display
  dynamic _convertToDetailedQuestionAttemptData(dynamic question) {
    // For now, just return the question as-is since we're using the simple format
    // In a real implementation, you might need to convert between different data models

    print("Question detail is ${question}");

    //  Question detail is QuestionAttemptData { questionId: MATHS04, selectedAnswer: C, isCorrect: true, attemptStatus: true }

    return question;
  }
}
