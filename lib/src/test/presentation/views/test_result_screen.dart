import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/test_error_widget.dart';

class TestResultScreen extends StatefulWidget {
  final String testAttemptId;

  const TestResultScreen({
    super.key,
    required this.testAttemptId,
  });

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    final String testAttemptId = state.pathParameters['testAttemptId'] ?? '';

    return TestResultScreen(
      testAttemptId: testAttemptId,
    );
  }

  @override
  State<TestResultScreen> createState() => _TestResultScreenState();
}

class _TestResultScreenState extends State<TestResultScreen> {
  // Local state to store data from all three events
  TestQuestionsWithAnswerStatus? testQuestionsWithAnswerStatus;
  TestResultSummary? testResultSummary;
  TestAttemptAnalysis? testAttemptAnalysis;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    // Fetch both test data when the screen loads
    _fetchTestData();
  }

  void _fetchTestData() {
    // Call all three events
    context.read<TestsBloc>().add(
          FetchTestQuestionsWithAnswerStatusEvent(
            testAttemptId: widget.testAttemptId,
            testType: TestTypes.sectional, // TODO- Update this for daily tests
          ),
        );

    context.read<TestsBloc>().add(
          FetchTestResultSummaryEvent(
            testAttemptId: widget.testAttemptId,
            testType: TestTypes.sectional, // TODO- Update this for daily tests
          ),
        );

    context.read<TestsBloc>().add(
          FetchTestAttemptAnalysisEvent(
            testAttemptId: widget.testAttemptId,
            testType: TestTypes.sectional, // TODO- Update this for daily tests
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TestsBloc, TestsStateInitial>(
      listener: (context, state) {
        if (state is FetchTestQuestionsWithAnswerStatusState) {
          setState(() {
            testQuestionsWithAnswerStatus = state.testQuestionsWithAnswerStatus;
            _checkIfDataComplete();
          });
        } else if (state is FetchTestResultSummaryState) {
          setState(() {
            testResultSummary = state.testResultSummary;
            _checkIfDataComplete();
          });
        } else if (state is FetchTestAttemptAnalysisState) {
          setState(() {
            testAttemptAnalysis = state.testAttemptAnalysis;
            _checkIfDataComplete();
          });
        } else if (state is FetchTestQuestionsWithAnswerStatusErrorState) {
          setState(() {
            errorMessage = state.message;
            isLoading = false;
          });
        } else if (state is FetchTestResultSummaryErrorState) {
          setState(() {
            errorMessage = state.message;
            isLoading = false;
          });
        } else if (state is FetchTestAttemptAnalysisErrorState) {
          setState(() {
            errorMessage = state.message;
            isLoading = false;
          });
        }
      },
      child: _buildContent(),
    );
  }

  void _checkIfDataComplete() {
    if (testQuestionsWithAnswerStatus != null &&
        testResultSummary != null &&
        testAttemptAnalysis != null) {
      setState(() {
        isLoading = false;
      });
    }
  }

  Widget _buildContent() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (errorMessage != null) {
      return TestErrorWidget.general(
        errorMessage: errorMessage!,
        title: 'Test Result Error',
        onRetry: () {
          setState(() {
            isLoading = true;
            errorMessage = null;
            testQuestionsWithAnswerStatus = null;
            testResultSummary = null;
            testAttemptAnalysis = null;
          });
          _fetchTestData();
        },
      );
    }

    if (testQuestionsWithAnswerStatus != null) {
      return _buildTestResultView();
    }

    return const Center(
      child: Text('No data available'),
    );
  }

  Widget _buildTestResultView() {
    final testAttemptStatus = testQuestionsWithAnswerStatus!;
    final questionAttemptDataList = testAttemptStatus.questions;
    final summary = testResultSummary;

    // Get score from test attempt status or summary
    final correctAnswers =
        summary?.correctAnswers ?? testAttemptStatus.correctAnswers;
    final totalQuestions = summary?.totalQuestions ??
        (correctAnswers +
            testAttemptStatus.incorrectAnswers +
            testAttemptStatus.unansweredQuestions);
    final scorePercentage = summary?.totalPercentage.round() ??
        (totalQuestions > 0
            ? (correctAnswers / totalQuestions * 100).round()
            : 0);

    // Use summary data if available, otherwise fallback to current date/time
    final DateTime submittedAt = summary?.endTime ?? DateTime.now();
    final submissionDate = DateFormat('E, dd MMM yyyy').format(submittedAt);
    final submissionTime = DateFormat('h:mm a').format(submittedAt);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary section with enhanced data

            _buildSummarySection(
              submissionDate: submissionDate,
              submissionTime: submissionTime,
              correctAnswers: correctAnswers,
              totalQuestions: totalQuestions,
              scorePercentage: scorePercentage,
              summary: summary,
            ),

            // Total Percentage and Time Spent cards
            _buildPerformanceCards(
              scorePercentage: scorePercentage,
              summary: summary,
            ),

            // Test Results breakdown
            _buildTestResultsBreakdown(
              testAttemptStatus: testAttemptStatus,
              totalQuestions: totalQuestions,
            ),

            // Test Analysis section
            if (testAttemptAnalysis != null)
              _buildTestAnalysisSection(testAttemptAnalysis!),

            // Test result details
            _buildTestResultDetails(
              testAttemptStatus: testAttemptStatus,
              questionAttemptDataList: questionAttemptDataList,
            ),

            // Back to dashboard button
            const SizedBox(height: 24),
            SizedBox(
              width: 120,
              child: OutlinedButton(
                onPressed: () {
                  // context.go('/home');
                  context.go('/past-tests');
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text('Back'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection({
    required String submissionDate,
    required String submissionTime,
    required int correctAnswers,
    required int totalQuestions,
    required int scorePercentage,
    TestResultSummary? summary,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Summary header
        const Padding(
          padding: EdgeInsets.only(left: 8.0),
          child: Row(
            children: [
              /* Icon(Icons.summarize, color: Colors.black54),
              SizedBox(width: 8),*/
              Text(
                ' Test Summary',
                style: TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Summary card with enhanced data
        Container(
          margin: const EdgeInsets.only(top: 12, bottom: 24),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: [
              // First row - submission info and score

              // Additional summary data if available
              if (summary != null) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildSummaryItem('Test Name', summary.testName),
                    _buildSummaryItem('Subject', summary.subject),
                    _buildSummaryItem(
                        'Attempted On', '$submissionDate @ $submissionTime'),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceCards({
    required int scorePercentage,
    TestResultSummary? summary,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Row(
        children: [
          // Total Percentage Card
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Percentage',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$scorePercentage%',
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Time Spent Card
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Time Spent',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    summary?.timeSpent ?? 'N/A',
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResultsBreakdown({
    required TestQuestionsWithAnswerStatus testAttemptStatus,
    required int totalQuestions,
  }) {
    final correctAnswers = testAttemptStatus.correctAnswers;
    final incorrectAnswers = testAttemptStatus.incorrectAnswers;
    final unansweredQuestions = testAttemptStatus.unansweredQuestions;

    // For now, we'll set partially correct to 0 since it's not in the current data model
    // This can be updated when the data model includes partially correct answers
    const partiallyCorrectAnswers = 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'Test Result',
            style: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),

          // Content with breakdown and chart
          Row(
            children: [
              // Left side - Results breakdown
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    _buildResultItem(
                      color: Colors.green,
                      label: 'Correct Answers',
                      count: correctAnswers,
                    ),
                    const SizedBox(height: 12),
                    _buildResultItem(
                      color: Colors.red,
                      label: 'Incorrect Answers',
                      count: incorrectAnswers,
                    ),
                    const SizedBox(height: 12),
                    _buildResultItem(
                      color: Colors.grey,
                      label: 'Unanswered answers',
                      count: unansweredQuestions,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 40),

              // Right side - Circular progress chart
              Expanded(
                flex: 2,
                child: Center(
                  child: _buildCircularChart(
                    correctAnswers: correctAnswers,
                    incorrectAnswers: incorrectAnswers,
                    unansweredQuestions: unansweredQuestions,
                    totalQuestions: totalQuestions,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem({
    required Color color,
    required String label,
    required int count,
  }) {
    return Row(
      children: [
        // Colored circle indicator
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),

        // Label
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),

        // Count
        Text(
          count.toString(),
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildCircularChart({
    required int correctAnswers,
    required int incorrectAnswers,
    required int unansweredQuestions,
    required int totalQuestions,
  }) {
    // Calculate percentages
    final correctPercentage =
        totalQuestions > 0 ? (correctAnswers / totalQuestions) : 0.0;
    final incorrectPercentage =
        totalQuestions > 0 ? (incorrectAnswers / totalQuestions) : 0.0;
    final unansweredPercentage =
        totalQuestions > 0 ? (unansweredQuestions / totalQuestions) : 0.0;
    // For now, partially correct is 0 since it's not in the current data model
    const partialPercentage = 0.0;

    return SizedBox(
      width: 150,
      height: 150,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Circular progress indicator
          SizedBox(
            width: 150,
            height: 150,
            child: CustomPaint(
              painter: CircularChartPainter(
                correctPercentage: correctPercentage,
                incorrectPercentage: incorrectPercentage,
                partialPercentage: partialPercentage,
                unansweredPercentage: unansweredPercentage,
              ),
            ),
          ),

          // Center text
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Total Questions',
                style: TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                totalQuestions.toString(),
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTestAnalysisSection(TestAttemptAnalysis analysis) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Text(
                      'Question Type Analysis',
                      style: TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Performance breakdown by question type',
                  style: TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Table header
          Container(
            // clipBehavior: Clip.,
            decoration: const ShapeDecoration(
                shape: RoundedRectangleBorder(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                color: const Color(0xFFF1ECF5)),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),

            child: const Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Question Type',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'No. of Questions',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Accuracy',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),

          // Analysis data rows
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: analysis.moduleAnalysis.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Colors.grey.shade200,
            ),
            itemBuilder: (context, index) {
              final moduleAnalysis = analysis.moduleAnalysis[index];
              return Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  children: [
                    // Question Type
                    Expanded(
                      flex: 2,
                      child: Text(
                        moduleAnalysis.questionType,
                        style: const TextStyle(
                          fontFamily: 'Poppins',
                          fontSize: 14,
                        ),
                      ),
                    ),

                    // No. of Questions
                    Expanded(
                      flex: 3,
                      child: Text(
                        '${moduleAnalysis.totalQuestions}',
                        style: const TextStyle(
                          fontFamily: 'Poppins',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),

                    // Accuracy with color coding
                    Expanded(
                      flex: 2,
                      child: Container(
                        alignment: Alignment.bottomRight,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getAccuracyColor(moduleAnalysis.accuracy),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${moduleAnalysis.accuracy.round()}%',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: _getAccuracyTextColor(
                                  moduleAnalysis.accuracy),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Color _getAccuracyColor(double accuracy) {
    if (accuracy >= 80) {
      return Colors.green.shade100;
    } else if (accuracy >= 60) {
      return Colors.orange.shade100;
    } else {
      return Colors.red.shade100;
    }
  }

  Color _getAccuracyTextColor(double accuracy) {
    if (accuracy >= 80) {
      return Colors.green.shade800;
    } else if (accuracy >= 60) {
      return Colors.orange.shade800;
    } else {
      return Colors.red.shade800;
    }
  }

  Widget _buildTestResultDetails({
    required TestQuestionsWithAnswerStatus testAttemptStatus,
    required List<QuestionWithAnswerStatus> questionAttemptDataList,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Questions',
                  style: const TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Table header
          Container(
            decoration: const ShapeDecoration(
                shape: RoundedRectangleBorder(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                color: const Color(0xFFF1ECF5)),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: const Row(
              children: [
                SizedBox(
                    width: 40,
                    child: Text('Sl No',
                        style: TextStyle(fontWeight: FontWeight.w600))),
                SizedBox(width: 16),
                Expanded(
                    child: Text('Question',
                        style: TextStyle(fontWeight: FontWeight.w600))),
                SizedBox(
                    width: 100,
                    child: Text('Result',
                        style: TextStyle(fontWeight: FontWeight.w600))),
              ],
            ),
          ),

          // Question list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: questionAttemptDataList.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Colors.grey.shade200,
            ),
            itemBuilder: (context, index) {
              final question = questionAttemptDataList[index];
              return InkWell(
                onTap: () {
                  // Navigate to question detail view with data
                  print(
                      'Navigating to question ${index + 1} for test ${widget.testAttemptId}');
                  print(
                      'Question data count: ${questionAttemptDataList.length}');
                  context.go(
                      '/test-result/${widget.testAttemptId}/question/${index + 1}',
                      extra: questionAttemptDataList);
                },
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 40,
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          question.shortDescription,
                          style: const TextStyle(
                            fontFamily: 'Poppins',
                          ),
                        ),
                      ),
                      Container(
                        width: 100,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: question.answerStatus ==
                                  QuestionAnswerStatus.correct
                              ? Colors.green.shade100
                              : Colors.red.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          question.answerStatus == QuestionAnswerStatus.correct
                              ? 'Correct'
                              : 'Incorrect',
                          style: TextStyle(
                            fontFamily: 'Poppins',
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: question.answerStatus ==
                                    QuestionAnswerStatus.correct
                                ? Colors.green.shade800
                                : Colors.red.shade800,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class CircularChartPainter extends CustomPainter {
  final double correctPercentage;
  final double incorrectPercentage;
  final double partialPercentage;
  final double unansweredPercentage;

  CircularChartPainter({
    required this.correctPercentage,
    required this.incorrectPercentage,
    required this.partialPercentage,
    required this.unansweredPercentage,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 15; // Leave more padding for thicker stroke
    const strokeWidth = 20.0; // Thicker stroke for donut effect

    // Define colors
    const correctColor = Colors.green;
    const incorrectColor = Colors.red;
    const partialColor = Colors.orange;
    const unansweredColor = Colors.grey;

    // Create paint objects
    final correctPaint = Paint()
      ..color = correctColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final incorrectPaint = Paint()
      ..color = incorrectColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final partialPaint = Paint()
      ..color = partialColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final unansweredPaint = Paint()
      ..color = unansweredColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // Background circle (light gray) - only draw if there are no results
    if (correctPercentage == 0 &&
        incorrectPercentage == 0 &&
        unansweredPercentage == 0) {
      final backgroundPaint = Paint()
        ..color = Colors.grey.shade200
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth;

      canvas.drawCircle(center, radius, backgroundPaint);
    }

    // Calculate angles (2π = full circle)
    const fullCircle = 2 * 3.14159;
    final correctAngle = correctPercentage * fullCircle;
    final incorrectAngle = incorrectPercentage * fullCircle;
    final partialAngle = partialPercentage * fullCircle;
    final unansweredAngle = unansweredPercentage * fullCircle;

    // Starting angle (top of circle)
    double startAngle = -3.14159 / 2; // Start from top

    // Draw correct answers arc
    if (correctPercentage > 0) {
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        correctAngle,
        false,
        correctPaint,
      );
      startAngle += correctAngle;
    }

    // Draw incorrect answers arc
    if (incorrectPercentage > 0) {
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        incorrectAngle,
        false,
        incorrectPaint,
      );
      startAngle += incorrectAngle;
    }

    // Draw partial answers arc
    if (partialPercentage > 0) {
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        partialAngle,
        false,
        partialPaint,
      );
      startAngle += partialAngle;
    }

    // Draw unanswered arc
    if (unansweredPercentage > 0) {
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        unansweredAngle,
        false,
        unansweredPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
