import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/answer_option.dart';
import 'package:skillapp/src/test/presentation/widgets/test_content_area.dart';
import 'package:skillapp/src/test/presentation/widgets/test_footer.dart';
import 'package:skillapp/src/test/presentation/widgets/test_screen_header.dart';
import 'package:skillapp/src/test/presentation/widgets/test_progress_drawer.dart';
import 'package:skillapp/src/test/presentation/widgets/test_finish_drawer.dart';
import 'package:skillapp/src/test/presentation/widgets/test_timer_widget.dart';

class TestScreen extends StatefulWidget {
  final Test test;
  final String testAttemptId;
  final TestTypes testType;

  const TestScreen({
    super.key,
    required this.test,
    required this.testAttemptId,
    required this.testType,
  });

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    // Handle case when navigating back (state.extra is null)
    if (state.extra == null) {
      // Navigate back to home immediately without showing a loading screen
      // This is more direct and avoids the loading screen getting stuck
      context.go('/home');

      // Return an empty container that won't be visible
      // since we're navigating away immediately
      return const SizedBox.shrink();
    }

    final Map<String, dynamic> extra = state.extra as Map<String, dynamic>;
    return TestScreen(
      test: extra['test'] as Test,
      testAttemptId: extra['testAttemptId'] as String,
      testType: extra['testType'] as TestTypes,
    );
  }

  @override
  State<TestScreen> createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  // Scaffold key to control the drawer
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  // Key for the timer widget to access its methods
  final GlobalKey<TestTimerWidgetState> _timerKey =
      GlobalKey<TestTimerWidgetState>();

  // Flag to track if test has been marked as started
  bool _testMarkedAsStarted = false;

  @override
  void initState() {
    super.initState();
    // First Fetch the test questions
    context
        .read<TestsBloc>()
        .add(FetchTestDataEvent(testId: widget.test.testId));
  }

  Widget _buildEndDrawer() {
    print("TestScreen: _buildEndDrawer called");
    return BlocBuilder<TestsBloc, TestsStateInitial>(builder: (context, state) {
      if (state is TestProgressState) {
        return TestProgressDrawer(
          testProgressSummaryList: state.testProgressSummaryList,
          onQuestionSelected: (questionId) {
            // Use ViewSelectedQuestionEvent to directly navigate to the selected question
            context.read<TestsBloc>().add(
                  ViewSelectedQuestionEvent(
                    questionId: questionId,
                  ),
                );

            // No need to clear selected answer as we're using bloc state

            // Close the drawer
            _scaffoldKey.currentState!.closeEndDrawer();
          },
          scaffoldKey: _scaffoldKey,
        );
      } else if (state is TestFinishSummaryState) {
        return TestFinishDrawer(
          testName: state.test.description,
          testSubject: state.test.subject,
          onClose: () {
            _scaffoldKey.currentState!.closeEndDrawer();
          },
          onGoBack: () {
            _scaffoldKey.currentState!.closeEndDrawer();
          },
          onSubmit: () {
            _scaffoldKey.currentState!.closeEndDrawer();

            // Stop the timer
            if (_timerKey.currentState != null) {
              _timerKey.currentState!.stopTimer();
            }

            // Submit the test
            context.read<TestsBloc>().add(SubmitTestEvent());
          },
        );
      }
      return Container(); // Empty drawer when not in progress state
    });
  }

  // We'll use the bloc state for tracking selected answers instead of local state

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      endDrawer: _buildEndDrawer(),
      body: BlocConsumer<TestsBloc, TestsStateInitial>(
        listener: (context, state) {
          // Mark test as started when TestsDataState is emitted (after FetchTestDataEvent completes)
          if (state is TestsDataState && !_testMarkedAsStarted) {
            print(
                "TestScreen: TestsDataState emitted, marking test as started");
            print("TestScreen: Test status is ${state.status}");
            _testMarkedAsStarted = true;

            // Use addPostFrameCallback to ensure the state emission is complete
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // Additional safety check to ensure the widget is still mounted
              if (mounted) {
                print("TestScreen: Dispatching MarkTestAsStartedEvent");
                context.read<TestsBloc>().add(MarkTestAsStartedEvent(
                    testAttemptId: widget.testAttemptId,
                    testType: widget.testType));
              }
            });
          }

          // Handle TestStartedState to confirm the test was marked as started
          else if (state is TestStartedState && _testMarkedAsStarted) {
            print(
                "TestScreen: Test successfully marked as started - status: ${state.status}");
          }

          // Handle error states
          else if (state is TestDataErrorState) {
            print("TestScreen: Error occurred - ${state.message}");
            // Reset the flag in case of error so user can retry
            _testMarkedAsStarted = false;
          }

          // Open the drawer when TestProgressState is emitted
          if (state is TestProgressState) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_scaffoldKey.currentState != null &&
                  !_scaffoldKey.currentState!.isEndDrawerOpen) {
                _scaffoldKey.currentState!.openEndDrawer();
              }
            });
          } else if (state is TestFinishSummaryState) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_scaffoldKey.currentState != null &&
                  !_scaffoldKey.currentState!.isEndDrawerOpen) {
                _scaffoldKey.currentState!.openEndDrawer();
              }
            });
          } else if (state is AnswererCapturedState &&
              state.message == 'submitted') {
            // Navigate to test result screen
            print("JB: Test Type is-->${state.testType}");
            print("Test attempted id : ${state.testAttemptId}");

            if (state.testType == TestTypes.daily) {
              // Navigate to daily test success screen
              context.go('/daily-test-success/${state.testAttemptId}');
            } else {
              context.go('/test-result/${state.testAttemptId}');
            }
          }
        },
        builder: (context, state) {
          // Handle different states
          if (state is TestsDataState) {
            final totalQuestions =
                state.testWithQuestionsData.testQuestionEntrySet.length;
            final currentQuestionOrder = state.currentQuestion.order;

            print("JB: Test subject is ${state.test.subject}");

            return Column(
              children: [
                // Custom header with timer and question counter
                TestScreenHeader(
                  timerKey: _timerKey,
                  currentQuestion: currentQuestionOrder,
                  totalQuestions: totalQuestions,
                  subject: state.test.subject,
                  testType: widget.testType,
                  onFullscreenToggle: () {
                    // Dispatch ViewTestProgressEvent to show progress summary
                    context.read<TestsBloc>().add(ViewTestProgressEvent());
                  },
                  isTestFlow: true,
                ),

                // Main content area with fixed footer
                Expanded(
                  child: Stack(
                    children: [
                      // Scrollable content area
                      Positioned.fill(
                        bottom: 80, // Reserve space for footer
                        child: _buildTestContent(context, state),
                      ),

                      // Fixed footer at the bottom
                      Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0,
                        child: Center(
                          child: Container(
                            constraints: const BoxConstraints(maxWidth: 1600),
                            // Use ClipRRect to ensure the Material elevation shadow is also clipped
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: TestFooter(
                                onBack: () {
                                  if (currentQuestionOrder > 1) {
                                    // Dispatch FetchPreviousQuestionEvent to get the previous question
                                    context.read<TestsBloc>().add(
                                          FetchPreviousQuestionEvent(
                                            currentOrder:
                                                state.currentQuestion.order,
                                          ),
                                        );

                                    // No need to clear selected answer as we're using bloc state
                                  } else {
                                    // Show dialog to confirm exit
                                    _showExitConfirmationDialog(context);
                                  }
                                },
                                onFlag: () {
                                  // Dispatch FlagAQuestionEvent to flag the current question
                                  context.read<TestsBloc>().add(
                                        FlagAQuestionEvent(
                                          questionId:
                                              state.currentQuestion.questionId,
                                        ),
                                      );
                                },
                                onNext: () {
                                  if (currentQuestionOrder < totalQuestions) {
                                    // Dispatch FetchNextQuestionEvent to get the next question
                                    context.read<TestsBloc>().add(
                                          FetchNextQuestionEvent(
                                            currentOrder:
                                                state.currentQuestion.order,
                                          ),
                                        );

                                    // No need to clear selected answer as we're using bloc state
                                  } else {
                                    // Show dialog to confirm submission
                                    _showSubmitConfirmationDialog(
                                        context, context.read<TestsBloc>());
                                  }
                                },
                                isLastQuestion:
                                    currentQuestionOrder == totalQuestions,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          } else if (state is NewTestFetchedState) {
            // Show loading while fetching test data
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading test questions...'),
                ],
              ),
            );
          } else if (state is TestDataErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: ${state.message} '),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      context.go('/home');
                    },
                    iconAlignment: IconAlignment.end,
                    label: const Text(
                      'Back to home',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.fromLTRB(24, 9, 16, 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Initial loading state
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Preparing your test...'),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildTestContent(BuildContext context, TestsDataState state) {
    // Get the current question directly from the state
    final currentQuestion = state.currentQuestion;
    final questionData = currentQuestion.questionData;

    // Extract question context and prompt
    String questionContext = '';
    String questionPrompt = '';
    List<AnswerOption> answerOptions = [];
    String? imageUrl;
    // Process question data

    // Extract data from the complex question structure
    if (questionData.question.descAndImgDataList.isNotEmpty) {
      // Get the description text from the first description entry
      final descData = questionData.question.descAndImgDataList.first;
      // Process description data

      if (descData.descAndImgDataEntryList.isNotEmpty) {
        questionContext = descData.descAndImgDataEntryList.first.description;
      }

      // Check for image URL in the description data
      for (var entry in descData.descAndImgDataEntryList) {
        if (entry.type == 'image' && entry.imageDataList.isNotEmpty) {
          imageUrl = entry.imageDataList.first.path;
          break;
        }
      }
    }

    // Get the question prompt from the first option entry's description
    if (questionData.answerOptions.entries.isNotEmpty) {
      final firstOption = questionData.answerOptions.entries.first;
      if (firstOption.descAndImgDataWrapper.descAndImgDataList.isNotEmpty) {
        final descList = firstOption.descAndImgDataWrapper.descAndImgDataList
            .first.descAndImgDataEntryList;
        if (descList.isNotEmpty) {
          questionPrompt = descList.first.description;
        }
      }
    }

    // Get the answer options
    for (var i = 0; i < questionData.answerOptions.entries.length; i++) {
      final option = questionData.answerOptions.entries[i];
      // Process option data

      String optionText = '';

      // Get the option text from the description data
      if (option.descAndImgDataWrapper.descAndImgDataList.isNotEmpty) {
        final descList = option.descAndImgDataWrapper.descAndImgDataList.first
            .descAndImgDataEntryList;
        if (descList.isNotEmpty) {
          optionText = descList.first.description;
        }
      }

      answerOptions.add(AnswerOption(
        id: option.id,
        //  label: String.fromCharCode(65 + i), // A, B, C, D, etc.
        label: option.displayId,
        text: optionText,
      ));
    }

    // Get the selected answer directly from the current question
    // Check if the question context contains package information
    bool hasPackageInfo = questionContext.contains('Package 1') ||
        questionContext.contains('Package 2') ||
        questionContext.contains('packages');

    // Extract bullet points from the question context if available
    List<String> bulletPoints = [];
    if (hasPackageInfo) {
      // Extract bullet points from the question context
      // This is a simple example - in a real app, you'd use regex or more sophisticated parsing
      if (questionContext.contains('Package 1')) {
        bulletPoints.add('Package 1 has a mass of 850g.');
      }
      if (questionContext.contains('Package 2')) {
        bulletPoints.add('Package 2 has a mass of 225 g.');
      }
    }

    return TestContentArea(
      questionFullContext: questionData.question.descAndImgDataList,
      questionContext: questionContext,
      questionPrompt: questionPrompt,
      answerOptions: answerOptions,
      questionImage: imageUrl != null && imageUrl.isNotEmpty
          ? Image.network(imageUrl)
          : null,
      selectedAnswerId: currentQuestion.selectedAnswer.isNotEmpty
          ? currentQuestion.selectedAnswer
          : null,
      showBulletPoints: hasPackageInfo,
      bulletPoints: bulletPoints,
      onAnswerSelected: (answerId) {
        // Submit the answer directly to the bloc without local state update
        context.read<TestsBloc>().add(
              AnswerAQuestionEvent(
                questionId: currentQuestion.questionId,
                selectedAnswer: answerId,
              ),
            );
      },
    );
  }

  // Dialog to confirm exit
  void _showExitConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Test?'),
        content: const Text(
          'Are you sure you want to exit the test? Your progress will be saved.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/home');
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }

  // Show finish drawer for test submission
  void _showSubmitConfirmationDialog(BuildContext context, TestsBloc testBloc) {
    // Get the current test from the bloc state
    if (testBloc.state is TestsDataState) {
      final currentState = testBloc.state as TestsDataState;
      // Dispatch event to show finish drawer
      testBloc.add(ViewTestFinishSummaryEvent(test: currentState.test));

      // Open the drawer
      Future.delayed(const Duration(milliseconds: 100), () {
        _scaffoldKey.currentState?.openEndDrawer();
      });
    }
  }
}
