import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/widgets/test_layout_widget.dart';

class TestConfirmationScreen extends StatelessWidget {
  final Test test;
  final String testAttemptId;
  final TestTypes testType;

  const TestConfirmationScreen({
    super.key,
    required this.test,
    required this.testAttemptId,
    required this.testType,
  });

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    // Handle case when navigating back (state.extra is null)
    if (state.extra == null) {
      // Navigate back to home immediately without showing a loading screen
      // This is more direct and avoids the loading screen getting stuck
      context.go('/home');

      // Return an empty container that won't be visible
      // since we're navigating away immediately
      return const SizedBox.shrink();
    }

    final Map<String, dynamic> extra = state.extra as Map<String, dynamic>;
    return TestConfirmationScreen(
      test: extra['test'] as Test,
      testAttemptId: extra['testAttemptId'] as String,
      testType: extra['testType'] as TestTypes,
    );
  }

  @override
  Widget build(BuildContext context) {
    return TestLayoutWidget(
      automaticallyImplyLeading: false,
      child: Container(
        color: Colors.grey[100],
        child: Center(
          child: Card(
            margin: const EdgeInsets.all(24),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              width: 500, // Fixed width to match screenshot
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Test title
                  const Text(
                    'Selective High School Placement',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const Text(
                    'Practice Test',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  // Test subject
                  const Text(
                    'Mathematical Reasoning- Full Mock Test 1',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 24),
                  const Divider(height: 1),
                  const SizedBox(height: 24),

                  // Confirmation question
                  const Text(
                    'Are You Sure You Have Finished Reading The Instructions?',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // Warning message
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.blue[50],
                          ),
                          child: const Center(
                            child: Icon(Icons.info_outline,
                                color: Colors.blue, size: 16),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Text(
                            'You will not be able to read them again until the test begins',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      OutlinedButton(
                        onPressed: () {
                          // Go back to instructions
                          context.pop();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          side: BorderSide(color: Colors.grey[400]!),
                        ),
                        child: const Text('No',
                            style: TextStyle(color: Colors.black87)),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          // Navigate to the actual test screen
                          context.go('/test/${test.testId}', extra: {
                            'test': test,
                            'testAttemptId': testAttemptId,
                            'testType': testType,
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(
                              0xFF50409A), // Purple color from screenshot
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: const Text('Yes'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
