import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/services/injection_container.dart' as di;
import 'package:skillapp/src/test/domain/common/tests_config_cache.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/test_error_widget.dart';
import 'package:skillapp/src/test/presentation/widgets/test_instructions_card.dart';
import 'package:skillapp/src/test/presentation/widgets/test_layout_widget.dart';

class TestInstructionsScreen extends StatelessWidget {
  final String subject;
  final TestTypes testType;

  const TestInstructionsScreen(
      {super.key, required this.subject, required this.testType});

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    // Handle case when navigating back (state.extra is null)
    if (state.extra == null) {
      // Navigate back to home immediately without showing a loading screen
      // This is more direct and avoids the loading screen getting stuck
      context.go('/home');

      // Return an empty container that won't be visible
      // since we're navigating away immediately
      return const SizedBox.shrink();
    }

    final extra = state.extra as Map<String, dynamic>;
    return TestInstructionsScreen(
      subject: extra['subject'] as String,
      testType: extra['testType'] as TestTypes,
    );
  }

  // Helper method to get test duration from config
  int _getTestDuration(BuildContext context, String subject) {
    try {
      final testsConfigCache = di.sl<TestsConfigCache>();
      final testConfig = testsConfigCache.getTestConfig(
        testType,
        subject,
      );
      return testConfig.parameters.maxAllowedTime;
    } catch (e) {
      debugPrint('Error getting test config: $e');
      return 40; // Default fallback value
    }
  }

  @override
  Widget build(BuildContext context) {
    return TestLayoutWidget(
      automaticallyImplyLeading: false,
      child: Container(
        color: Colors.grey[100],
        child: Center(
          child: BlocBuilder<TestsBloc, TestsStateInitial>(
            builder: (context, state) {
              // Loading state
              if (state is! NewTestFetchedState &&
                  state is! NewTestFetchErrorState) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Loading test...'),
                    ],
                  ),
                );
              }

              // Error state
              if (state is NewTestFetchErrorState) {
                return TestErrorWidget.sectionalTest();
              }

              // Success state
              if (state is NewTestFetchedState) {
                // Get test duration from config
                final durationMinutes = _getTestDuration(context, subject);

                return TestInstructionsCard(
                  test: state.test,
                  testAttemptId: state.testAttemptId,
                  subject: subject,
                  questionCount: state.test.questionWithOrderList.length,
                  durationMinutes: durationMinutes,
                  onBack: () => context.go('/home'),
                  onContinue: () {
                    // Navigate to the confirmation screen
                    context.push('/test-confirmation', extra: {
                      'test': state.test,
                      'testAttemptId': state.testAttemptId,
                      'testType': testType,
                    });
                  },
                );
              }

              // Fallback (should never reach here)
              return const Text('Unexpected state');
            },
          ),
        ),
      ),
    );
  }
}
