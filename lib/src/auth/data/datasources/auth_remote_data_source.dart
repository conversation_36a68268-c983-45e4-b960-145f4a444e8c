import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/errors/exceptions.dart';

abstract class AuthRemoteDataSource {
  const AuthRemoteDataSource();

  Stream<LocalUser> getUserAuthChanges();

  Future<LocalUser> login({
    required String email,
    required String password,
  });

  Future<String> signup({
    required String email,
    required String password,
  });

  Future<String> fetchUID();

  Future<bool> createUserProfile(String email, String name, int standard);

  Future<void> logout();

  Future<LocalUser> populateDefaultProfileIfMissing(String uid, String email);

  Future<Set<UserProfile>> fetchUserProfileDetails(String email);
  Future<LocalUser> signInWithGoogle(AuthCredential credential);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firebaseFirestore;
  static final _googleSignIn = GoogleSignIn();

  const AuthRemoteDataSourceImpl(
      {required FirebaseAuth firebaseAuth,
      required FirebaseFirestore firebaseFirestore})
      : _firebaseAuth = firebaseAuth,
        _firebaseFirestore = firebaseFirestore;

  @override
  Stream<LocalUser> getUserAuthChanges() {
    print("AW:Streaming user changes");
    return _firebaseAuth.authStateChanges().map((firebaseUser) {
      print("AW:Streaming user changes:firebaseUser= $firebaseUser");
      final userProfile = firebaseUser == null
          ? LocalUser.empty
          : LocalUser(
              id: firebaseUser.uid,
              email: firebaseUser.email ?? '',
              currentProfile: const UserProfile.empty());

      print("AW:Streaming user changes:userProfile= $userProfile");
      return userProfile;
    });
  }

  @override
  Future<LocalUser> login({
    required String email,
    required String password,
  }) async {
    try {
      await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      print("AW::Repo:logInWithEmailAndPassword:success");
      //TODO - To revisit this code to support multiple profiles
      return await populateDefaultProfileIfMissing(
          _firebaseAuth.currentUser!.uid, email);
      /*return LocalUser(
        email: email,
        id: _firebaseAuth.currentUser!.uid,
        currentProfile: const UserProfile.empty(),
      );*/
    } on FirebaseAuthException catch (e) {
      throw ServerException(
        message: e.message ?? 'Error Occurred',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrint(s.toString());
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
      ]);

      await _googleSignIn.signOut();

      await HydratedBloc.storage.clear();

      //  await googleSignIn.signOut();
    } catch (_) {}
  }

  @override
  Future<String> signup({
    required String email,
    required String password,
  }) async {
    try {
      await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      print("AW::Repo:signup:success");
      return Future.value(_firebaseAuth.currentUser!.uid);
    } on FirebaseAuthException catch (e) {
      throw ServerException(
        message: e.message ?? 'Error Occurred',
        statusCode: e.code,
      );
    } catch (e, s) {
      print("AW::Repo:signup:exception $e");
      debugPrint(s.toString());
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<String> fetchUID() async {
    try {
      return Future.value(_firebaseAuth.currentUser!.uid);
    } on FirebaseAuthException catch (e) {
      throw ServerException(
        message: e.message ?? 'Error Occurred',
        statusCode: e.code,
      );
    }
  }

  @override
  Future<bool> createUserProfile(
      String email, String name, int standard) async {
    CollectionReference usersCollection =
        _firebaseFirestore.collection('users');
    print("AW:Repo:createUser:name=$name");
    print("AW:Repo:createUser:email=$email");
    print("AW:Repo:createUser:standard=$standard");

    DocumentSnapshot emailSnapshot = await usersCollection.doc(email).get();
    if (!emailSnapshot.exists) {
      await usersCollection.doc(email).set({'defaultValue': ''});
    }

    int level = await _fetchStandardLevel(standard);

    Map<String, dynamic> profileData = {
      'name': name,
      'standard': standard,
      'level': level
    };
    DocumentReference<Map<String, dynamic>> ref = await usersCollection
        .doc(email)
        .collection('profiles')
        .add(profileData);

    DocumentSnapshot doc = await ref.get();
    //TODO- See if this needs to be changed to send the user details itself
    if (doc.exists) {
      return Future.value(true);
    } else {
      return Future.value(false);
    }
  }

  Future<int> _fetchStandardLevel(int standard) async {
    DocumentSnapshot standardSnapshot = await _firebaseFirestore
        .collection('standards')
        .doc(standard.toString())
        .get();

    if (!standardSnapshot.exists) {
      throw const ServerException(
        message: 'Standard not found',
        statusCode: '404',
      );
    }

    Map<String, dynamic> standardData =
        standardSnapshot.data() as Map<String, dynamic>;
    return standardData['level'];
  }

  @override
  Future<LocalUser> populateDefaultProfileIfMissing(
      String uid, String email) async {
    print('AW:userWithDefaultProfile:user= $email, $uid');

    Set<UserProfile> profileList = await fetchUserProfileDetails(email);

    //TODO-Change this when multi profile is implemented.

    return LocalUser(
      id: uid,
      email: email,
      currentProfile: profileList.isNotEmpty
          ? profileList.first
          : const UserProfile.empty(),
    );
  }

  @override
  Future<Set<UserProfile>> fetchUserProfileDetails(String email) async {
    print("JB:fetchUserProfileDetails:email=$email");

    // Check if email is valid to prevent Firestore path errors
    if (email.isEmpty) {
      print("JB:fetchUserProfileDetails:email is empty, returning empty set");
      return <UserProfile>{};
    }

    QuerySnapshot querySnapshot =
        await _firebaseFirestore.collection('users/$email/profiles').get();

    Set<UserProfile> profiles = querySnapshot.docs.map((profile) {
      Map<String, dynamic> data = profile.data() as Map<String, dynamic>;
      print('fetchUserProfileDetails:$data');
      return UserProfile(
        id: profile.id,
        name: data['name'],
        standard: data['standard'],
        level: data['level'] != null
            ? (data['level'] as num).toInt()
            : (data['standard'] as num).toInt(),
      );
    }).toSet();

    return profiles;
  }

  @override
  Future<LocalUser> signInWithGoogle(AuthCredential credential) async {
    try {
      print("AW:signInWithGoogle:credential=$credential");
      UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      String email = userCredential.additionalUserInfo?.profile?['email'] ?? '';

      LocalUser localUser = await populateDefaultProfileIfMissing(
          _firebaseAuth.currentUser!.uid, email);
      //_googleSignIn = GoogleSignIn();
      String name = userCredential.user?.displayName ?? '';
      if (localUser.currentProfile.isEmpty) {
        localUser = LocalUser(
            id: localUser.id,
            email: localUser.email,
            currentProfile:
                UserProfile(id: '', name: name, standard: 0, level: 0));
      }
      return localUser;
    } catch (error, stackTrace) {
      print('Error signing in with Google: $error');
      debugPrintStack(stackTrace: stackTrace);
      return LocalUser.empty;
    }
  }
}
