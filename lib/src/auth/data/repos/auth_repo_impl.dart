import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/auth/data/datasources/auth_remote_data_source.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';
import 'package:firebase_auth/firebase_auth.dart';

class AuthRepoImpl extends AuthRepo {
  final AuthRemoteDataSource _remoteDataSource;
  //final FirebaseAuth _firebaseAuth;
  final CacheContext _cacheContext;

  AuthRepoImpl(
      {required AuthRemoteDataSource remoteDataSource,
      required CacheContext cacheContext})
      : _remoteDataSource = remoteDataSource,
        _cacheContext = cacheContext;

  @override
  ResultFuture<LocalUser> login(
      {required String email, required String password}) async {
    try {
      final result =
          await _remoteDataSource.login(email: email, password: password);
      _cacheContext.setCurrentUser(result);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }

  @override
  ResultFuture<void> logout() async {
    try {
      final result = await _remoteDataSource.logout();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }

  @override
  ResultFuture<LocalUser> register(
      {required String email,
      required String password,
      required String name,
      required int standard}) async {
    try {
      String uid =
          await _remoteDataSource.signup(email: email, password: password);

      await _remoteDataSource.createUserProfile(email, name, standard);

      return Right(LocalUser(
        id: uid,
        email: email,
        currentProfile: const UserProfile.empty(),
      ));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }

  @override
  ResultFuture<LocalUser> createProfile(
      {required String email,
      required String name,
      required int standard}) async {
    try {
      String uid = await _remoteDataSource.fetchUID();

      await _remoteDataSource.createUserProfile(email, name, standard);

      return Right(LocalUser(
        id: uid,
        email: email,
        currentProfile: const UserProfile.empty(),
      ));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }

  @override
  ResultStream<LocalUser> getUserAuthChanges() {
    return _remoteDataSource.getUserAuthChanges().transform(_handleStream());
    // same as
    // .map((messages) => Right(messages));
  }

  StreamTransformer<LocalUser, Either<Failure, LocalUser>> _handleStream() {
    return StreamTransformer<LocalUser,
        Either<Failure, LocalUser>>.fromHandlers(
      handleError: (error, stackTrace, sink) {
        if (error is ServerException) {
          sink.add(
            Left(
              ServerFailure(
                message: error.message,
                statusCode: error.statusCode,
              ),
            ),
          );
        } else {
          // Handle other types of exceptions as needed
          sink.add(
            Left(
              ServerFailure(
                message: error.toString(),
                statusCode: 500,
              ),
            ),
          );
        }
      },
      handleData: (user, sink) {
        sink.add(Right(user));
      },
    );
  }

  @override
  ResultFuture<LocalUser> registerWithGoogle(
      {required AuthCredential credential}) async {
    try {
      print("AW:registerWithGoogle:credential=$credential");
      LocalUser localUser =
          await _remoteDataSource.signInWithGoogle(credential);
      print("AW:registerWithGoogle:localUser=$localUser");
      _cacheContext.setCurrentUser(localUser);
      return Right(localUser);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }

  @override
  ResultFuture<Set<UserProfile>> fetchUserProfile(String email) async {
    try {
      Set<UserProfile> profiles =
          await _remoteDataSource.fetchUserProfileDetails(email);

      return Right(profiles);
    } on ServerException catch (e, s) {
      print("AW:fetchUserProfile:error=$e");
      print("AW:fetchUserProfile:stackTrace=$s");
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }

  @override
  ResultFuture<LocalUser> populateDefaultProfileIfMissing(
      String uid, String email) async {
    try {
      final result =
          await _remoteDataSource.populateDefaultProfileIfMissing(uid, email);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    }
  }
}
