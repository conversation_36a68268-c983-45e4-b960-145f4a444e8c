import 'package:flutter/material.dart';

class SignUpOption extends StatelessWidget {
  final Color color;
  final String text;
  final Function onTap;

  const SignUpOption({
    super.key,
    required this.color,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            onTap();
          },
          child: Text(
            text,
            style: TextStyle(
              color: color,
              fontFamily: 'Poppins',
              fontStyle: FontStyle.normal,
              fontWeight: FontWeight.normal,
              fontSize: 16,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }
}
