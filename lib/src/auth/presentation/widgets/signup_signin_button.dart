import 'package:flutter/material.dart';

class SignUpSignInButton extends StatelessWidget {
  final bool isLogin;
  final Function onTap;
  final double fem;
  final double ffem;

  const SignUpSignInButton({
    super.key,
    required this.isLogin,
    required this.onTap,
    required this.fem,
    required this.ffem,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5.0),
      width: double.infinity,
      height: 55 * fem,
      decoration: BoxDecoration(
        color: const Color(0xFF50409A),
        borderRadius: BorderRadius.circular(26 * fem),
        boxShadow: [
          BoxShadow(
            color: const Color(0x3f000000),
            offset: Offset(0 * fem, 0 * fem),
            blurRadius: 4 * fem,
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          onTap();
        },
        child: Center(
          child: Text(
            isLogin ? 'Sign In' : 'Create an account',
            style: TextStyle(
              fontFamily: 'Poppins',
              color: Colors.white,
              fontSize: 16 * ffem,
              fontWeight: FontWeight.w500,
              height: 1.5 * ffem / fem,
            ),
          ),
        ),
      ),
    );
  }
}
