import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';

class CarouselItem {
  final String firstText;
  final String secondText;
  final String image;

  CarouselItem(
      {required this.firstText, required this.secondText, required this.image});
}

class LandingPageCarouselDesktop extends StatelessWidget {
  final List<Map<String, String>> imgList = [
    {
      'image': 'assets/images/FirstMedal.png',
      'text1': 'This is the text for Slide 1',
      'text2': 'This is the small text for Slide 1'
    },
    /*  {
      'image': 'assets/images/graduationHat.png',
      'text1': 'This is the text for Slide 2',
      'tex2': 'This is the small text for Slide 2',
    },
    {
      'image': 'assets/images/plane.png',
      'text1': 'This is the text for Slide 3',
      'text2': 'This is the small text for Slide 3'
    },*/
  ];

  int carouselDotIndex = 0;

  List<CarouselItem> carouselData = [
    CarouselItem(
      firstText: '\nLorem ipsum \n dolor sit amet consectetur',
      secondText:
          '\n\n\nLorem ipsum dolor sit amet,\n consectetur adipiscing elit, sed do\n eiusmod tempor incididunt ut ',
      image: 'assets/images/FirstMedal.png',
    ),
    CarouselItem(
      firstText: '\nLorem ipsum \n dolor sit amet consectetur',
      secondText:
          '\n\n\nLorem ipsum dolor sit amet,\n consectetur adipiscing elit, sed do\n eiusmod tempor incididunt ut ',
      image: 'assets/images/graduationHat.png',
    ),
    CarouselItem(
      firstText: '\nLorem ipsum \n dolor sit amet consectetur',
      secondText:
          '\n\n\nLorem ipsum dolor sit amet,\n consectetur adipiscing elit, sed do\n eiusmod tempor incididunt ut ',
      image: 'assets/images/plane.png',
    ),
  ];

  LandingPageCarouselDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    final double aspectRatio =
        screenWidth / screenHeight > 1.5 ? 16 / 9 : 4 / 3;

    double fem = ScreenUtil.getFem(context);
    double ffem = fem * 0.97;

    return CarouselSlider(
      items: carouselData.map((data) {
        if (carouselDotIndex < carouselData.length - 1) {
          carouselDotIndex = carouselDotIndex + 1;
        }
        return Container(
          // margin: const EdgeInsets.only(top: 10, left: 10),
          //  padding: EdgeInsets.fromLTRB(10, screenHeight * 0.2, 10, 10),
          padding: EdgeInsets.fromLTRB(0, screenHeight * 0.2, 0, 0),
          child: Column(
            //crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // code for carousel image
              Container(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.1),
                child: Image.asset(
                  data.image,
                  height: screenHeight * 0.4,
                  width: screenWidth * 0.8,
                  fit: BoxFit.cover,
                ),
              ),

              Container(
                padding: EdgeInsets.fromLTRB(100 * fem, 0, 100 * fem, 0),
                decoration: ShapeDecoration(
                  gradient: LinearGradient(
                    begin: const Alignment(0.98, -0.22),
                    end: const Alignment(-0.98, 0.22),
                    colors: [
                      Colors.white.withOpacity(0.20000000298023224),
                      Colors.black.withOpacity(0.10000000149011612)
                    ],
                  ),
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(width: 1, color: Colors.white),
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                // child: Text('h'),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    //  text: data.firstText,
                    //text:  'screen Heigt is $screenHeight and width is $screenWidth. divsiion is $screenHeight/$screenWidth',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.normal,
                      color: Color(0xFFECECEC),
                    ),
                    children: <TextSpan>[
                      TextSpan(
                        text: data.secondText,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.normal,
                          color: Color(0xFFECECEC),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
      options: CarouselOptions(
        height: screenHeight,
        // viewportFraction: 1,
        aspectRatio: ((screenHeight / screenWidth) - 0.10),
        // aspectRatio: 0.6,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        autoPlayCurve: Curves.fastOutSlowIn,
        enableInfiniteScroll: true,
        pauseAutoPlayOnTouch: true,
        enlargeCenterPage: true,
        //  enlargeStrategy: CenterPageEnlargeStrategy.height,
      ),
    );
  }
}
