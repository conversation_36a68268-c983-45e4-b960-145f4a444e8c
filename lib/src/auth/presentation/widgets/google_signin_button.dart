import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/data/services/firebase_services.dart';

import '../cubits/signup_cubit.dart';

class GoogleSignInButton extends StatefulWidget {
  const GoogleSignInButton({super.key});

  factory GoogleSignInButton.routeBuilder(_, __) {
    return const GoogleSignInButton();
  }

  @override
  State<GoogleSignInButton> createState() => _GoogleSignInButton();
}

class _GoogleSignInButton extends State<GoogleSignInButton> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25.0),
      child: Stack(
        children: [
          ElevatedButton(
            onPressed: () async {
              try {
                LocalUser localUser =
                    (await context.read<SignupCubit>().signInWithGoogle());

                print("localUser GoogleSign In Button : $localUser");

                /*String? email =
                    userCredential?.additionalUserInfo?.profile?['email'];
                String name = userCredential?.user?.displayName ?? '';

                print("Print emmail, name after google login: $email, $name");

                  Set<UserProfile> profileList = {};
                
                if (email!.isNotEmpty) {
                  profileList = await context
                      .read<SignupCubit>()
                      .fetchProfile(email) as Set<UserProfile>;

                  if (profileList.isNotEmpty) {
                    context.go("/home");
                  } else {
                    context.go(
                        "/googlesignupprofile/${Uri.encodeComponent(email)}/${Uri.encodeComponent(name)}");
                  }
                }*/

                if (localUser.isEmpty) {
                  //TODO- Handle this case and show proper error
                  print("AW:Sign in with google failed");
                } else if (localUser.currentProfile.id != '') {
                  context.go("/home");
                } else {
                  context.go(
                      "/googlesignupprofile/${Uri.encodeComponent(localUser.email)}/${Uri.encodeComponent(localUser.currentProfile.name)}");
                }
              } catch (e) {
                // Handle sign-in errors
                print('Sign-in error: $e');
                // You can display an error message to the user or perform other error handling here
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF50409A),
              minimumSize: const Size(double.infinity, 55),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(26)),
              ),
            ),
            child: const Text('Continue with Google'),
          ),
          Positioned(
            top: 10,
            left: 10,
            child: GestureDetector(
              onTap: () {
                // the tap action for the image here
                FirebaseServices.signInWithGoogle();
              },
              child: Image.asset(
                'assets/images/google.png', // replace with your image path
                width: 32,
                height: 32,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
