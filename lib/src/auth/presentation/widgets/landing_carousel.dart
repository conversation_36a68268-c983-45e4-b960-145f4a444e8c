import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:go_router/go_router.dart';

class CarouselItem {
  final String firstText;
  final String secondText;

  CarouselItem({required this.firstText, required this.secondText});
}

Widget buildLandingCarousel(BuildContext context) {
  List<CarouselItem> carouselData = [
    // Add your carousel items here
    // Example: CarouselItem(firstText: 'First', secondText: 'Lorem ipsum...'),
  ];

  int carouselDotIndex = 0;
  void fetchData() {
    // Database fetching logic goes here
    // Replace this with actual database fetching code

    // Dummy data
    List<CarouselItem> dummyData = [
      CarouselItem(
        firstText: '\nLorem ipsum \n dolor sit amet consectetur',
        secondText:
            '\n\n\nLorem ipsum dolor sit amet,\n consectetur adipiscing elit, sed do\n eiusmod tempor incididunt ut ',
      ),
      CarouselItem(
        firstText: '\nLorem ipsum \n dolor sit amet consectetur',
        secondText:
            '\n\n\nLorem ipsum dolor sit amet,\n consectetur adipiscing elit, sed do\n eiusmod tempor incididunt ut ',
      ),
      CarouselItem(
        firstText: '\nLorem ipsum \n dolor sit amet consectetur',
        secondText:
            '\n\n\nLorem ipsum dolor sit amet,\n consectetur adipiscing elit, sed do\n eiusmod tempor incididunt ut ',
      ),
    ];

    carouselData = dummyData;
  }

  fetchData();

  double baseWidth = 375;
  double fem = MediaQuery.of(context).size.width / baseWidth;
  double ffem = fem * 0.97;
  return Center(
//padding: const EdgeInsets.only(top: 182.0),
      child: Center(
    child: Container(
      width: 350 * ffem,
      height: 200 * ffem,
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(22),
        backgroundBlendMode: BlendMode.overlay,
        // gradient: onBoardingcarouselTextBoxGradient, // Add your gradient
        boxShadow: const [
          //  UIParameters.getCarouselBoxShadow(),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 2.0,
        ),
      ),
      child: Stack(
        children: [
          CarouselSlider(
            items: carouselData.map((data) {
              if (carouselDotIndex < carouselData.length - 1) {
                carouselDotIndex = carouselDotIndex + 1;
              }

              return Container(
                margin: const EdgeInsets.only(top: 10, left: 10),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 2.0),
                  child: Column(
                    //crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: data.firstText,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.normal,
                            color: Color(0xFFECECEC),
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: data.secondText,
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.normal,
                                color: Color(0xFFECECEC),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
            options: CarouselOptions(
              //height: 150,
              viewportFraction: 1,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              autoPlayCurve: Curves.fastOutSlowIn,
              enableInfiniteScroll: true,
              pauseAutoPlayOnTouch: true,
              enlargeCenterPage: true,
              enlargeStrategy: CenterPageEnlargeStrategy.height,
            ),
          ),
          Positioned(
            bottom: 10, // Adjust the position as needed
            left: 0,
            right: 0,
            child: DotsIndicator(
              dotsCount: carouselData
                  .length, // Replace with the actual number of carousel items
              position: carouselDotIndex, // Set the initial position

              // duration: const Duration(milliseconds: 200),
            ),
          ),
        ],
      ),
    ),
  ));
}

Widget buildImageCarousel(BuildContext context) {
  double baseWidth = 375;
  double fem = MediaQuery.of(context).size.width / baseWidth;
  double ffem = fem * 0.97;

  return Positioned(
    left: 0,
    right: 0,
    child: GestureDetector(
      onTap: () {
        // Perform navigation when the carousel is tapped
        context.push("/");
      },
      child: CarouselSlider(
        items: [
          Container(
            padding: EdgeInsets.only(top: 175 * ffem), // Add space at the top
            child: Transform.scale(
              scale: 4.0, // Adjust the scale factor as needed
              child: Image.asset(
                'assets/images/FirstMedal.png',
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 200 * ffem), // Add space at the top
            child: Transform.scale(
              scale: 5.5, // Adjust the scale factor as needed
              child: Image.asset(
                'assets/images/graduationHat.png',
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 25 * ffem), // Add space at the top
            child: Transform.scale(
              scale: 1.1, // Adjust the scale factor as needed
              child: Image.asset(
                'assets/images/plane.png',
              ),
            ),
          ),
        ],
        options: CarouselOptions(
          height: MediaQuery.of(context).size.height / 2.75 * ffem,
          viewportFraction: 1,
          autoPlay: true,
          autoPlayInterval: const Duration(seconds: 3),
          autoPlayCurve: Curves.linear,
          reverse: true,
          enableInfiniteScroll: true,
          pauseAutoPlayOnTouch: true,
          enlargeCenterPage: true,
          scrollDirection: Axis.vertical,
        ),
      ),
    ),
  );
}
