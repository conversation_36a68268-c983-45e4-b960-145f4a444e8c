import 'package:flutter/material.dart';

class DividerLine extends StatelessWidget {
  final double fem;
  final double ffem;

  const DividerLine({
    super.key,
    required this.fem,
    required this.ffem,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.fromLTRB(30 * fem, 0 * fem, 0 * fem, 0 * fem),
            child: const Divider(
              color: Color(0xff575757),
              thickness: 2.0,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.fromLTRB(5 * fem, 0 * fem, 5 * fem, 0 * fem),
          child: Text(
            'or',
            style: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 14 * ffem,
              fontWeight: FontWeight.w400,
              height: 1.5 * ffem / fem,
              color: const Color(0xff121212),
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 30 * fem, 0 * fem),
            child: const Divider(
              color: Color(0xff575757),
              thickness: 2.0,
            ),
          ),
        ),
      ],
    );
  }
}
