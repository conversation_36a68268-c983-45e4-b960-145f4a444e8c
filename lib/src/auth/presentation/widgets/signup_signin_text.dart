import 'package:flutter/material.dart';

class SignUpSignInText<PERSON>ield extends StatelessWidget {
  final String text;
  final IconData icon;
  final bool isPasswordType;
  final bool isValidationFailed;
  final String errText;
  final TextEditingController controller;
  final double fem;
  final double ffem;
  final Function(String) onChanged;
  final TextInputType keyboardType;
  final bool enabled;
  final FocusNode? focusNode;

  const SignUpSignInTextField(
      {super.key,
      required this.text,
      required this.icon,
      required this.isPasswordType,
      required this.isValidationFailed,
      required this.errText,
      required this.controller,
      required this.fem,
      required this.ffem,
      required this.onChanged,
      required this.keyboardType,
      this.focusNode,
      this.enabled = true});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 500 * fem,
      height: 54 * fem,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8 * fem),
          border: Border.all(color: const Color(0xff964ec2)),
        ),
        child: TextField(
          controller: controller,
          obscureText: isPasswordType,
          enableSuggestions: !isPasswordType,
          autocorrect: !isPasswordType,
          cursorColor: Colors.black54,
          style: const TextStyle(color: Colors.black),
          decoration: InputDecoration(
            prefixIcon: icon != null
                ? Icon(
                    icon,
                    color: Colors.white70,
                  )
                : null,
            labelText: text,
            errorText: isValidationFailed ? errText : null,
            errorBorder: isValidationFailed
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0 * fem),
                    borderSide: BorderSide.none,
                  )
                : null,
            labelStyle: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 18 * ffem,
              fontWeight: FontWeight.w500,
              height: 1.5 * ffem / fem,
              color: const Color(0xff575757),
            ),
            filled: true,
            floatingLabelBehavior: FloatingLabelBehavior.never,
            fillColor: Colors.white.withOpacity(0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30.0 * fem),
              borderSide: BorderSide.none,
            ),
          ),
          keyboardType: keyboardType,
          onChanged: onChanged,
          enabled: enabled,
          focusNode: focusNode,
        ),
      ),
    );
  }
}
