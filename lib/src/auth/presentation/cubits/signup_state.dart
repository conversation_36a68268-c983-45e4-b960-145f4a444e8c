part of 'signup_cubit.dart';

enum SignupStatus { initial, submitting, success, error, submitgooglelogin, googleSignIn, googleSignUp }

class SignupState extends Equatable {
  final String email;
  final String password;
  final String name;
  final int standard;
  final SignupStatus status;
  final String errorMessage;

  const SignupState(
      {required this.email,
      required this.password,
      required this.status,
      required this.name,
      required this.standard,
      this.errorMessage = ''});

  factory SignupState.initial() {
    return const SignupState(
      email: '',
      password: '',
      name: '',
      standard: 0,
      status: SignupStatus.initial,
    );
  }

  SignupState copyWith({
    String? email,
    String? password,
    String? name,
    int? standard,
    SignupStatus? status,
    String? errorMessage,
  }) {
    print(
        "AW:Inside signup state:copywith:$email : $name : $password : $standard : $status");
    print("AW:Inside signup state:copywith:this values= $this ");
    return SignupState(
      email: email ?? this.email,
      password: password ?? this.password,
      name: name ?? this.name,
      standard: standard ?? this.standard,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object> get props => [email, password, name, standard, status];
}
