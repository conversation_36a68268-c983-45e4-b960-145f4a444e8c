import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/features/user/domain/usecases/set_current_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_in.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  //final UserAuthRepository _authRepository;
  final SignIn _signIn;
  final SetCurrentUserProfile _setCurrentProfile;
  LoginCubit(
      {required SignIn signIn,
      required SetCurrentUserProfile setCurrentProfile})
      : _signIn = signIn,
        _setCurrentProfile = setCurrentProfile,
        super(LoginState.initial());

  //LoginCubit(this._authRepository) : super(LoginState.initial());

  void emailChanged(String value) {
    //print("AW::emailChanged:value::$value");
    emit(
      state.copyWith(
        email: value,
        status: LoginStatus.initial,
      ),
    );
  }

  void passwordChanged(String value) {
    //print("AW::passwordChanged:value::$value");
    emit(
      state.copyWith(
        password: value,
        status: LoginStatus.initial,
      ),
    );
  }

  Future<void> logInWithCredentialsString(String email, String password) async {
    try {
      /*  print("AW::logInWithCredentials:email::${state.email}");
      print("AW::logInWithCredentials:password::${state.password}");
      
      final result = await _signIn(
          SignInParams(email: state.email, password: state.password));

*/

      print("AW::logInWithCredentials:email::$email");
      print("AW::logInWithCredentials:password::$password");

      final result =
          await _signIn(SignInParams(email: email, password: password));

      result.fold((failure) => emit(state.copyWith(status: LoginStatus.error)),
          (success) {
        //TODO-The below call may not be needed as profile gets populated in signIn call itself.
        _setCurrentProfile();
        emit(state.copyWith(status: LoginStatus.success));
      });
    } catch (e, stackTrace) {
      print('AW::Exception caught: $e');
      print('AW::Stack trace:\n$stackTrace');
      emit(state.copyWith(status: LoginStatus.error));
    }
  }

  Future<void> logInWithCredentials() async {
    try {
      print("AW::logInWithCredentials:email::${state.email}");
      print("AW::logInWithCredentials:password::${state.password}");

      final result = await _signIn(
          SignInParams(email: state.email, password: state.password));

      result.fold((failure) => emit(state.copyWith(status: LoginStatus.error)),
          (success) {
        //TODO-The below call may not be needed as profile gets populated in signIn call itself.
        _setCurrentProfile();
        emit(state.copyWith(status: LoginStatus.success));
      });
    } catch (e, stackTrace) {
      print('AW::Exception caught: $e');
      print('AW::Stack trace:\n$stackTrace');
      emit(state.copyWith(status: LoginStatus.error));
    }
  }

  /*Future<void> logInWithCredentials1() async {
    try {
      print("AW::logInWithCredentials:email::${state.email}");
      print("AW::logInWithCredentials:password::${state.password}");
      await _authRepository.logInWithEmailAndPassword(
        email: state.email,
        password: state.password,
      );
      print("AW::logInWithCredentials:Inside success");
      emit(state.copyWith(status: LoginStatus.success));
    } catch (e, stackTrace) {
      print('AW::Exception caught: $e');
      print('AW::Stack trace:\n$stackTrace');
      emit(state.copyWith(status: LoginStatus.error));
    }
  }*/
}
