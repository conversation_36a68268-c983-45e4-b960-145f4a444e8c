// ignore_for_file: avoid_print

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/features/user/domain/usecases/set_current_profile.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/auth/domain/usecases/create_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/fetch_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/populate_default_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_up.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_up_google.dart';
//import 'package:skillapp/data/services/firebase_services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

part 'signup_state.dart';

class SignupCubit extends Cubit<SignupState> {
  final SignUp _signUp;
  final SignUpGoogle _signUpGoogle;
  final FetchProfile _fetchProfile;
  final CreateProfile _createProfile;
  static final _googleSignIn = GoogleSignIn();
  //final GoogleSignIn _googleSignIn;
  final SetCurrentUserProfile _setCurrentProfile;
  final PopulateDefaultProfile _populateDefaultProfile;

  SignupCubit(
      {required SignUp signUp,
      required SignUpGoogle signUpGoogle,
      required FetchProfile fetchProfile,
      required CreateProfile createProfile,
      required SetCurrentUserProfile setCurrentProfile,
      //required GoogleSignIn googleSignIn,
      required PopulateDefaultProfile populateDefaultProfile})
      : _signUp = signUp,
        _signUpGoogle = signUpGoogle,
        _fetchProfile = fetchProfile,
        _createProfile = createProfile,
        _setCurrentProfile = setCurrentProfile,
        //_googleSignIn = googleSignIn,
        _populateDefaultProfile = populateDefaultProfile,
        super(SignupState.initial());

  void emailChanged(String value) {
    //print("AW::Singup:emailChanged:$value");
    //print("AW::Singup:emailChanged:state=:$state");
    emit(
      state.copyWith(
        email: value,
        status: SignupStatus.initial,
      ),
    );
  }

  void passwordChanged(String value) {
    //print("AW::Singup:passwordChanged:$value");
    //print("AW::Singup:emailChanged:state=:$state");
    emit(
      state.copyWith(
        password: value,
        status: SignupStatus.initial,
      ),
    );
  }

  void nameChanged(String value) {
    //print("AW::Singup:NameChanged:$value");
    //print("AW::Singup:NameChanged:state=:$state");
    emit(
      state.copyWith(
        name: value,
        status: SignupStatus.initial,
      ),
    );
  }

  void standardChanged(int value) {
    //print("AW::Singup:standardChanged:standard=$value ");
    //print("AW::Singup:standardChanged:state=:$state");
    emit(
      state.copyWith(
        standard: value,
        status: SignupStatus.initial,
      ),
    );
  }

  Future<void> signupFormSubmitted() async {
    print("AW::signupFormSubmitted:state.email=${state.email}");
    print("AW::signupFormSubmitted:state.password=${state.password}");
    print("AW::signupFormSubmitted:state.status=${state.status}");
    if (state.status == SignupStatus.submitting) return;
    print(
        "AW::signupFormSubmitted:state.status is not submitting=${state.status}");
    emit(state.copyWith(status: SignupStatus.submitting));
    try {
      print(
          "AW::signupFormSubmitted:state.status before signup call=${state.status}");

      print("AW::signupFormSubmitted:before createUser=${state.status}");

      print("AW:Repo:createUser:name=$state.name");
      print("AW:Repo:createUser:email=$state.email");
      print("AW:Repo:createUser:standard=$state.standard");

      dynamic result = await _signUp(SignUpParams(
        email: state.email,
        password: state.password,
        standard: state.standard,
        name: state.name,
      ));

      result.fold(
          (failure) => emit(state.copyWith(
              status: SignupStatus.error,
              errorMessage: failure.message)), (success) {
        emit(state.copyWith(status: SignupStatus.success));
      });

      print("AW::signupFormSubmitted:after createUser=${state.status}");
    } catch (e, stackTrace) {
      print(
          "AW::signupFormSubmitted:state.status in exception=${state.status}");
      print('AW::Exception caught: $e');
      print('AW::Stack trace:\n$stackTrace');
      emit(state.copyWith(
          status: SignupStatus.error, errorMessage: e.toString()));
    }
  }

  // signup_cubit.dart

  Future<LocalUser> signInWithGoogle() async {
    emit(
      state.copyWith(status: SignupStatus.googleSignUp),
    );

    try {
      // If there is any previous login, signing out. This has been handled in logout also.
      await _googleSignIn.signOut();
      final GoogleSignInAccount? googleSignInAccount =
          await _googleSignIn.signIn();

      if (googleSignInAccount != null) {
        final GoogleSignInAuthentication googleSignInAuthentication =
            await googleSignInAccount.authentication;
        //TODO-Move GoogleAuthProvider to injection container
        final AuthCredential credential = GoogleAuthProvider.credential(
          accessToken: googleSignInAuthentication.accessToken,
          idToken: googleSignInAuthentication.idToken,
        );
        print("AW::SignpCubit:signInWithGoogle:credential=$credential");

        final Either<Failure, LocalUser> resEither =
            await _signUpGoogle(SignUpGoogleParams(
          credential: credential,
        ));

        print("AW::SignpCubit:signInWithGoogle:resEither=$resEither");
        return resEither.fold(
          (failure) {
            print("Unable to fetch credential: ${failure.message}");
            emit(state.copyWith(
                status: SignupStatus.error, errorMessage: failure.message));
            return LocalUser
                .empty; // Return null or handle failure case accordingly
          },
          (localUser) => localUser, // Return the user credential on success
        );
      } else {
        emit(state.copyWith(
            status: SignupStatus.error,
            errorMessage: 'Google Sign-In canceled'));
        return LocalUser.empty;
      }
    } catch (e, s) {
      print(e);
      debugPrintStack(stackTrace: s);
      emit(state.copyWith(
          status: SignupStatus.error,
          errorMessage: 'Google Sign-In failed: $e'));
      return LocalUser.empty;
    }
  }

  Future<Set<UserProfile>?> fetchProfile(String email) async {
    try {
      final Either<Failure, Set<UserProfile>> resEither =
          (await _fetchProfile(FetchProfileParams(email)));
      Set<UserProfile> profiles = resEither.getOrElse(() => {});
      if (profiles.isNotEmpty) {
        emit(state.copyWith(
            email: email, status: SignupStatus.submitgooglelogin));
      }

      return profiles;
    } catch (e) {
      emit(state.copyWith(
          status: SignupStatus.error,
          errorMessage: 'Error fetching profile: $e'));
    }
    return null;
  }

  Future<void> createProfile() async {
    print("AW::signupFormSubmitted:state.email=${state.email}");
    print("AW::signupFormSubmitted:state.status=${state.status}");
    try {
      dynamic result = await _createProfile(CreateProfileParams(
        email: state.email,
        standard: state.standard,
        name: state.name,
      ));
      _setCurrentProfile();

      result.fold(
          (failure) => emit(state.copyWith(
              status: SignupStatus.error,
              errorMessage: failure.message)), (success) {
        emit(state.copyWith(
            email: state.email,
            name: state.name,
            status: SignupStatus.success));
      });

      print("AW::Profile Created: Added Additional details=${state.status}");
    } catch (e, stackTrace) {
      print("AW::createProfile:state.status in exception=${state.status}");
      print('AW::Exception caught: $e');
      print('AW::Stack trace:\n$stackTrace');
      emit(state.copyWith(
          status: SignupStatus.error, errorMessage: e.toString()));
    }
  }
}
