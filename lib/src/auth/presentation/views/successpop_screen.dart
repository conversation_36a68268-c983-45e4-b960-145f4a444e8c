import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/landing_carousel.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';

class SuccessPopUpScreen extends StatefulWidget {
  const SuccessPopUpScreen({super.key});

  factory SuccessPopUpScreen.routeBuilder(_, __) {
    return const SuccessPopUpScreen();
  }

  @override
  State<SuccessPopUpScreen> createState() => _SuccessPopUpScreenState();
}

class _SuccessPopUpScreenState extends State<SuccessPopUpScreen> {
  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Scaffold(
      body: Stack(
        children: [
          Container(
            //width: MediaQuery.of(context).size.width,
            //height: MediaQuery.of(context).size.height,
            decoration: const BoxDecoration(
              gradient: signUpTopRightGradient,
            ),
          ),
          buildImageCarousel(context),
          DraggableScrollableSheet(
            initialChildSize: 0.75,
            minChildSize: 0.6,
            maxChildSize: 0.8,
            builder: (BuildContext context, ScrollController scrollController) {
              return Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(16 * fem)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 3,
                      blurRadius: 10,
                      offset: const Offset(0, -3),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    children: [
                      // Positioned Success Popup
                      Positioned(
                        left: 0,
                        top: 0,
                        right: 0,
                        bottom: 0,
                        child: Center(
                          child: Container(
                            padding: EdgeInsets.fromLTRB(
                                18 * fem, 12 * fem, 16 * fem, 32 * fem),
                            width: 377 * fem,
                            height: 600 * fem,
                            decoration: BoxDecoration(
                              color: const Color(0xffffffff),
                              borderRadius: BorderRadius.circular(24 * fem),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  margin: EdgeInsets.fromLTRB(
                                      152 * fem, 0 * fem, 151 * fem, 28 * fem),
                                  width: double.infinity,
                                  height: 4 * fem,
                                  decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.circular(2 * fem),
                                    color: const Color(0xff575757),
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.fromLTRB(
                                      39 * fem, 0 * fem, 0 * fem, 96 * fem),
                                  width: 282 * fem,
                                  height: 24 * fem,
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        left: 0 * fem,
                                        top: 0 * fem,
                                        child: Align(
                                          child: SizedBox(
                                            width: 282 * fem,
                                            height: 24 * fem,
                                            child: Text(
                                              'Successfully Created \nyour account',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                fontFamily: 'Poppins',
                                                fontSize: 16 * ffem,
                                                fontWeight: FontWeight.w500,
                                                height: 1.5 * ffem / fem,
                                                color: const Color(0xff121212),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        left: 14 * fem,
                                        top: 0 * fem,
                                        child: Align(
                                          child: SizedBox(
                                            width: 24 * fem,
                                            height: 24 * fem,
                                            child: Image.asset(
                                              'assets/images/checkcircle.png',
                                              width: 24 * fem,
                                              height: 24 * fem,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.fromLTRB(
                                      3 * fem, 0 * fem, 0 * fem, 60 * fem),
                                  width: 292 * fem,
                                  height: 292 * fem,
                                  child: Image.asset(
                                    'assets/images/boy-exploding-party-popper.png',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    context.go("/home");
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    height: 52 * fem,
                                    decoration: BoxDecoration(
                                      color: const Color(0xff50409a),
                                      borderRadius:
                                          BorderRadius.circular(26 * fem),
                                      boxShadow: [
                                        BoxShadow(
                                          color: const Color(0x3f000000),
                                          offset: Offset(0 * fem, 0 * fem),
                                          blurRadius: 4 * fem,
                                        ),
                                      ],
                                    ),
                                    child: Center(
                                      child: Text(
                                        'Explore now',
                                        style: TextStyle(
                                          fontFamily: 'Poppins',
                                          fontSize: 14 * ffem,
                                          fontWeight: FontWeight.w500,
                                          height: 1.5 * ffem / fem,
                                          color: const Color(0xffffffff),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
