import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../widgets/divider_line.dart';
import '../cubits/login_cubit.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import '../widgets/landing_carousel.dart';
import '../widgets/signup_signin_text.dart';
import '../widgets/signup_signin_button.dart';
import '../widgets/google_signin_button.dart';
import '../widgets/signup_option.dart';
import 'package:fluttertoast/fluttertoast.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  factory SignInScreen.routeBuilder(_, __) {
    return const SignInScreen();
  }

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final TextEditingController _emailTextController = TextEditingController();
  final TextEditingController _passwordTextController = TextEditingController();
  bool valPassFail = false, valEmailFail = false;
  String passErrTxt = "", emailErrText = "";

  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();

  @override
  void dispose() {
    _emailTextController.dispose();
    _passwordTextController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    void handleEmailChanged(String email) {
      _emailValidation();
      context.read<LoginCubit>().emailChanged(email);
      print('Handle Email changed');
    }

    void handlePasswordChanged(String password) {
      _pwdValidation();
      context.read<LoginCubit>().passwordChanged(password);
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocListener<LoginCubit, LoginState>(
        listener: (context, state) {
          if (state.status == LoginStatus.error) {
            Fluttertoast.showToast(
              msg: "Invalid Username or Password",
              gravity: ToastGravity.TOP,
              backgroundColor: Colors.red,
            );
          }
          if (state.status == LoginStatus.success) {
            context.go("/home");
          }
          if (state == LoginState.initial()) {}
        },
        //  child: GestureDetector(
        child: Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                gradient: signUpTopRightGradient,
              ),
            ),
            buildImageCarousel(context),
            DraggableScrollableSheet(
              initialChildSize: 0.75,
              minChildSize: 0.6,
              maxChildSize: 0.8,
              builder:
                  (BuildContext context, ScrollController scrollController) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey,
                        spreadRadius: 3,
                        blurRadius: 10,
                        offset: Offset(0, -3),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    controller: scrollController,
                    physics:
                        const ClampingScrollPhysics(), // Add this to prevent scroll issues
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                        3,
                        MediaQuery.of(context).size.height * 0.05,
                        3,
                        0,
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(30.0),
                          topRight: Radius.circular(30.0),
                        ),
                        child: Container(
                          padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(16)),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.3),
                                spreadRadius: 3,
                                blurRadius: 10,
                                offset: const Offset(0, -3),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Container(
                                margin: EdgeInsets.fromLTRB(
                                  0 * fem,
                                  0 * fem,
                                  2 * fem,
                                  16 * fem,
                                ),
                                child: Text(
                                  'Sign in',
                                  style: TextStyle(
                                    fontFamily: 'Poppins',
                                    fontSize: 20 * ffem,
                                    fontWeight: FontWeight.w500,
                                    height: 1.5 * ffem / fem,
                                    color: const Color(0xff121212),
                                  ),
                                ),
                              ),
                              SignUpSignInTextField(
                                text: "Email",
                                icon: Icons.person_outline,
                                isPasswordType: false,
                                isValidationFailed: valEmailFail,
                                errText: emailErrText,
                                controller: _emailTextController,
                                focusNode: _emailFocusNode,
                                fem: fem,
                                ffem: ffem,
                                onChanged: handleEmailChanged,
                                keyboardType: TextInputType.emailAddress,
                              ),
                              const SizedBox(height: 20),
                              SignUpSignInTextField(
                                text: "Password",
                                icon: Icons.person_outline,
                                isPasswordType: true,
                                isValidationFailed: valPassFail,
                                errText: passErrTxt,
                                controller: _passwordTextController,
                                fem: fem,
                                ffem: ffem,
                                focusNode: _passwordFocusNode,
                                onChanged: handlePasswordChanged,
                                keyboardType: TextInputType.visiblePassword,
                              ),
                              const SizedBox(
                                height: 30,
                              ),
                              SignUpSignInButton(
                                isLogin: true,
                                onTap: () {
                                  bool validationSuccess = _validateForm();

                                  if (validationSuccess == true) {
                                    /* context
                                        .read<LoginCubit>()
                                        .logInWithCredentialsString(
                                            _emailTextController.text,
                                            _passwordTextController.text);*/

                                    context
                                        .read<LoginCubit>()
                                        .logInWithCredentials();
                                  }
                                },
                                fem: fem,
                                ffem: ffem,
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                margin: EdgeInsets.fromLTRB(
                                  1 * fem,
                                  0 * fem,
                                  0 * fem,
                                  8 * fem,
                                ),
                                child: Text(
                                  'Forgot Password?',
                                  style: TextStyle(
                                    fontFamily: 'Poppins',
                                    fontSize: 14 * ffem,
                                    fontWeight: FontWeight.normal,
                                    height: 1.5 * ffem / fem,
                                    color: const Color(0xff050505),
                                  ),
                                ),
                              ),
                              DividerLine(fem: fem, ffem: ffem),
                              const SizedBox(
                                height: 20,
                              ),
                              const Align(
                                alignment: Alignment.center,
                                child: SizedBox(
                                  width: 480, // Set the desired width here
                                  child: GoogleSignInButton(),
                                ),
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              SignUpOption(
                                color: Colors.black,
                                text: "Don't have an account? Sign Up",
                                onTap: () {
                                  context.push("/signup");
                                },
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        // ),
      ),
      // ),
    );
  }

  bool _validateForm() {
    bool validation = true;

    if (_emailValidation() == false) {
      validation ? validation = false : null;
    }
    if (_pwdValidation() == false) {
      validation ? validation = false : null;
    }

    return validation;
  }

  bool _emailValidation() {
    if (_emailTextController.text.isEmpty) {
      setState(() {
        valEmailFail = true;
        emailErrText = "Email is mandatory";
      });
      return false;
    } else {
      final RegExp emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$',
      );
      if (emailRegex.hasMatch(_emailTextController.text)) {
        setState(() {
          valEmailFail = false;
          emailErrText = ""; // Reset the error text when the name is not empty
        });
      } else {
        setState(() {
          valEmailFail = true;
          emailErrText = "Email entered is not in valid format";
        });
        return false;
      }
    }
    return true;
  }

  bool _pwdValidation() {
    if (_passwordTextController.text.isEmpty) {
      setState(() {
        valPassFail = true;
        passErrTxt = "Password is mandatory";
      });
      return false;
    } else {
      setState(() {
        valPassFail = false;
        passErrTxt = ""; // Reset the error text when the name is not empty
      });
    }
    return true;
  }
}
