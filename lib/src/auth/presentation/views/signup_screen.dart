import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubits/signup_cubit.dart';
import '../widgets/signup_signin_text.dart';
import '../widgets/signup_signin_button.dart';
import '../widgets/divider_line.dart';
import '../widgets/google_signin_button.dart';
import '../widgets/signup_option.dart';
//import '../reusable_widgets/reusable_widgets.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  //static final GlobalKey<_SignUpScreenState> _key = GlobalKey<_SignUpScreenState>();

  factory SignUpScreen.routeBuilder(_, __) {
    return const SignUpScreen();
  }

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final TextEditingController _emailTextController = TextEditingController();
  final TextEditingController _passwordTextController = TextEditingController();
  final TextEditingController _userNameTextController = TextEditingController();
  final TextEditingController _standardTextController = TextEditingController();
  final TextEditingController _confirmPwdTextController =
      TextEditingController();
  bool valNameFail = false,
      valStandardFail = false,
      valPassFail = false,
      valConfirmPassFail = false,
      valEmailFail = false;
  String nameErrText = "",
      standardErrText = "",
      passErrTxt = "",
      confirmPassErrText = "",
      emailErrText = "";
  bool _agreedToTerms = false;

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    void handleEmailChanged(String email) {
      _emailValidation();
      context.read<SignupCubit>().emailChanged(email);
    }

    void handlePasswordChanged(String password) {
      _pwdValidation();
      context.read<SignupCubit>().passwordChanged(password);
    }

    void handleUserNameChanged(String name) {
      _nameValidation();
      context.read<SignupCubit>().nameChanged(name);
    }

    void handleStandardChanged(String standard) {
      _stdValidation();
      context.read<SignupCubit>().standardChanged(int.parse(standard));
    }

    void handleConfirmPasswordChanged(String password) {
      _confirmPwdValidation();
      context.read<SignupCubit>().passwordChanged(password);
    }

    return Scaffold(
      // key: SignUpScreen.key,
      extendBodyBehindAppBar: true,
      body: BlocListener<SignupCubit, SignupState>(
        listener: (context, state) {
          var snackBarMessage = "";
          if (state.status == SignupStatus.success) {
            context.go("/successpopup");
            // buildSuccessPopup(fem, ffem);
          } else if (state.status == SignupStatus.error) {
            snackBarMessage = "Error in signup";
            print("AW::Signuperror:status is error");
            var snackBar = SnackBar(
                content: Text(state.errorMessage), backgroundColor: Colors.red);
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          } else if (state.status != SignupStatus.initial ||
              state.status != SignupStatus.submitting) {
            snackBarMessage = "Something is not right!";
            //print('AW::Signup error $state.status');
          } else if (state.status != SignupStatus.submitgooglelogin) {
            context.go("/home");
            //print('AW::Signup error $state.status');
          }
        },
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                  15,
                  MediaQuery.of(context).size.height * 0.05,
                  15,
                  0,
                ),
                child: Container(
                  child: Column(
                    children: [
                      Container(
                        height: 2,
                        width: 20,
                        color: Colors.grey, // Grey box color
                      ),
                      const SizedBox(height: 15),
                      const Text(
                        'Create New Account',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 25),
                      SignUpSignInTextField(
                        text: "Name",
                        icon: Icons.person_outline,
                        isPasswordType: false,
                        isValidationFailed: valNameFail,
                        errText: nameErrText,
                        controller: _userNameTextController,
                        fem: fem,
                        ffem: ffem,
                        onChanged: handleUserNameChanged,
                        keyboardType: TextInputType.name,
                      ),
                      const SizedBox(height: 25),
                      SignUpSignInTextField(
                        text: "Standard",
                        icon: Icons.person_outline,
                        isPasswordType: false,
                        isValidationFailed: valStandardFail,
                        errText: standardErrText,
                        controller: _standardTextController,
                        fem: fem,
                        ffem: ffem,
                        onChanged: handleStandardChanged,
                        keyboardType: TextInputType.text,
                      ),
                      const SizedBox(height: 25),
                      SignUpSignInTextField(
                        text: "Email",
                        icon: Icons.person_outline,
                        isPasswordType: false,
                        isValidationFailed: valEmailFail,
                        errText: emailErrText,
                        controller: _emailTextController,
                        fem: fem,
                        ffem: ffem,
                        onChanged: handleEmailChanged,
                        keyboardType: TextInputType.emailAddress,
                      ),
                      const SizedBox(height: 25),
                      SignUpSignInTextField(
                        text: "Password",
                        icon: Icons.person_outline,
                        isPasswordType: true,
                        isValidationFailed: valPassFail,
                        errText: passErrTxt,
                        controller: _passwordTextController,
                        fem: fem,
                        ffem: ffem,
                        onChanged: handlePasswordChanged,
                        keyboardType: TextInputType.visiblePassword,
                      ),
                      const SizedBox(height: 25),
                      SignUpSignInTextField(
                        text: "Confirm Password",
                        icon: Icons.person_outline,
                        isPasswordType: false,
                        isValidationFailed: valConfirmPassFail,
                        errText: confirmPassErrText,
                        controller: _confirmPwdTextController,
                        fem: fem,
                        ffem: ffem,
                        onChanged: handleConfirmPasswordChanged,
                        keyboardType: TextInputType.visiblePassword,
                      ),
                      signupTermsandCondition(fem, ffem),
                      SignUpSignInButton(
                          isLogin: false,
                          onTap: () {
                            bool validationSuccess = _validateForm();
                            if (validationSuccess == true) {
                              context.read<SignupCubit>().signupFormSubmitted();
                            }
                          },
                          fem: fem,
                          ffem: ffem),
                      DividerLine(fem: fem, ffem: ffem),
                      const SizedBox(height: 10),
                      const GoogleSignInButton(),
                      const SizedBox(height: 20),
                      SignUpOption(
                        color: Colors.black,
                        text: "Already have an account? Sign in",
                        onTap: () {
                          context.push("/signin");
                        },
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Row signupTermsandCondition(double fem, double ffem) {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Transform.scale(
            scale: 0.8,
            child: Checkbox(
              value: _agreedToTerms,
              onChanged: (value) {
                // Change needs to be added here
                setState(() {
                  _agreedToTerms = value!;
                });
              },
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              // Handle terms and conditions tap event
              setState(() {
                _agreedToTerms = !_agreedToTerms;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(left: 10),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'I agree with ',
                      style: TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 15 * ffem,
                        fontWeight: FontWeight.w400,
                        height: 1.5 * ffem / fem,
                        color: const Color(0xff121212),
                      ),
                    ),
                    TextSpan(
                      text: 'terms and conditions',
                      style: TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 15 * ffem,
                        fontWeight: FontWeight.w400,
                        height: 1.5 * ffem / fem,
                        color: const Color(0xff964ec2),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool _validateForm() {
    bool validation = true;
    if (_nameValidation() == false) {
      validation ? validation = false : null;
    }
    if (_stdValidation() == false) {
      validation ? validation = false : null;
    }
    if (_emailValidation() == false) {
      validation ? validation = false : null;
    }
    if (_pwdValidation() == false) {
      validation ? validation = false : null;
    }
    if (_confirmPwdValidation() == false) {
      validation ? validation = false : null;
    }
    if (_validateAgreedTermsandCondition() == false) {
      validation ? validation = false : null;
    }

    return validation;
  }

  bool _nameValidation() {
    if (_userNameTextController.text.isEmpty) {
      setState(() {
        valNameFail = true;
        nameErrText = "Name is mandatory";
      });
      return false;
    } else {
      setState(() {
        valNameFail = false;
        nameErrText = ""; // Reset the error text when the name is not empty
      });
    }
    return true;
  }

  bool _emailValidation() {
    if (_emailTextController.text.isEmpty) {
      setState(() {
        valEmailFail = true;
        emailErrText = "Email is mandatory";
      });
      return false;
    } else {
      final RegExp emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$',
      );
      if (emailRegex.hasMatch(_emailTextController.text)) {
        setState(() {
          valEmailFail = false;
          emailErrText = ""; // Reset the error text when the name is not empty
        });
      } else {
        setState(() {
          valEmailFail = true;
          emailErrText = "Email entered is not in valid format";
        });
        return false;
      }
    }
    return true;
  }

  bool _stdValidation() {
    if (_standardTextController.text.isEmpty) {
      setState(() {
        valStandardFail = true;
        standardErrText = "Standard is mandatory";
      });
      return false;
    } else {
      setState(() {
        valStandardFail = false;
        standardErrText = ""; // Reset the error text when the name is not empty
      });
    }
    return true;
  }

  bool _pwdValidation() {
    if (_passwordTextController.text.isEmpty) {
      setState(() {
        valPassFail = true;
        passErrTxt = "Password is mandatory";
      });
      return false;
    } else if (_passwordTextController.text.length < 6) {
      setState(() {
        valPassFail = true;
        passErrTxt = "Password length should be minimum 6";
      });
      return false;
    } else if (_passwordTextController.text != _confirmPwdTextController.text &&
        _confirmPwdTextController.text.isNotEmpty) {
      setState(() {
        valPassFail = true;
        passErrTxt = "Password and confirm password is not Matching";
      });
      return false;
    } else {
      setState(() {
        valPassFail = false;
        passErrTxt = ""; // Reset the error text when the name is not empty
        if (_passwordTextController.text == _confirmPwdTextController.text &&
            _confirmPwdTextController.text.isNotEmpty) {
          valConfirmPassFail = false;
          confirmPassErrText = "";
        }
      });
    }
    return true;
  }

  bool _confirmPwdValidation() {
    if (_confirmPwdTextController.text.isEmpty) {
      setState(() {
        valConfirmPassFail = true;
        confirmPassErrText = "Confirm Password is mandatory";
      });
      return false;
    } else if (_passwordTextController.text != _confirmPwdTextController.text &&
        _passwordTextController.text.isNotEmpty) {
      setState(() {
        valConfirmPassFail = true;
        confirmPassErrText = "Confirm Password and password is not Matching";
      });
      return false;
    } else {
      setState(() {
        valConfirmPassFail = false;
        confirmPassErrText =
            ""; // Reset the error text when the name is not empty
        if (_passwordTextController.text == _confirmPwdTextController.text &&
            _passwordTextController.text.isNotEmpty) {
          valPassFail = false;
          passErrTxt = "";
        }
      });
    }
    return true;
  }

  bool _validateAgreedTermsandCondition() {
    if (_agreedToTerms == false) {
      var snackBarMessage = "Please accept the terms and conditions";
      var snackBar =
          SnackBar(content: Text(snackBarMessage), backgroundColor: Colors.red);
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return false;
    }
    return true;
  }
}
