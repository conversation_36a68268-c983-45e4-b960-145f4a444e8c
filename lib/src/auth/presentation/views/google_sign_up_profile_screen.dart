import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubits/signup_cubit.dart';
import '../widgets/signup_signin_text.dart';
import '../widgets/signup_signin_button.dart';

class GoogleSignUpProfileScreen extends StatefulWidget {
  final String email;
  final String name;

  const GoogleSignUpProfileScreen(
      {super.key, required this.email, required this.name});

  factory GoogleSignUpProfileScreen.routeBuilder(
      BuildContext context, GoRouterState state, String email, String name) {
    return GoogleSignUpProfileScreen(email: email, name: name);
  }

  @override
  State<GoogleSignUpProfileScreen> createState() =>
      _GoogleSignUpProfileScreen();
}

class _GoogleSignUpProfileScreen extends State<GoogleSignUpProfileScreen> {
  final TextEditingController _emailTextController = TextEditingController();
  final TextEditingController _userNameTextController = TextEditingController();
  final TextEditingController _standardTextController = TextEditingController();
  bool valNameFail = false, valStandardFail = false, valEmailFail = false;
  String nameErrText = "", standardErrText = "", emailErrText = "";
  bool _agreedToTerms = false;

  @override
  void initState() {
    super.initState();
    _emailTextController.text = widget.email;
    _userNameTextController.text = widget.name;
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    void handleUserNameChanged(String name) {
      _nameValidation();
      context.read<SignupCubit>().nameChanged(name);
    }

    void handleStandardChanged(String standard) {
      _stdValidation();
      context.read<SignupCubit>().standardChanged(int.parse(standard));
    }

    void handleEmailChanged(String email) {
      _emailValidation();
      context.read<SignupCubit>().emailChanged(email);
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocListener<SignupCubit, SignupState>(
        listener: (context, state) {
          var snackBarMessage = "";
          if (state.status == SignupStatus.success) {
            context.go("/successpopup");
          } else if (state.status == SignupStatus.error) {
            snackBarMessage = "Error in signup";
            var snackBar = SnackBar(
                content: Text(state.errorMessage), backgroundColor: Colors.red);
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          } else if (state.status != SignupStatus.initial ||
              state.status != SignupStatus.submitting) {
            snackBarMessage = "Something is not right!";
          }
        },
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              15,
              MediaQuery.of(context).size.height * 0.05,
              15,
              0,
            ),
            child: Column(
              children: [
                Container(
                  height: 2,
                  width: 20,
                  color: Colors.grey,
                ),
                const SizedBox(height: 15),
                const Text(
                  'You are almost there',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 25),
                SignUpSignInTextField(
                    text: "Name",
                    icon: Icons.person_outline,
                    isPasswordType: false,
                    isValidationFailed: valNameFail,
                    errText: nameErrText,
                    controller: _userNameTextController,
                    fem: fem,
                    ffem: ffem,
                    onChanged: handleUserNameChanged,
                    keyboardType: TextInputType.name,
                    enabled: false),
                const SizedBox(height: 25),
                SignUpSignInTextField(
                    text: "Email",
                    icon: Icons.person_outline,
                    isPasswordType: false,
                    isValidationFailed: valEmailFail,
                    errText: emailErrText,
                    controller: _emailTextController,
                    onChanged: handleEmailChanged,
                    fem: fem,
                    ffem: ffem,
                    keyboardType: TextInputType.emailAddress,
                    enabled: false),
                const SizedBox(height: 25),
                SignUpSignInTextField(
                  text: "Standard",
                  icon: Icons.person_outline,
                  isPasswordType: false,
                  isValidationFailed: valStandardFail,
                  errText: standardErrText,
                  controller: _standardTextController,
                  fem: fem,
                  ffem: ffem,
                  onChanged: handleStandardChanged,
                  keyboardType: TextInputType.text,
                ),
                signupTermsandCondition(fem, ffem),
                SignUpSignInButton(
                  isLogin: false,
                  onTap: () {
                    bool validationSuccess = _validateForm();
                    handleEmailChanged(widget.email);
                    handleUserNameChanged(widget.name);
                    if (validationSuccess == true) {
                      context.read<SignupCubit>().createProfile();
                    }
                  },
                  fem: fem,
                  ffem: ffem,
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Row signupTermsandCondition(double fem, double ffem) {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Transform.scale(
            scale: 0.8,
            child: Checkbox(
              value: _agreedToTerms,
              onChanged: (value) {
                setState(() {
                  _agreedToTerms = value!;
                });
              },
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _agreedToTerms = !_agreedToTerms;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(left: 10),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'I agree with ',
                      style: TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 15 * ffem,
                        fontWeight: FontWeight.w400,
                        height: 1.5 * ffem / fem,
                        color: const Color(0xff121212),
                      ),
                    ),
                    TextSpan(
                      text: 'terms and conditions',
                      style: TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 15 * ffem,
                        fontWeight: FontWeight.w400,
                        height: 1.5 * ffem / fem,
                        color: const Color(0xff964ec2),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool _validateForm() {
    bool validation = true;
    if (_nameValidation() == false) {
      validation ? validation = false : null;
    }
    if (_stdValidation() == false) {
      validation ? validation = false : null;
    }
    if (_emailValidation() == false) {
      validation ? validation = false : null;
    }

    if (_validateAgreedTermsandCondition() == false) {
      validation ? validation = false : null;
    }

    return validation;
  }

  bool _nameValidation() {
    if (_userNameTextController.text.isEmpty) {
      setState(() {
        valNameFail = true;
        nameErrText = "Name is mandatory";
      });
      return false;
    } else {
      setState(() {
        valNameFail = false;
        nameErrText = "";
      });
    }
    return true;
  }

  bool _emailValidation() {
    if (_emailTextController.text.isEmpty) {
      setState(() {
        valEmailFail = true;
        emailErrText = "Email is mandatory";
      });
      return false;
    } else {
      final RegExp emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$',
      );
      if (emailRegex.hasMatch(_emailTextController.text)) {
        setState(() {
          valEmailFail = false;
          emailErrText = "";
        });
      } else {
        setState(() {
          valEmailFail = true;
          emailErrText = "Email entered is not in valid format";
        });
        return false;
      }
    }
    return true;
  }

  bool _stdValidation() {
    if (_standardTextController.text.isEmpty) {
      setState(() {
        valStandardFail = true;
        standardErrText = "Standard is mandatory";
      });
      return false;
    } else {
      setState(() {
        valStandardFail = false;
        standardErrText = "";
      });
    }
    return true;
  }

  bool _validateAgreedTermsandCondition() {
    if (_agreedToTerms == false) {
      var snackBarMessage = "Please accept the terms and conditions";
      var snackBar =
          SnackBar(content: Text(snackBarMessage), backgroundColor: Colors.red);
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return false;
    }
    return true;
  }
}
