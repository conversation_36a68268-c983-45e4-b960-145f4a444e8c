import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/auth/presentation/cubits/login_cubit.dart';
import 'package:skillapp/src/auth/presentation/cubits/signup_cubit.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/auth/presentation/widgets/desktop/landing_page_carousel.dart';
import '../widgets/landing_carousel.dart';
import '../widgets/google_signin_button.dart';
import '../widgets/signup_option.dart';

class LandingScreen extends StatefulWidget {
  const LandingScreen({super.key});

  factory LandingScreen.routeBuilder(_, __) {
    return const LandingScreen();
  }

  @override
  State<LandingScreen> createState() => _LandingScreenState();
}

class _LandingScreenState extends State<LandingScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return const AdaptiveLayout(
        mobileBody: LandingScreenMobile(),
        tabletBody: LandingScreentablet(),
        desktopBody: LandingScreenDesktop());

    /* return MultiBlocProvider(
      providers: [
        BlocProvider<SignupCubit>(
          create: (context) => sl<SignupCubit>(),
        ),
        // Add more bloc providers if needed
      ],
      child: Scaffold(
        body: BlocListener<SignupCubit, SignupState>(
          listener: (context, state) {
            if (state.status == SignupStatus.submitgooglelogin) {
              context.go("/home");
            }
          },
          child: Stack(
            children: [
              // background gradient
              Container(
                decoration: const BoxDecoration(
                  gradient: onBoarding1TopRightGradient,
                ),
              ),
              // rectangular box
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  buildLandingCarousel(context),
                  const SizedBox(height: 20),
                  GoogleSignInButton(),
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 35),
                    child: SignUpOption(
                      color: Colors.white,
                      text: "Sign Up with email",
                      onTap: () {
                        context.go("/signup");
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 30),
                    child: SignUpOption(
                      color: Colors.white,
                      text: "Already have an account? Sign in",
                      onTap: () {
                        context.go("/signin");
                      },
                    ),
                  ),
                ],
              ),
              buildImageCarousel(context),
            ],
          ),
        ),
      ),
    );*/
  }
}

class LandingScreenMobile extends StatelessWidget {
  const LandingScreenMobile({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SignupCubit>(
          create: (context) => sl<SignupCubit>(),
        ),
        // Add more bloc providers if needed
      ],
      child: Scaffold(
        body: BlocListener<SignupCubit, SignupState>(
          listener: (context, state) {
            if (state.status == SignupStatus.submitgooglelogin) {
              context.go("/home");
            }
          },
          child: Stack(
            children: [
              // background gradient
              Container(
                decoration: const BoxDecoration(
                  gradient: onBoarding1TopRightGradient,
                ),
              ),
              // rectangular box
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  buildLandingCarousel(context),
                  const SizedBox(height: 20),
                  const GoogleSignInButton(),
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 35),
                    child: SignUpOption(
                      color: Colors.white,
                      text: "Sign Up with email",
                      onTap: () {
                        context.go("/signup");
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 30),
                    child: SignUpOption(
                      color: Colors.white,
                      text: "Already have an account? Sign in",
                      onTap: () {
                        context.go("/signin");
                      },
                    ),
                  ),
                ],
              ),
              buildImageCarousel(context),
            ],
          ),
        ),
      ),
    );
  }
}

class LandingScreentablet extends StatelessWidget {
  const LandingScreentablet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: const Text("This is tablet view"),
    );
  }
}

class LandingScreenDesktop extends StatefulWidget {
  const LandingScreenDesktop({super.key});

  @override
  _LandingScreenDesktopState createState() => _LandingScreenDesktopState();
}

class _LandingScreenDesktopState extends State<LandingScreenDesktop> {
  final TextEditingController _emailTextController = TextEditingController();
  final TextEditingController _passwordTextController = TextEditingController();
  bool valPassFail = false, valEmailFail = false;
  String passErrTxt = "", emailErrText = "";
  bool _isPasswordVisible = false; // Track password visibility

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          scrollDirection: Axis.vertical,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: _buildContent(context),
            ),
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context) {
    // Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    void handleEmailChanged(String email) {
      _emailValidation();
      context.read<LoginCubit>().emailChanged(email);
    }

    void handlePasswordChanged(String password) {
      _pwdValidation();
      context.read<LoginCubit>().passwordChanged(password);
    }

    return Scaffold(
      body: BlocListener<LoginCubit, LoginState>(
        listener: (BuildContext context, LoginState state) {
          if (state.status == LoginStatus.error) {
            // Show toast message with error.
            print("Error in signin");
            Fluttertoast.showToast(
              msg: "Invalid Username or Password",
              gravity: ToastGravity.TOP,
              backgroundColor: Colors.red,
            );
          }
          if (state.status == LoginStatus.success) {
            print("AW::Signin success");
            /*Navigator.push(context,
              MaterialPageRoute(builder: (context) => const HomeScreen()));*/
            context.go("/home");
          }
        },
        child: Row(
          children: [
            Expanded(
              child: Container(
                clipBehavior: Clip.antiAlias,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.43, -0.90),
                    end: Alignment(-0.43, 0.9),
                    colors: [Color(0xFF9A4090), Color(0xFF070123)],
                  ),
                ),
                child: Column(
                  children: [
                    LandingPageCarouselDesktop(),
                  ],
                ),
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  const SizedBox(
                    height: 110,
                  ),
                  const DefaultTextStyle(
                    style: TextStyle(
                      color: Color(0xFF121212),
                      fontSize: 32,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                      height: 0,
                    ),
                    child: Text(
                      'Sign in',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Container(
                    // padding: EdgeInsets.fromLTRB(164 * fem, 64, 164 * fem, 50),
                    padding: EdgeInsets.fromLTRB(164 * fem, 64, 164 * fem, 50),
                    child: Column(
                      children: [
                        TextField(
                          controller: _emailTextController,
                          style: const TextStyle(color: Colors.black),
                          onChanged: handleEmailChanged,
                          obscureText: false,
                          enableSuggestions: false,
                          autocorrect: false,
                          decoration: InputDecoration(
                            labelText: 'Email',
                            prefixIcon: const Icon(
                              Icons.person_outline,
                              color: Color(0xff575757),
                            ),
                            errorText: valEmailFail ? emailErrText : null,
                            errorBorder: valEmailFail
                                ? const OutlineInputBorder(
                                    // borderRadius: BorderRadius.circular(30.0),
                                    borderSide: BorderSide(color: Colors.red),
                                  )
                                : null,
                            labelStyle: const TextStyle(
                              fontFamily: 'Poppins',
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              height: 1.5,
                              color: Color(0xff575757),
                            ),
                            filled: true,
                            floatingLabelBehavior: FloatingLabelBehavior.never,
                            fillColor: Colors.white.withOpacity(0.3),
                            /* enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  const BorderSide(color: Color(0xff964ec2)),
                            ),*/
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  const BorderSide(color: Color(0xff964ec2)),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 52,
                        ),
                        TextField(
                          controller: _passwordTextController,
                          style: const TextStyle(color: Colors.black),
                          onChanged: handlePasswordChanged,
                          obscureText: !_isPasswordVisible,
                          enableSuggestions: false,
                          autocorrect: false,
                          onSubmitted: (value) {
                            // Trigger the sign-in logic when Enter is pressed
                            if (_validateForm()) {
                              context.read<LoginCubit>().logInWithCredentials();
                            }
                          },
                          decoration: InputDecoration(
                            labelText: 'Password',
                            suffixIcon: IconButton(
                              icon: Icon(
                                _isPasswordVisible
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                              ),
                              //const Icon(Icons.remove_red_eye),
                              onPressed: () {
                                setState(() {
                                  _isPasswordVisible = !_isPasswordVisible;
                                });
                              },
                            ),
                            prefixIcon: Icons.person_outline != null
                                ? const Icon(
                                    Icons.person_outline,
                                    color: Color(0xff575757),
                                  )
                                : null,
                            errorText: valPassFail ? passErrTxt : null,
                            errorBorder: valPassFail
                                ? const OutlineInputBorder(
                                    // borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.red),
                                  )
                                : null,
                            labelStyle: const TextStyle(
                              fontFamily: 'Poppins',
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              height: 1.5,
                              color: Color(0xff575757),
                            ),
                            filled: true,
                            floatingLabelBehavior: FloatingLabelBehavior.never,
                            fillColor: Colors.white.withOpacity(0.3),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  const BorderSide(color: Color(0xff964ec2)),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 42,
                        ),
                        FractionallySizedBox(
                          widthFactor:
                              1.0, // Makes the button width equal to the parent's width

                          child: ElevatedButton(
                            onPressed: () {
                              bool validationSuccess = _validateForm();

                              if (validationSuccess == true) {
                                context
                                    .read<LoginCubit>()
                                    .logInWithCredentials();
                              }
                              //   context.read<LoginCubit>().logInWithCredentials();
                            },
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(
                                  const Color(0xFF50409A)),
                              padding: WidgetStateProperty.all<EdgeInsets>(
                                const EdgeInsets.symmetric(vertical: 20),
                              ),
                              shape: WidgetStateProperty.all<
                                  RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(28),
                                ),
                              ),
                            ),
                            child: const Text(
                              'Sign In',
                              style: TextStyle(fontSize: 18),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        GestureDetector(
                          onTap: _forgotPassword,
                          child: const Text(
                            'Forgot Password?',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF121212),
                              fontSize: 16,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              decoration: TextDecoration.underline,
                              height: 0,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ), // Space between link and divider
                        const Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: Color(0xFF575757),
                                thickness: 1,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8.0),
                              child: Text(
                                'Or',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Color(0xFF121212),
                                  fontSize: 16,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w400,
                                  height: 0,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Divider(
                                color: Color(0xFF575757),
                                thickness: 1,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 20,
                        ), // Space between link and divider
                        FractionallySizedBox(
                          widthFactor:
                              1.0, // Makes the button width equal to the parent's width
                          child: ElevatedButton(
                            onPressed: _signIn,
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(
                                  const Color(0xFF9A4090)),
                              padding: WidgetStateProperty.all<EdgeInsets>(
                                const EdgeInsets.symmetric(vertical: 20),
                              ),
                              shape: WidgetStateProperty.all<
                                  RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(28),
                                ),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  'assets/images/google.png',
                                  height: 26,
                                  width: 27,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Continue with Google',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: () {
                                context.push("/signup");
                              },
                              child: const Text(
                                "Don't have an account? Sign Up",
                                style: TextStyle(
                                  color: Colors.black,
                                  fontFamily: 'Poppins',
                                  fontStyle: FontStyle.normal,
                                  fontWeight: FontWeight.normal,
                                  fontSize: 20,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  void _signIn() {
    // Add your sign-in logic here
    print("Sign-In button pressed");
    //  context.read<LoginCubit>().logInWithCredentials();
  }

  bool _emailValidation() {
    if (_emailTextController.text.isEmpty) {
      setState(() {
        valEmailFail = true;
        emailErrText = "Email is mandatory";
      });
      return false;
    } else {
      final RegExp emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$',
      );
      if (emailRegex.hasMatch(_emailTextController.text)) {
        setState(() {
          valEmailFail = false;
          emailErrText = ""; // Reset the error text when the name is not empty
        });
      } else {
        setState(() {
          valEmailFail = true;
          emailErrText = "Email entered is not in valid format";
        });
        return false;
      }
    }
    return true;
  }

  bool _pwdValidation() {
    if (_passwordTextController.text.isEmpty) {
      setState(() {
        valPassFail = true;
        passErrTxt = "Password is mandatory";
      });
      return false;
    } else {
      setState(() {
        valPassFail = false;
        passErrTxt = ""; // Reset the error text when the name is not empty
      });
    }
    return true;
  }

  bool _validateForm() {
    bool validation = true;

    if (_emailValidation() == false) {
      validation ? validation = false : null;
    }
    if (_pwdValidation() == false) {
      validation ? validation = false : null;
    }

    return validation;
  }

  void _forgotPassword() {}
}
