part of 'app_bloc.dart';

enum AppStatus { authenticated, unauthenticated }

class AppState extends Equatable {
  final AppStatus status;
  final LocalUser userProfile;

  const AppState._({required this.status, this.userProfile = LocalUser.empty});

  const AppState.authenticated(LocalUser userProfile)
      : this._(status: AppStatus.authenticated, userProfile: userProfile);

  //const AppState.unauthenticated() : this._(status: AppStatus.unauthenticated);
  const AppState.unauthenticated()
      : this._(status: AppStatus.unauthenticated, userProfile: LocalUser.empty);

  @override
  List<Object> get props => [status, userProfile];

  AppState.fromJson(Map<String, dynamic> json)
      : status = json['status'] == 'Yes'
            ? AppStatus.authenticated
            : AppStatus.unauthenticated,
        userProfile = LocalUser(
            email: json['email'],
            id: json['uid'],
            currentProfile: UserProfile(
                id: json['profileId'],
                name: json['name'],
                standard: json['standard'],
                level: 0));

  Map<String, dynamic> toJson() {
    print("AW:AppState:toJson:userProfile= $userProfile and status= $status");
    return {
      'uid': userProfile.id,
      'email': userProfile.email,
      'authenticated': status == AppStatus.authenticated ? "Yes" : "No",
      'profileId': userProfile.currentProfile.id,
      'name': userProfile.currentProfile.name,
      'standard': userProfile.currentProfile.standard,
      'level': userProfile.currentProfile.level
    };
  }
}
