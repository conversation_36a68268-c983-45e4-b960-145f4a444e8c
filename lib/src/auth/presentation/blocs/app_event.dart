part of 'app_bloc.dart';

abstract class AppEvent extends Equatable {
  const AppEvent();

  @override
  List<Object> get props => [];
}

class AppUnauthenticated extends AppEvent {}

class AppAuthenticated extends AppEvent {
  final LocalUser _userProfile;

  const AppAuthenticated({required LocalUser userProfile})
      : _userProfile = userProfile;

  @override
  List<Object> get props => [_userProfile];
}

class AppLogoutRequested extends AppEvent {}

class AppUserChanged extends AppEvent {
  const AppUserChanged(this.userProfile);

  final LocalUser userProfile;

  @override
  List<Object> get props => [userProfile];
}
