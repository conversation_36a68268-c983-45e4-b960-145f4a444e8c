// ignore_for_file: avoid_print

import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/features/user/domain/usecases/get_current_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/get_user_auth_changes.dart';
import 'package:skillapp/src/auth/domain/usecases/populate_default_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_out.dart';

part 'app_event.dart';
part 'app_state.dart';

class AppBloc extends Bloc<AppEvent, AppState> with HydratedMixin {
  //final UserAuthRepository _authRepository;
  StreamSubscription<LocalUser>? _userSubscription;
  final GetCurrentProfile _getCurrentProfile;
  final GetUserAuthChanges _getUserAuthChanges;
  final PopulateDefaultProfile _populateDefaultProfile;
  final SignOut _signOut;

  AppBloc({
    required GetCurrentProfile getCurrentProfile,
    required GetUserAuthChanges getUserAuthChanges,
    required PopulateDefaultProfile populateDefaultProfile,
    required SignOut signOut,
    required FirebaseAuth firebaseAuth,
  })  : _getCurrentProfile = getCurrentProfile,
        _getUserAuthChanges = getUserAuthChanges,
        _populateDefaultProfile = populateDefaultProfile,
        _signOut = signOut,
        super(const AppState.unauthenticated()) {
    /*super(
          firebaseAuth.currentUser == null
              ? const AppState.unauthenticated()
              : AppState.authenticated(
                  LocalUser(
                    id: firebaseAuth.currentUser!.uid,
                    email: firebaseAuth.currentUser!.email ?? '',
                    currentProfile: const UserProfile.empty(),
                  ),
                ),
        ) {*/
    /*_getCurrentProfile().then((profile) {
      profile.fold(
        (failure) => add(AppUnauthenticated()),
        (localuser) => null,
      );
    });*/
    print("JB:Printing from app bloc");
    on<AppUserChanged>(_onUserChanged);
    on<AppLogoutRequested>(_onLogoutRequested);
    on<AppUnauthenticated>(_onAppUnauthenticated);
    on<AppAuthenticated>(_onAppAuthenticated);

    //TODO- Enable this back later!

    _getUserAuthChanges().listen((userProfileDetails) {
      print(
          "AW:Inside AppBloc: _getUserAuthChanges: userProfileDetails= $userProfileDetails");
      userProfileDetails.fold(
        (failure) => null,
        //TODO- Important - This is to be enabled back. Commenting out since this was getting invoked twice.
        /*add(
            
            const AppUserChanged(LocalUser.empty),
            
            ),*/
        (localuser) => add(
          AppUserChanged(localuser),
        ),
      );

      /*add(
        const AppUserChanged(LocalUser.empty),
      );*/
    });
  }

  void _onUserChanged(
    AppUserChanged event,
    Emitter<AppState> emit,
  ) async {
    print("AW:_onUserChanged:event.userProfile= ${event.userProfile}");
    try {
      final result = await _getCurrentProfile();
      print("AW:_onUserChanged:event.result= $result");
      result.fold((failure) {
        print("AW:_onUserChanged:inside failure:failure= $failure");
        emit(
          const AppState.unauthenticated(),
        );
      }, (success) {
        print(
            "AW:_onUserChanged:after default profile populate:success= $success");
        print(
            "AW:_onUserChanged:inside success:event.userProfile.email= ${event.userProfile.email}");
        print(
            "AW:_onUserChanged:inside success:event.userProfile.id= ${event.userProfile.id}");
        // Check if user has valid email before populating profile
        if (event.userProfile.email.isNotEmpty &&
            event.userProfile.id.isNotEmpty) {
          _populateDefaultProfile(PopulateDefaultProfileParams(
            email: event.userProfile.email,
            uid: event.userProfile.id,
          ));
          print("AW:_onUserChanged:after default profile populate");
          emit(
            AppState.authenticated(success),
          );
        } else {
          print(
              "AW:_onUserChanged:User email or ID is empty, emitting unauthenticated state");
          emit(
            const AppState.unauthenticated(),
          );
        }
      });
    } catch (e, s) {
      print("AW:_onUserChanged:Exception= $e");
      debugPrintStack(stackTrace: s);
    }
  }

  void _onLogoutRequested(
    AppLogoutRequested event,
    Emitter<AppState> emit,
  ) {
    emit(
      const AppState.unauthenticated(),
    );
    unawaited(_signOut());
  }

  @override
  Future<void> close() {
    _userSubscription?.cancel();
    return super.close();
  }

  @override
  AppState fromJson(Map<String, dynamic> json) {
    print("AW:AppBloc:fromJson:json= $json");
    try {
      if (json['authenticated'] != 'Yes') {
        print("AW:AppBloc:fromJson:UNauthenticated flow");
        return const AppState.unauthenticated();
      } else {
        print("AW:AppBloc:fromJson:AUTHenticated flow");
        LocalUser userProfileDetails = LocalUser(
          email: json['email'],
          id: json['uid'],
          currentProfile: UserProfile(
            id: json['profileId'],
            name: json['name'],
            standard: json['standard'],
            level: json['level'],
          ),
        );

        return AppState.authenticated(userProfileDetails);
      }
    } catch (e) {
      print("AW:fromJson:Exception $e");
    }

    print("AW:returning efault");
    return const AppState.unauthenticated();
  }

  @override
  Map<String, dynamic>? toJson(AppState state) {
    print("AW:AppBloc:toJson:state= $state");

    try {
      print("AW:chke2");
      return {
        'uid': state.userProfile.id,
        'email': state.userProfile.email,
        'authenticated': state.status == AppStatus.authenticated ? "Yes" : "No",
        'profileId': state.userProfile.currentProfile.id,
        'name': state.userProfile.currentProfile.name,
        'standard': state.userProfile.currentProfile.standard,
        'level': state.userProfile.currentProfile.level
      };
    } catch (e) {
      print("AW:chke1");
      print("AW:Inside exception ${e.toString()}");
    }
    print("AW:Return empty string in toJson");
    return {};
  }

  bool _validateUserData(Map<String, dynamic> json) {
    //Add code to check if all fields in json has valid values.
    return true;
  }

  FutureOr<void> _onAppUnauthenticated(
      AppUnauthenticated event, Emitter<AppState> emit) {
    emit(
      const AppState.unauthenticated(),
    );
  }

  FutureOr<void> _onAppAuthenticated(
      AppAuthenticated event, Emitter<AppState> emit) {
    emit(
      AppState.authenticated(event._userProfile),
    );
  }
}
