import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class SignUp extends FutureUsecaseWithParams<LocalUser, SignUpParams> {
  final AuthRepo _authRepo;

  SignUp({required AuthRepo authRepo}) : _authRepo = authRepo;

  @override
  ResultFuture<LocalUser> call(SignUpParams params) async {
    return await _authRepo.register(
      email: params.email,
      password: params.password,
      name: params.name,
      standard: params.standard,
    );
  }
}

class SignUpParams extends Equatable {
  final String email;
  final String password;
  final String name;
  final int standard;

  const SignUpParams({
    required this.email,
    required this.password,
    required this.name,
    required this.standard,
  });

  const SignUpParams.empty()
      : email = '',
        password = '',
        name = '',
        standard = 0;

  @override
  List<Object?> get props => [email, name];
}
