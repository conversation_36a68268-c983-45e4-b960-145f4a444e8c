import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class PopulateDefaultProfile
    implements
        FutureUsecaseWithParams<LocalUser, PopulateDefaultProfileParams> {
  final AuthRepo _authRepo;
  final CacheContext _cacheContext;

  PopulateDefaultProfile(
      {required AuthRepo authRepo, required CacheContext cacheContext})
      : _authRepo = authRepo,
        _cacheContext = cacheContext;

  @override
  ResultFuture<LocalUser> call(PopulateDefaultProfileParams params) async {
    final result = await _authRepo.populateDefaultProfileIfMissing(
        params.uid, params.email);

    result.fold(
      (failure) => _cacheContext.setCurrentUser(LocalUser.empty),
      (currentUser) => _cacheContext.setCurrentUser(currentUser),
    );
    return result;
  }
}

class PopulateDefaultProfileParams extends Equatable {
  final String uid;
  final String email;

  const PopulateDefaultProfileParams({required this.uid, required this.email});

  @override
  List<Object?> get props => [uid, email];
}
