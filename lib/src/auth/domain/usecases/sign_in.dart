import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class SignIn extends FutureUsecaseWithParams<LocalUser, SignInParams> {
  final AuthRepo _authRepo;

  SignIn({required AuthRepo authRepo}) : _authRepo = authRepo;

  @override
  ResultFuture<LocalUser> call(SignInParams params) async {
    return await _authRepo.login(
      email: params.email,
      password: params.password,
    );
  }
}

class SignInParams extends Equatable {
  final String email;
  final String password;

  const SignInParams({
    required this.email,
    required this.password,
  });

  const SignInParams.empty()
      : email = '',
        password = '';

  @override
  List<Object?> get props => [email, password];
}
