import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class CreateProfile extends FutureUsecaseWithParams<LocalUser, CreateProfileParams> {
  final AuthRepo _authRepo;

  CreateProfile({required AuthRepo authRepo}) : _authRepo = authRepo;

  @override
  ResultFuture<LocalUser> call(CreateProfileParams params) async {
    return await _authRepo.createProfile(
      email: params.email,
      name: params.name,
      standard: params.standard,
    );
  }
}

class CreateProfileParams extends Equatable {
  final String email;
  final String name;
  final int standard;

  const CreateProfileParams({
    required this.email,
      required this.name,
    required this.standard,
  });

  const CreateProfileParams.empty()
      : email = '',
        name = '',
        standard = 0;

  @override
  List<Object?> get props => [email, name, standard];
}
