import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class FetchProfile extends FutureUsecaseWithParams<Object, FetchProfileParams> {
  final AuthRepo _authRepo;

  FetchProfile({required AuthRepo authRepo}) : _authRepo = authRepo;

  @override
  ResultFuture<Set<UserProfile>> call(FetchProfileParams params) async {
    return await _authRepo.fetchUserProfile(params.email);
  }
}

class FetchProfileParams {
  final String email;

  const FetchProfileParams(this.email);

  //const SignUpParams.empty() : credential = AuthCredential(accessToken: '', idToken: '');

  @override
  List<Object> get props => [email];
}
