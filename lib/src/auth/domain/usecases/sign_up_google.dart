import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class SignUpGoogle
    extends FutureUsecaseWithParams<LocalUser, SignUpGoogleParams> {
  final AuthRepo _authRepo;

  SignUpGoogle({required AuthRepo authRepo}) : _authRepo = authRepo;

  @override
  ResultFuture<LocalUser> call(SignUpGoogleParams params) async {
    return await _authRepo.registerWithGoogle(
      credential: params.credential,
    );
  }
}

class SignUpGoogleParams extends Equatable {
  final AuthCredential credential;

  const SignUpGoogleParams({required this.credential});

  // Updated the empty constructor to properly initialize the credential field
  //const SignUpGoogleParams.empty() : credential = AuthCredential(accessToken: '', idToken: '');

  @override
  List<Object?> get props => [credential];
}
