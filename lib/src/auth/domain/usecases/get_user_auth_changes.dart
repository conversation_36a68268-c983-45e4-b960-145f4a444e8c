import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';

class GetUserAuthChanges extends StreamUsecaseWithoutParams<LocalUser> {
  const GetUserAuthChanges({required AuthRepo authRepo}) : _authRepo = authRepo;
  final AuthRepo _authRepo;

  @override
  ResultStream<LocalUser> call() => _authRepo.getUserAuthChanges();
}
