import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:firebase_auth/firebase_auth.dart';

abstract class AuthRepo {
  const AuthRepo();

  ResultFuture<LocalUser> login({
    required String email,
    required String password,
  });

  ResultFuture<LocalUser> register(
      {required String email,
      required String password,
      required String name,
      required int standard});

  ResultFuture<LocalUser> createProfile(
      {required String email, required String name, required int standard});

  ResultFuture<void> logout();

  ResultStream<LocalUser> getUserAuthChanges();

  ResultFuture<LocalUser> registerWithGoogle(
      {required AuthCredential credential});

  ResultFuture<Set<UserProfile>> fetchUserProfile(String email);

  ResultFuture<LocalUser> populateDefaultProfileIfMissing(
      String uid, String email);
}
