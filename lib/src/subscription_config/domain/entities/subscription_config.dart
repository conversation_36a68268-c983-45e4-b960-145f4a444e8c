/// Represents the subscription configuration parameters.
class SubscriptionConfig {
  final int maxDailyPracticeCount;
  final int? maxSectionalTestMaths;
  final int? maxSectionalTestThinking;
  final int? monthlySectionalTestCredit;

  SubscriptionConfig({
    required this.maxDailyPracticeCount,
    this.maxSectionalTestMaths,
    this.maxSectionalTestThinking,
    this.monthlySectionalTestCredit,
  });

  factory SubscriptionConfig.fromMap(Map<String, dynamic> map) {
    return SubscriptionConfig(
      maxDailyPracticeCount: map['max_daily_practice_count'] ?? 0,
      maxSectionalTestMaths: map['max_sectional_test_maths'],
      maxSectionalTestThinking: map['max_sectional_test_thinking'],
      monthlySectionalTestCredit: map['monthly_sectional_test_credit'],
    );
  }
}
