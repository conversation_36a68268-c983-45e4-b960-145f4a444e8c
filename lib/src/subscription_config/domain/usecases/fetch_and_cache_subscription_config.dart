import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';
import '../entities/subscription_config.dart';
import '../repos/subscription_config_repo.dart';

/// Use case to fetch and cache subscription config.
class FetchAndCacheSubscriptionConfigUseCase
    extends FutureUsecaseWithoutParams<SubscriptionConfig> {
  final CacheContext _cacheContext;
  final SubscriptionConfigRepository _repository;

  FetchAndCacheSubscriptionConfigUseCase({
    required CacheContext cacheContext,
    required SubscriptionConfigRepository repository,
  })  : _cacheContext = cacheContext,
        _repository = repository;

  @override
  ResultFuture<SubscriptionConfig> call() async {
    try {
      // Try to get config from cache
      final cachedConfig = await _cacheContext.get('subscription_config');
      if (cachedConfig != null && cachedConfig is SubscriptionConfig) {
        return Right(cachedConfig);
      }

      String subscriptionType = _cacheContext.subscriptionType;
      if (subscriptionType.isEmpty) {
        // Try to fetch subscriptionType from database using user email
        final String email = _cacheContext.email;
        if (email.isEmpty) {
          return const Left(CacheFailure(
              message: 'User email not found in cache context',
              statusCode: 404));
        }
        // This assumes repository has a method to fetch subscriptionType by email
        final fetchedType =
            await _repository.fetchSubscriptionTypeByEmail(email);
        if (fetchedType == null || fetchedType.isEmpty) {
          return const Left(CacheFailure(
              message: 'Subscription type not found in database',
              statusCode: 404));
        }
        subscriptionType = fetchedType;
        _cacheContext.setSubscriptionType(subscriptionType);
        //_cacheContext.set('subscriptionType', subscriptionType);
      }

      // Fetch from Firebase if not in cache
      final config = await _repository.getSubscriptionConfig(subscriptionType);
      if (config == null) {
        return const Left(ServerFailure(
            message: 'Subscription config not found in Firebase',
            statusCode: 404));
      }
      _cacheContext.set('subscription_config', config);
      return Right(config);
    } catch (e, s) {
      print('Error fetching subscription config: $e');
      print('Stack trace: $s');
      return const Left(ServerFailure(
          message: 'Unknown error fetching subscription config',
          statusCode: 500));
    }
  }
}

// You will need to implement or inject the following:
// - CacheContext: with getSubscriptionType(), getSubscriptionConfig(), setSubscriptionConfig()
// - FirebaseRemoteConfigDataSource: with fetchSubscriptionConfig(String type)
// - Failure: error class for use case failures
