import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';
import '../repos/attempted_question_count_repo.dart';

/// Use case to fetch attempted question count for today.
class FetchAttemptedQuestionCountUseCase
    extends FutureUsecaseWithoutParams<int> {
  final AttemptedQuestionCountRepo _attemptedQuestionCountRepo;

  FetchAttemptedQuestionCountUseCase(
      {required AttemptedQuestionCountRepo attemptedQuestionCountRepo})
      : _attemptedQuestionCountRepo = attemptedQuestionCountRepo;

  @override
  ResultFuture<int> call() async {
    try {
      final count =
          await _attemptedQuestionCountRepo.getAttemptedQuestionCountForToday();
      return Right(count);
    } catch (e) {
      return const Left(ServerFailure(
        message: 'Unknown error fetching attempted question count',
        statusCode: 500,
      ));
    }
  }
}
