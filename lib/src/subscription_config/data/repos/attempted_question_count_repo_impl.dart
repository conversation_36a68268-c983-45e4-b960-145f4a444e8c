import 'package:skillapp/core/common/cache/context_cache.dart';

import '../../domain/repos/attempted_question_count_repo.dart';
import '../datasources/attempted_question_count_remote_datasource.dart';

class AttemptedQuestionCountRepoImpl implements AttemptedQuestionCountRepo {
  final AttemptedQuestionCountRemoteDatasource _remoteDatasource;
  final CacheContext _cachecontext;

  AttemptedQuestionCountRepoImpl(
      AttemptedQuestionCountRemoteDatasource remoteDatasource,
      CacheContext cacheContext)
      : _remoteDatasource = remoteDatasource,
        _cachecontext = cacheContext;

  @override
  Future<int> getAttemptedQuestionCountForToday() async {
    return await _remoteDatasource
        .getAttemptedQuestionCountForToday(_cachecontext.profileId);
  }
}
