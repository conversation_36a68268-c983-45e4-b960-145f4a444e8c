import 'package:dartz/dartz.dart';
import 'package:flutter/widgets.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/streaks/data/datasources/streaks_remote_datasource.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class StreakDetailsRepoImpl implements StreakDetailsRepo {
  final StreaksRemoteDataSource _streaksRemoteDataSource;
  final StreaksCache _streakCache;
  final CacheContext _cacheContext;

  StreakDetailsRepoImpl(
      {required StreaksRemoteDataSource streaksRemoteDataSource,
      required StreaksCache streakCache,
      required CacheContext cacheContext})
      : _streaksRemoteDataSource = streaksRemoteDataSource,
        _streakCache = streakCache,
        _cacheContext = cacheContext;

  @override
  ResultFuture<Streak> fetchStreakFromId(String currentStreakId) async {
    try {
      final result = await _streaksRemoteDataSource.fetchStreakFromId(
          _cacheContext.profileId, currentStreakId);

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<CurrentStreak> getCurrentStreakInfo() async {
    try {
      CurrentStreak currentStreak = await _streaksRemoteDataSource
          .getCurrentStreakInfo(_cacheContext.profileId);
      StreakStatus status =
          await _streaksRemoteDataSource.getCurrentStreakStatus(
              _cacheContext.profileId, currentStreak.currentStreakId);
      currentStreak = currentStreak.copyWith(status: status);
      _streakCache.setCurrentStreak(currentStreak);
      print(_streakCache.getCurrentStreak());
      print("StreakId in cache ${_streakCache.getCurrentStreakId()}");
      return Right(currentStreak);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<StreakStatus> getCurrentStreakStatus(String streakId) async {
    try {
      StreakStatus result = await _streaksRemoteDataSource
          .getCurrentStreakStatus(_cacheContext.profileId, streakId);
      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<List<StreakConfig>> fetchStreakConfigs() async {
    try {
      final result = await _streaksRemoteDataSource.fetchStreakConfigs();
      _streakCache.setStreakConfigList(result);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<StreakConfig> fetchStreakConfigById(String id) async {
    try {
      final result = await _streaksRemoteDataSource.fetchStreakConfigById(id);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateCurrentStreakData(String streakId,
      int currentStreakCount, StreakStatus streakStatus) async {
    try {
      final result = await _streaksRemoteDataSource.updateCurrentStreakData(
          _cacheContext.profileId, streakId, currentStreakCount, streakStatus);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateCurrentStreakId(String streakId) async {
    try {
      final result = await _streaksRemoteDataSource.updateCurrentStreakId(
          _cacheContext.profileId, streakId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateCurrentStreakStatus(
      StreakStatus streakStatus) async {
    try {
      final result = await _streaksRemoteDataSource.updateCurrentStreakStatus(
          _cacheContext.profileId, streakStatus);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateCurrentStreakCount(int currentStreakCount) async {
    try {
      final result = await _streaksRemoteDataSource.updateCurrentStreakCount(
          _cacheContext.profileId, currentStreakCount);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> markStreakDayEntryAsCompleted(
      String streakId, String streakDay, StreakEntryStatus completed) async {
    try {
      final result = _streaksRemoteDataSource.updateStreakDayStatus(
          _cacheContext.profileId, streakId, streakDay, completed);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> addStreakDayEntryToStreak(
      String streakId, String streakDay) async {
    try {
      final result = _streaksRemoteDataSource.addNewStreakDay(
          _cacheContext.profileId, streakId, streakDay);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<Streak> addNewStreak(StreakType type) async {
    try {
      final result = await _streaksRemoteDataSource.addNewStreakForTheUser(
          _cacheContext.profileId,
          type == StreakType.notAvailable ? StreakType.infinity : type);
      //_streakCache.setCurrentStreak(result);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<CompleteStreaksData> fetchCompleteStreaksData() async {
    try {
      List<Streak> result =
          await _streaksRemoteDataSource.fetchCompleteStreaksData(
        _cacheContext.profileId,
      );
      return Right(CompleteStreaksData(
          streaks: result,
          combinedStreakEntries: _formCombinedStreakEntries(result)));
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  Set<StreakEntry> _formCombinedStreakEntries(List<Streak> result) {
    Set<StreakEntry> combinedStreakEntries = {};
    for (var streak in result) {
      combinedStreakEntries.addAll(streak.streakEntries);
    }

    return combinedStreakEntries;
  }

  @override
  ResultFuture<StreakEntry> getCurrentDayStreakStatus(
      String currentStreakId) async {
    //call getStreakDayStatus with today's date in YYYYMMDD format
    DateTime now = DateTime.now();
    String formattedDate =
        '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';

    final result = await getStreakDayStatus(currentStreakId, formattedDate);
    result.fold((l) => l, (r) {
      _streakCache.setCurrentDayStreak(r);
    });
    return result;
  }

  @override
  ResultFuture<StreakEntry> getStreakDayStatus(
      String streakId, String date) async {
    try {
      //call getStreakDayStatus with today's date in YYYYMMDD format
      StreakEntry result = await _streaksRemoteDataSource.getStreakDayStatus(
          _cacheContext.profileId, streakId, date);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateStreakDayDetails(String streakId, String streakDayId,
      StreakEntryStatus status, int completedCount) async {
    try {
      final result =
          await _streaksRemoteDataSource.updateStreakDayStatusAndCounter(
              _cacheContext.profileId,
              streakId,
              streakDayId,
              status,
              completedCount);
      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateStreakDayDetailsAndIncrementCacheCount(
      String streakId,
      String streakDayId,
      StreakEntryStatus status,
      int completedCount) async {
    try {
      final result = await updateStreakDayDetails(
          streakId, streakDayId, status, completedCount);
      _streakCache.incrementCurrentDayStreakCount();
      if (status == StreakEntryStatus.completed) {
        updateCurrentStreakCount(
            _streakCache.getCurrentStreak().currentStreakLength + 1);
      }
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> markStreakAsInactive(String streakId) async {
    try {
      final result = await _streaksRemoteDataSource.markStreakAsInactive(
          _cacheContext.profileId, streakId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }
}
