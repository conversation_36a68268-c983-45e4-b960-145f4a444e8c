import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/widgets.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';

abstract class StreaksRemoteDataSource {
  Future<CurrentStreak> getCurrentStreakInfo(String profileId);
  Future<Streak> fetchStreakFromId(String profileId, String currentStreakId);

  Future<List<StreakConfig>> fetchStreakConfigs();
  Future<StreakConfig> fetchStreakConfigById(String id);

  Future<Streak> addNewStreakForTheUser(String profileId, StreakType type);
  Future<void> addNewStreakDay(
      String profileId, String streakId, String entryId);
  Future<void> updateStreakDayStatus(String profileId, String streakId,
      String entryId, StreakEntryStatus status);

  Future<void> updateStreakDayStatusAndCounter(
      String profileId,
      String streakId,
      String entryId,
      StreakEntryStatus status,
      int newCounterValue);

  Future<void> updateCurrentStreakData(String profileId, String currentStreakId,
      int currentStreak, StreakStatus status);

  Future<void> updateCurrentStreakId(String profileId, String currentStreakId);

  Future<void> updateCurrentStreakCount(
      String profileId, int currentStreakCount);

  Future<void> updateCurrentStreakStatus(String profileId, StreakStatus status);

  Future<List<Streak>> fetchCompleteStreaksData(String profileId);

  Future<StreakEntry> getStreakDayStatus(
      String profileId, String streakId, String date);

  Future<StreakStatus> getCurrentStreakStatus(
      String profileId, String streakId);

  Future<void> markStreakAsInactive(String profileId, String streakId);
}

class StreakRemoteDataSourceImpl extends StreaksRemoteDataSource {
  final FirebaseFirestore _firestore;

  StreakRemoteDataSourceImpl({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<CurrentStreak> getCurrentStreakInfo(String profileId) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> currentStreakDoc = await _firestore
          .collection('streaks_information')
          .doc(profileId)
          .get();
      if (currentStreakDoc.exists) {
        Map<String, dynamic> currentStreakData = currentStreakDoc.data()!;
        return CurrentStreak(
          currentStreakLength: currentStreakData['current_streak'],
          longestStreakLength:
              currentStreakData['longest_streak'], //Not being used
          currentStreakId: currentStreakData['current_streak_id'],
          type: currentStreakData['type'] != null
              ? stringToType(currentStreakData['type'])
              : StreakType.notAvailable,
          status: currentStreakData['status'] != null
              ? stringToStatus(currentStreakData['status'])
              : StreakStatus.notAvailable,
          streak: const Streak.empty(),
          currentWeekStreakEntries: const {},
        );
      } else {
        throw const ServerException(
            message: 'No streaks found', statusCode: '404');
      }
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<StreakStatus> getCurrentStreakStatus(
      String profileId, String streakId) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> streakDoc = await _firestore
          .collection('streaks_information/$profileId/streaks')
          .doc(streakId)
          .get();

      if (streakDoc.exists) {
        Map<String, dynamic> streakDocData = streakDoc.data()!;

        return streakDocData['status'] != null
            ? stringToStatus(streakDocData['status'])
            : StreakStatus.notAvailable;
      } else {
        throw const ServerException(
            message: 'Unable to fetch streak status', statusCode: '500');
      }
    } on FirebaseException catch (e) {
      debugPrintStack(stackTrace: e.stackTrace);
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<Streak> fetchStreakFromId(
      String profileId, String currentStreakId) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> streakDoc = await _firestore
          .collection('streaks_information/$profileId/streaks')
          .doc(currentStreakId)
          .get();

      if (streakDoc.exists) {
        Map<String, dynamic> streakDocData = streakDoc.data()!;
        QuerySnapshot<Map<String, dynamic>> streakSnapshot = await _firestore
            .collection(
                'streaks_information/$profileId/streaks/$currentStreakId/streak_days')
            .get();

        List<StreakEntry> streakEntries = streakSnapshot.docs.map((streakDay) {
          Map<String, dynamic> data = streakDay.data();
          return StreakEntry(
            streakDay: int.parse(streakDay.id),
            status: stringToStreakEntryStatus(data['status']),
            targetCount: data['target_count'],
            completedCount: data['completed_count'],
          );
        }).toList();

        return Streak(
            id: currentStreakId,
            streakEntries: streakEntries,
            type: streakDocData['type'] != null
                ? stringToType(streakDocData['type'])
                : StreakType.notAvailable,
            status: streakDocData['status'] != null
                ? stringToStatus(streakDocData['status'])
                : StreakStatus.notAvailable);
      } else {
        return const Streak.empty();
      }
    } on FirebaseException catch (e) {
      debugPrintStack(stackTrace: e.stackTrace);
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<List<StreakConfig>> fetchStreakConfigs() async {
    try {
      QuerySnapshot<Map<String, dynamic>> sn =
          await _firestore.collection('streaks_config').get();
      return sn.docs.map((doc) {
        doc as Map<String, dynamic>;
        return StreakConfig(
          id: doc.id,
          name: doc.get('name'),
          config: getConfigData(doc as Map<String, dynamic>),
        );
      }).toList();
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<StreakConfig> fetchStreakConfigById(String id) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> doc =
          await _firestore.collection('streaks_config').doc(id).get();
      return StreakConfig(
        id: doc.id,
        name: doc.get('name'),
        config: getConfigData(doc as Map<String, dynamic>),
      );
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  Map<StreakConfigTypes, dynamic> getConfigData(Map<String, dynamic> doc) {
    Map<StreakConfigTypes, dynamic> config = {};
    doc.forEach((key, value) {
      if (streakConfigTypeToStringMap.containsKey(key)) {
        config[streakConfigTypeToStringMap[key]!] = value;
      }
    });
    return config;
  }

  @override
  Future<Streak> addNewStreakForTheUser(
      String profileId, StreakType type) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> profileDoc = await _firestore
          .collection('streaks_information')
          .doc(profileId)
          .get();
      if (!profileDoc.exists) {
        await _firestore.collection('streaks_information').doc(profileId).set({
          'current_streak_id': '',
          'longest_streak': 0,
          'current_streak': 0,
        });
      }

      String streakId = DateTime.now().microsecondsSinceEpoch.toString();
      await _firestore
          .collection('streaks_information/$profileId/streaks')
          .doc(streakId)
          .set(
        {
          'type': type.name,
          'status': StreakStatus.active.name,
          'timeStamp': DateTime.now()
        },
      );

      await updateCurrentStreakId(profileId, streakId);

      return Streak(
          id: streakId,
          streakEntries: const [],
          type: type,
          status: StreakStatus.active);
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<void> addNewStreakDay(
      String profileId, String streakId, String entryId) async {
    try {
      print("AW::addNewStreakDay:streakId::$streakId");
      print("AW::addNewStreakDay:entryId::$entryId");

      //In a for loop add 5 entries from 29-Jul to 2-Aug
      //TODO-Delete the below code after testing
      /*var list = ["20240729", "20240730", "20240731", "20240801", "20240802"];
      for (int i = 0; i < 5; i++) {
        DateTime date = DateTime(2022, 7, 29).add(Duration(days: i));
        String entryId = list[i];
        await _firestore
            .collection(
                'streaks_information/$profileId/streaks/$streakId/streak_days')
            .doc(entryId)
            .set({
          'timeStamp': date,
          'completed_count': 5,
          'target_count': 5,
          'status': StreakEntryStatus.completed.name,
        });
      }*/
      await _firestore
          .collection(
              'streaks_information/$profileId/streaks/$streakId/streak_days')
          .doc(entryId)
          .set({
        'timeStamp': DateTime.now(),
        'completed_count': 0,
        'target_count': 5,
        //TODO-Change above line to read from config
        'status': StreakEntryStatus.notStarted.name
      });
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<void> updateStreakDayStatus(String profileId, String streakId,
      String entryId, StreakEntryStatus status) async {
    try {
      await _firestore
          .collection(
              'streaks_information/$profileId/streaks/$streakId/streak_days')
          .doc(entryId)
          .update({'status': status.name});
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<void> updateStreakDayStatusAndCounter(
      String profileId,
      String streakId,
      String entryId,
      StreakEntryStatus status,
      int newCounterValue) async {
    try {
      await _firestore
          .collection(
              'streaks_information/$profileId/streaks/$streakId/streak_days')
          .doc(entryId)
          .update({'status': status.name, 'completed_count': newCounterValue});
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<void> updateCurrentStreakId(
      String profileId, String currentStreakId) async {
    updateCurrentStreakData(
        profileId, currentStreakId, -1, StreakStatus.notAvailable);
  }

  @override
  Future<void> updateCurrentStreakCount(
      String profileId, int currentStreakCount) async {
    updateCurrentStreakData(
        profileId, '', currentStreakCount, StreakStatus.notAvailable);
  }

  @override
  Future<void> updateCurrentStreakStatus(
      String profileId, StreakStatus status) async {
    updateCurrentStreakData(profileId, '', -1, status);
  }

  @override
  Future<void> updateCurrentStreakData(String profileId, String currentStreakId,
      int currentStreak, StreakStatus status) async {
    try {
      Map<String, dynamic> data = {};
      if (currentStreak != -1) {
        data['current_streak'] = currentStreak;
      }
      if (currentStreakId.isNotEmpty) {
        data['current_streak_id'] = currentStreakId;
      }

      if (status != StreakStatus.notAvailable) {
        data['current_streak_status'] = status.name;
      }

      await _firestore
          .collection('streaks_information')
          .doc(profileId)
          .update(data);
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<List<Streak>> fetchCompleteStreaksData(String profileId) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> streakProfile = await _firestore
          .collection('streaks_information')
          .doc(profileId)
          .get();

      if (streakProfile.exists) {
        QuerySnapshot<Map<String, dynamic>> streaksSnapshot = await _firestore
            .collection('streaks_information/$profileId/streaks')
            .get();

        List<String> streaksIdList =
            streaksSnapshot.docs.map((e) => e.id).toList();
        List<Streak> streaks = [];
        for (String streakId in streaksIdList) {
          Streak streak = await fetchStreakFromId(profileId, streakId);
          streaks.add(streak);
        }

        return streaks;
      } else {
        return const [];
      }
    } on FirebaseException catch (e) {
      debugPrintStack(stackTrace: e.stackTrace);
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<StreakEntry> getStreakDayStatus(
      String profileId, String streakId, String date) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> streakDay = await _firestore
          .collection(
              'streaks_information/$profileId/streaks/$streakId/streak_days')
          .doc(date)
          .get();

      if (streakDay.exists) {
        Map<String, dynamic> data = streakDay.data()!;
        return StreakEntry(
          streakDay: int.parse(streakDay.id),
          status: stringToStreakEntryStatus(data['status']),
          targetCount: data['target_count'],
          completedCount: data['completed_count'],
        );
      } else {
        throw ServerException(
            message:
                "Streak entry not present for the date $date under streak - $streakId",
            statusCode: '400');
      }
    } on FirebaseException catch (e) {
      debugPrintStack(stackTrace: e.stackTrace);
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<void> markStreakAsInactive(String profileId, String streakId) async {
    try {
      Map<String, dynamic> data = {
        'status': StreakStatus.inactive.name,
        'timeStamp': DateTime.now()
      };

      await _firestore
          .collection('streaks_information/$profileId/streaks')
          .doc(streakId)
          .update(data);
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }
}
