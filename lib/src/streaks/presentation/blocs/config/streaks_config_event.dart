part of 'streaks_config_bloc.dart';

class StreaksConfigEvent extends Equatable {
  const StreaksConfigEvent();

  @override
  List<Object?> get props => [];
}

class FetchStreakConfigByIdEvent extends StreaksConfigEvent {
  final String streakConfigId;
  const FetchStreakConfigByIdEvent({required this.streakConfigId});

  @override
  List<Object?> get props => [streakConfigId];
}

class FetchAllStreaksConfigsEvent extends StreaksConfigEvent {
  const FetchAllStreaksConfigsEvent();
}
