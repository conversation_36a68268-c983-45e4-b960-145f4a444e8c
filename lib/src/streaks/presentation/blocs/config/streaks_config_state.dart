part of 'streaks_config_bloc.dart';

class StreakConfigStateInitial extends Equatable {
  const StreakConfigStateInitial();

  @override
  List<Object?> get props => [];
}

class StreakConfigState extends StreakConfigStateInitial {
  final StreakConfig streakConfig;

  const StreakConfigState({required this.streakConfig});

  @override
  List<Object?> get props => [streakConfig.id];
}

class StreakConfigList extends StreakConfigStateInitial {
  final List<StreakConfig> streakConfigList;

  const StreakConfigList({required this.streakConfigList});

  @override
  List<Object?> get props => [streakConfigList.length];
}

class StreakConfigErrorState extends StreakConfigStateInitial {
  final String errorMessage;

  const StreakConfigErrorState({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}
