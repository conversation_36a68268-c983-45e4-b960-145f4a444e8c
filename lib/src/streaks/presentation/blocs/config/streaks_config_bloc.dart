import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/usecases/config/fetch_streak_config_by_id.dart';
import 'package:skillapp/src/streaks/domain/usecases/config/fetch_streak_config_data.dart';

part 'streaks_config_event.dart';
part 'streaks_config_state.dart';

class StreaksConfigBloc
    extends Bloc<StreaksConfigEvent, StreakConfigStateInitial> {
  final FetchStreakConfigById _fetchStreakConfigById;
  final FetchStreakConfigsData _fetchStreakConfigsData;

  StreaksConfigBloc({
    required FetchStreakConfigById fetchStreakConfigById,
    required FetchStreakConfigsData fetchStreakConfigsData,
  })  : _fetchStreakConfigById = fetchStreakConfigById,
        _fetchStreakConfigsData = fetchStreakConfigsData,
        super(const StreakConfigStateInitial()) {
    on<FetchStreakConfigByIdEvent>(_fetchStreakConfigByIdEventHandler);
    on<FetchAllStreaksConfigsEvent>(_fetchAllStreakAConfigsEventHandler);
  }

  FutureOr<void> _fetchStreakConfigByIdEventHandler(
      FetchStreakConfigByIdEvent event,
      Emitter<StreakConfigStateInitial> emit) async {
    final result = await _fetchStreakConfigById(event.streakConfigId);
    result.fold(
        (failure) =>
            emit(StreakConfigErrorState(errorMessage: failure.errorMessage)),
        (streakConfig) => emit(StreakConfigState(streakConfig: streakConfig)));
  }

  FutureOr<void> _fetchAllStreakAConfigsEventHandler(
      FetchAllStreaksConfigsEvent event,
      Emitter<StreakConfigStateInitial> emit) async {
    final result = await _fetchStreakConfigsData();
    result.fold(
        (failure) =>
            emit(StreakConfigErrorState(errorMessage: failure.errorMessage)),
        (streakConfigList) =>
            emit(StreakConfigList(streakConfigList: streakConfigList)));
  }
}
