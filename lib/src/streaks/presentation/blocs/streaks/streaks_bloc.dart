import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/add_current_day_entry_to_current_streak.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/fetch_current_day_streak_entry.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/increment_streakday_along_with_adding_if_not_present.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/add_new_streak_for_the_user.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/add_streak_day_to_a_streak.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/mark_streak_day_as_completed.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_complete_streaks_data.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_details.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_entries.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_summary.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_streak_details.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/update_current_streak_details.dart';

part 'streaks_event.dart';
part 'streaks_state.dart';

class StreaksBloc extends Bloc<StreaksEvent, StreaksStateInitial> {
  final AddStreakDayEntryToStreak _addNewDayToStreak;
  final AddCurrentDayEntryToCurrentStreak _addCurrentDayEntryToCurrentStreak;
  final FetchCurrentDayStreakEntry _fetchCurrentDayStreakEntry;
  final IncrementCurrentStreakDayAlongWithAddingIfNotPresent
      _incrementCurrentStreakDayAlongWithAddingIfNotPresent;
  final MarkStreakDayEntryAsCompleted _markStreakDayEntryAsCompleted;

  final AddNewStreakForTheUser _addNewStreakForTheUser;

  final FetchCompleteStreaksData _fetchCompleteStreaksData;
  final FetchCurrentStreakSummary _fetchCurrentStreakSummary;
  final FetchCurrentStreakDetails _fetchCurrentStreakDetails;
  final FetchStreakDetails _fetchStreakDetails;
  final UpdateCurrentStreakData _updateCurrentStreakData;
  final UpdateCurrentStreakCount _updateCurrentStreakCount;
  final UpdateCurrentStreakId _updateCurrentStreakId;
  final UpdateCurrentStreakStatus _updateCurrentStreakStatus;
  final FetchCurrentStreakEntries _fetchCurrentStreakEntries;

  StreaksBloc({
    required AddStreakDayEntryToStreak addNewDayToStreak,
    required AddCurrentDayEntryToCurrentStreak
        addCurrentDayEntryToCurrentStreak,
    required MarkStreakDayEntryAsCompleted markStreakDayEntryAsCompleted,
    required IncrementCurrentStreakDayAlongWithAddingIfNotPresent
        incrementCurrentStreakDayAlongWithAddingIfNotPresent,
    required FetchCurrentDayStreakEntry fetchCurrentDayStreakEntry,
    required AddNewStreakForTheUser addNewStreakForTheUser,
    required FetchCurrentStreakSummary fetchCurrentStreakSummary,
    required FetchStreakDetails fetchStreakDetails,
    required FetchCompleteStreaksData fetchCompleteStreaksData,
    required FetchCurrentStreakDetails fetchCurrentStreakDetails,
    required UpdateCurrentStreakData updateCurrentStreakData,
    required UpdateCurrentStreakCount updateCurrentStreakCount,
    required UpdateCurrentStreakId updateCurrentStreakId,
    required UpdateCurrentStreakStatus updateCurrentStreakStatus,
    required FetchCurrentStreakEntries fetchCurrentStreakEntries,
  })  : _addNewStreakForTheUser = addNewStreakForTheUser,
        _addNewDayToStreak = addNewDayToStreak,
        _addCurrentDayEntryToCurrentStreak = addCurrentDayEntryToCurrentStreak,
        _fetchCurrentStreakSummary = fetchCurrentStreakSummary,
        _fetchStreakDetails = fetchStreakDetails,
        _markStreakDayEntryAsCompleted = markStreakDayEntryAsCompleted,
        _updateCurrentStreakData = updateCurrentStreakData,
        _updateCurrentStreakCount = updateCurrentStreakCount,
        _updateCurrentStreakId = updateCurrentStreakId,
        _updateCurrentStreakStatus = updateCurrentStreakStatus,
        _incrementCurrentStreakDayAlongWithAddingIfNotPresent =
            incrementCurrentStreakDayAlongWithAddingIfNotPresent,
        _fetchCurrentDayStreakEntry = fetchCurrentDayStreakEntry,
        _fetchCompleteStreaksData = fetchCompleteStreaksData,
        _fetchCurrentStreakDetails = fetchCurrentStreakDetails,
        _fetchCurrentStreakEntries = fetchCurrentStreakEntries,
        super(const StreaksStateInitial()) {
    on<AddCurrentDayEntryToCurrentStreakEvent>(
        _addCurrentDayEntryToCurrentStreakEventHandler);
    on<AddNewDayToStreakEvent>(_addNewDayToStreakEventHandler);
    on<LoadCurrentStreakSummaryEvent>(_fetchCurrentStreakSummaryEventHandler);
    on<FetchCurrentStreakDetailsEvent>(_fetchCurrentStreakDetailsEventHandler);
    on<IncrementCurrentDayStreakEntryEvent>(
        _incrementCurrentDayStreakEntryEventHandler);
    on<FetchStreakDetailsEvent>(_fetchStreakDetailsEventHandler);
    on<CreateNewStreakEvent>(_createNewEventStreakEventHandler);
    on<UpdateCurrentStreakIdEvent>(_updateCurrentStreakIdEventHandler);
    on<UpdateStreakStatusEvent>(_updateStreakStatusEventHandler);
    on<UpdateCurrentStreakStatusEvent>(_updateCurrentStreakStatusEventHandler);
    on<UpdateCurrentStreakCountEvent>(_updateCurrentStreakCountEventHandler);
    on<UpdateCurrentStreakDataEvent>(_updateCurrentStreakDataEventHandler);
    on<MarkStreakDayAsCompletedEvent>(_markStreakDayAsCompletedEventHandler);
    on<FetchCompleteStreakDataEvent>(_fetchCompleteStreaksDataEventHandler);
    on<FetchDaysFromCurrentStreakEvent>(
        _fetchDaysFromCurrentStreakEventHandler);
  }

  FutureOr<void> _fetchCurrentStreakSummaryEventHandler(
      LoadCurrentStreakSummaryEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _fetchCurrentStreakSummary();

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (currentStreak) =>
            emit(CurrentStreakSummaryState.fromCurrentStreak(currentStreak)));
  }

  FutureOr<void> _fetchStreakDetailsEventHandler(
      FetchStreakDetailsEvent event, Emitter<StreaksStateInitial> emit) async {
    final result = await _fetchStreakDetails(event.streakId);

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(StreaksDataState(streak: streak)));
  }

  FutureOr<void> _fetchCurrentStreakDetailsEventHandler(
      FetchCurrentStreakDetailsEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _fetchCurrentStreakDetails();

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(CurrentStreakDetailsState(currentStreak: streak)));
  }

  FutureOr<void> _incrementCurrentDayStreakEntryEventHandler(
      IncrementCurrentDayStreakEntryEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result =
        await _incrementCurrentStreakDayAlongWithAddingIfNotPresent();

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDayEntryUpdatedSuccessState()));
  }

  FutureOr<void> _createNewEventStreakEventHandler(
      CreateNewStreakEvent event, Emitter<StreaksStateInitial> emit) async {
    final result = await _addNewStreakForTheUser(event.type);

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(StreaksDataState(streak: streak)));
  }

  FutureOr<void> _addCurrentDayEntryToCurrentStreakEventHandler(
      AddCurrentDayEntryToCurrentStreakEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _addCurrentDayEntryToCurrentStreak();

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _addNewDayToStreakEventHandler(
      AddNewDayToStreakEvent event, Emitter<StreaksStateInitial> emit) async {
    final result = await _addNewDayToStreak(AddStreakDayEntryToStreakParams(
        streakId: event.streakId, streakDay: event.streakDay));
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _updateCurrentStreakIdEventHandler(
      UpdateCurrentStreakIdEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _updateCurrentStreakId(event.streakId);
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _updateStreakStatusEventHandler(
      UpdateStreakStatusEvent event, Emitter<StreaksStateInitial> emit) async {
    final result = await _updateCurrentStreakStatus(
        UpdateCurrentStreakStatusParams(status: event.status));
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _updateCurrentStreakStatusEventHandler(
      UpdateCurrentStreakStatusEvent event,
      Emitter<StreaksStateInitial> emit) async {
    //TODO- This should also update the status under the profile
    final result = await _updateCurrentStreakStatus(
        UpdateCurrentStreakStatusParams(status: event.status));
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _updateCurrentStreakCountEventHandler(
      UpdateCurrentStreakCountEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _updateCurrentStreakCount(
        UpdateCurrentStreakCounterParams(count: event.count));
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _updateCurrentStreakDataEventHandler(
      UpdateCurrentStreakDataEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _updateCurrentStreakData(UpdateCurrentStreakDataParams(
        id: event.streakId, count: event.streakCount, status: event.status));
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _markStreakDayAsCompletedEventHandler(
      MarkStreakDayAsCompletedEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _markStreakDayEntryAsCompleted(
        MarkStreakDayEntryAsCompletedParams(
            streakId: event.streakId, streakDay: event.streakDay));

    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streak) => emit(const StreakDetailsUpdateSuccessState()));
  }

  FutureOr<void> _fetchCompleteStreaksDataEventHandler(
      FetchCompleteStreakDataEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _fetchCompleteStreaksData();
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streaks) =>
            emit(CompleteStreakDataState(completeStreaksData: streaks)));
  }

  FutureOr<void> _fetchDaysFromCurrentStreakEventHandler(
      FetchDaysFromCurrentStreakEvent event,
      Emitter<StreaksStateInitial> emit) async {
    final result = await _fetchCurrentStreakEntries();
    result.fold(
        (failure) => emit(StreakErrorState(errorMessage: failure.errorMessage)),
        (streaks) => emit(CurrentStreakDataForCalendarState(
            currentStreakEntries: streaks.streakEntries)));
  }
}
