part of 'streaks_bloc.dart';

class StreaksStateInitial extends Equatable {
  const StreaksStateInitial();

  @override
  List<Object?> get props => [];
}

class StreaksDataState extends StreaksStateInitial {
  final Streak streak;

  const StreaksDataState({required this.streak}) : super();

  @override
  List<Object?> get props => [streak.id];

  @override
  String toString() {
    return 'StreaksDataState { streak: $streak }';
  }
}

class CurrentStreakSummaryState extends StreaksStateInitial {
  final String streakId;
  final StreakType type;
  final StreakStatus status;
  final int currentStreakLength;
  final int longestStreakLength;

  const CurrentStreakSummaryState({
    required this.streakId,
    required this.type,
    required this.status,
    required this.currentStreakLength,
    required this.longestStreakLength,
  });

  CurrentStreakSummaryState.fromCurrentStreak(CurrentStreak currentStreak)
      : streakId = currentStreak.currentStreakId,
        type = currentStreak.type,
        status = currentStreak.status,
        currentStreakLength = currentStreak.currentStreakLength,
        longestStreakLength = currentStreak.longestStreakLength;

  CurrentStreakSummaryState copyWith({
    String? streakId,
    StreakType? type,
    StreakStatus? status,
    int? currentStreakLength,
    int? longestStreakLength,
  }) {
    return CurrentStreakSummaryState(
        streakId: streakId ?? this.streakId,
        type: type ?? this.type,
        status: status ?? this.status,
        currentStreakLength: currentStreakLength ?? this.currentStreakLength,
        longestStreakLength: longestStreakLength ?? this.longestStreakLength);
  }

  @override
  List<Object?> get props =>
      [streakId, type, status, currentStreakLength, longestStreakLength];
}

class CurrentStreakDetailsState extends StreaksStateInitial {
  final CurrentStreak currentStreak;

  const CurrentStreakDetailsState({required this.currentStreak});

  @override
  List<Object?> get props => [currentStreak];
}

class StreakErrorState extends StreaksStateInitial {
  final String errorMessage;

  const StreakErrorState({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

class StreakDataLoadingState extends StreaksStateInitial {
  const StreakDataLoadingState();
}

class StreakDetailsUpdateSuccessState extends StreaksStateInitial {
  const StreakDetailsUpdateSuccessState();
}

class StreakDayEntryUpdatedSuccessState extends StreaksStateInitial {
  const StreakDayEntryUpdatedSuccessState();
}

class CompleteStreakDataState extends StreaksStateInitial {
  final CompleteStreaksData completeStreaksData;
  const CompleteStreakDataState({required this.completeStreaksData});
}

class CurrentStreakDataForCalendarState extends StreaksStateInitial {
  final List<StreakEntry> currentStreakEntries;

  const CurrentStreakDataForCalendarState({
    required this.currentStreakEntries,
  });

  @override
  List<Object?> get props => [
        currentStreakEntries,
      ];
}
