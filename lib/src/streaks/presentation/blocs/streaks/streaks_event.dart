part of 'streaks_bloc.dart';

abstract class StreaksEvent extends Equatable {
  const StreaksEvent();
  @override
  List<Object> get props => [];
}

class FetchStreakDetailsEvent extends StreaksEvent {
  final String streakId;
  const FetchStreakDetailsEvent({required this.streakId});
}

class LoadCurrentStreakSummaryEvent extends StreaksEvent {
  const LoadCurrentStreakSummaryEvent();
}

class FetchCurrentStreakDetailsEvent extends StreaksEvent {
  const FetchCurrentStreakDetailsEvent();
}

//pass Type as StreakType.notAvailable to create a new streak with default type
class CreateNewStreakEvent extends StreaksEvent {
  final StreakType type;
  const CreateNewStreakEvent(this.type);
}

class AddCurrentDayEntryToCurrentStreakEvent extends StreaksEvent {}

//This will add a new day entry to given streak
class AddNewDayToStreakEvent extends StreaksEvent {
  final String streakId;
  final String streakDay;
  const AddNewDayToStreakEvent(this.streakId, this.streakDay);
}

class FetchCurrentDayStreakEntryEvent extends StreaksEvent {}

class IncrementCurrentDayStreakEntryEvent extends StreaksEvent {}

class UpdateCurrentStreakIdEvent extends StreaksEvent {
  final String streakId;
  const UpdateCurrentStreakIdEvent({required this.streakId});
}

class UpdateStreakStatusEvent extends StreaksEvent {
  final String streakId;
  final StreakStatus status;
  const UpdateStreakStatusEvent({required this.streakId, required this.status});
}

class UpdateCurrentStreakStatusEvent extends StreaksEvent {
  final StreakStatus status;
  const UpdateCurrentStreakStatusEvent({required this.status});
}

class UpdateCurrentStreakCountEvent extends StreaksEvent {
  final int count;
  const UpdateCurrentStreakCountEvent({required this.count});
}

class UpdateCurrentStreakDataEvent extends StreaksEvent {
  final String streakId;
  final StreakStatus status;
  final int streakCount;
  const UpdateCurrentStreakDataEvent({
    required this.streakId,
    required this.status,
    required this.streakCount,
  });
}

class MarkStreakDayAsCompletedEvent extends StreaksEvent {
  final String streakId;
  final String streakDay;
  const MarkStreakDayAsCompletedEvent({
    required this.streakId,
    required this.streakDay,
  });
}

class FetchCompleteStreakDataEvent extends StreaksEvent {
  const FetchCompleteStreakDataEvent();
}

class FetchDaysFromCurrentStreakEvent extends StreaksEvent {
  const FetchDaysFromCurrentStreakEvent();
}
