import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:table_calendar/table_calendar.dart';

class StreakCalendarPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _StreakCalendarPageState();

  const StreakCalendarPage({super.key});

  factory StreakCalendarPage.routeBuilder(_, __) {
    return const StreakCalendarPage();
  }
}

class _StreakCalendarPageState extends State<StreakCalendarPage> {
  final CalendarFormat _calendarFormat = CalendarFormat.month;
  final DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  DateTime kToday = DateTime.now();
  DateTime kFirstDay = DateTime(
      DateTime.now().year, DateTime.now().month - 3, DateTime.now().day);
  DateTime kLastDay = DateTime(
      DateTime.now().year, DateTime.now().month + 3, DateTime.now().day + 2);
  DateTime kRangeStartDate = DateTime.now();
  DateTime kRangeEndDate = DateTime(
      DateTime.now().year, DateTime.now().month + 0, DateTime.now().day + 2);

  var sunday = {'day': 'S', 'date': '8', 'status': 'done'};
  var monday = {'day': 'M', 'date': '9', 'status': 'done'};
  var tuesday = {'day': 'T', 'date': '10', 'status': 'done'};
  var wednesday = {'day': 'W', 'date': '11', 'status': 'done'};
  var thursday = {'day': 'T', 'date': '12', 'status': 'notdone'};
  var friday = {'day': 'F', 'date': '13', 'status': 'notdone'};
  var saturday = {'day': 'S', 'date': '14', 'status': 'notdone'};

  List<Map<String, String>> currentWeakStreakList = [];

  @override
  void initState() {
    super.initState();

    currentWeakStreakList = [
      sunday,
      monday,
      tuesday,
      wednesday,
      thursday,
      friday,
      saturday
    ];
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Scaffold(
      /* appBar: AppBar(
        title: Text('Streak Calendar'),
      ),*/
      body: Center(
        child: SingleChildScrollView(
          //physics: AlwaysScrollableScrollPhysics,
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    height: 440 * fem,
                    decoration: const ShapeDecoration(
                      color: Color(0xFF50409A),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(24),
                          bottomRight: Radius.circular(24),
                        ),
                      ),
                    ),
                    child: Image.asset(
                      'assets/images/streaks/streak_1.png',
                      width: double.infinity,
                      height: 200 * fem,
                    ),
                  ),
                  Positioned(
                    top: 300 * fem,
                    child: Container(
                      padding: EdgeInsets.fromLTRB(
                          38 * fem, 38 * fem, 38 * fem, 38 * fem),
                      child: Row(
                        children: currentWeakStreakList.map(
                          (currentWeek) {
                            return Row(
                              children: [
                                Column(
                                  children: [
                                    Text(
                                      currentWeek['day']!,
                                      style: const TextStyle(
                                        color: Color(0xFFCBCBCB),
                                        fontSize: 14,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                        height: 0,
                                      ),
                                    ),
                                    SizedBox(height: 8 * fem),
                                    currentWeek['status'] == 'done'
                                        ? Container(
                                            width: 36,
                                            height: 48,
                                            decoration: ShapeDecoration(
                                              gradient: const LinearGradient(
                                                begin: Alignment(0.00, -1.00),
                                                end: Alignment(0, 1),
                                                colors: [
                                                  Color(0xFF4EE20B),
                                                  Color(0xFF236406)
                                                ],
                                              ),
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(5)),
                                            ),
                                            child: Image.asset(
                                                'assets/images/streaks/done_symbol.png'),
                                          )
                                        : Container(
                                            width: 36,
                                            height: 48,
                                            padding: EdgeInsets.fromLTRB(
                                                0 * fem,
                                                10 * fem,
                                                0 * fem,
                                                0 * fem),
                                            decoration: ShapeDecoration(
                                              color: const Color(0x19D9D9D9),
                                              shape: RoundedRectangleBorder(
                                                side: const BorderSide(
                                                    width: 1,
                                                    color: Color(0xFFA594F4)),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                            ),
                                            child: Text(
                                              currentWeek['date']!,
                                              textAlign: TextAlign.center,
                                              style: const TextStyle(
                                                color: Color(0xFFCBCBCB),
                                                fontSize: 16,
                                                fontFamily: 'Poppins',
                                                fontWeight: FontWeight.w400,
                                                height: 0,
                                              ),
                                            ),
                                          ),
                                  ],
                                ),
                                SizedBox(
                                  width: 8 * fem,
                                ),
                              ],
                            );
                          },
                        ).toList(),
                      ),
                    ),
                  ),
                ],
              ),
              TableCalendar(
                locale: 'en-us',
                headerStyle: const HeaderStyle(
                    formatButtonVisible: false, titleCentered: true),
                firstDay: kFirstDay,
                lastDay: kLastDay,
                rangeStartDay: kRangeStartDate,
                rangeEndDay: kRangeEndDate,
                focusedDay: _focusedDay,
                calendarStyle: const CalendarStyle(
                  rangeHighlightColor: kCalendarRangeHighlightedColor,
                  rangeStartDecoration: BoxDecoration(
                    color: kCalendarRangeHighlightedColor,
                    shape: BoxShape.circle,
                  ),
                  rangeEndDecoration: BoxDecoration(
                    color: kCalendarRangeHighlightedColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              SizedBox(
                height: 40 * fem,
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomActionButtonsWidget(
        fem: fem,
        primaryCallBack: () {
          print("call comes here in continue learning button");
          context.pop();
        },
        primaryText: 'Continue learning',
        secondaryCallBack: () {},
        secondaryText: '',
        isDualActions: false,
      ),
    );
  }
}
