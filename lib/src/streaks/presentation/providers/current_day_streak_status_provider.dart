import 'package:flutter/foundation.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/fetch_current_day_streak_entry.dart';

class CurrentDayStreakStatusProvider extends ChangeNotifier {
  StreakEntry? _currentDayStreakStatus;
  final FetchCurrentDayStreakEntry _fetchCurrentDayStreakEntry;

  CurrentDayStreakStatusProvider(
      {required FetchCurrentDayStreakEntry fetchCurrentDayStreakEntry})
      : _fetchCurrentDayStreakEntry = fetchCurrentDayStreakEntry;

  StreakEntry? get currentDayStreakStatus {
    if (_currentDayStreakStatus == null) {
      initializeCurrentDayStreakStatus();
    }
    return _currentDayStreakStatus;
  }

  void incrementCurrentDayStreakStatus() async {
    //if (_currentDayStreakStatus == null) {
    //await initializeCurrentDayStreakStatus();
    //}

    if (_currentDayStreakStatus != null) {
      int currentCount = _currentDayStreakStatus!.completedCount == -1
          ? 0
          : _currentDayStreakStatus!.completedCount;
      _currentDayStreakStatus = _currentDayStreakStatus!.copyWith(
        completedCount: currentCount + 1,
      );
    } else {
      await initializeCurrentDayStreakStatus();
    }
    notifyListeners();
  }

  Future<void> initializeCurrentDayStreakStatus() async {
    if (_currentDayStreakStatus == null) {
      final result = await _fetchCurrentDayStreakEntry();

      result.fold(
        (failure) {
          debugPrint(failure.toString());
        },
        (streakEntry) {
          _currentDayStreakStatus = streakEntry;
          notifyListeners();
        },
      );
    }
  }
}
