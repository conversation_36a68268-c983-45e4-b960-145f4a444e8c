import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchCurrentStreakSummary
    extends FutureUsecaseWithoutParams<CurrentStreak> {
  final StreakDetailsRepo _streakDetailsRepo;

  FetchCurrentStreakSummary({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;

  //This will not contain individual streak days
  @override
  ResultFuture<CurrentStreak> call() =>
      _streakDetailsRepo.getCurrentStreakInfo();
}
