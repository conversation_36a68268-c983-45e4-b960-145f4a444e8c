import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchCompleteStreaksData
    extends FutureUsecaseWithoutParams<CompleteStreaksData> {
  final StreakDetailsRepo _streakDetailsRepo;

  FetchCompleteStreaksData({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;

  @override
  ResultFuture<CompleteStreaksData> call() =>
      _streakDetailsRepo.fetchCompleteStreaksData();
}
