// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class UpdateCurrentStreakId extends FutureUsecaseWithParams<void, String> {
  final StreakDetailsRepo _streakDetailsRepo;

  UpdateCurrentStreakId({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;
  @override
  ResultFuture<void> call(String params) =>
      _streakDetailsRepo.updateCurrentStreakId(params);
}

class UpdateCurrentStreakStatus
    extends FutureUsecaseWithParams<void, UpdateCurrentStreakStatusParams> {
  final StreakDetailsRepo _streakDetailsRepo;

  UpdateCurrentStreakStatus({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;
  @override
  ResultFuture<void> call(UpdateCurrentStreakStatusParams params) =>
      _streakDetailsRepo.updateCurrentStreakStatus(params.status);
}

class UpdateCurrentStreakStatusParams extends Equatable {
  final StreakStatus status;
  const UpdateCurrentStreakStatusParams({
    required this.status,
  });

  @override
  List<Object?> get props => [status];
}

class UpdateCurrentStreakCount
    extends FutureUsecaseWithParams<void, UpdateCurrentStreakCounterParams> {
  final StreakDetailsRepo _streakDetailsRepo;

  UpdateCurrentStreakCount({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;
  @override
  ResultFuture<void> call(UpdateCurrentStreakCounterParams params) =>
      _streakDetailsRepo.updateCurrentStreakCount(params.count);
}

class UpdateCurrentStreakCounterParams extends Equatable {
  final int count;
  const UpdateCurrentStreakCounterParams({
    required this.count,
  });

  @override
  List<Object?> get props => [count];
}

class UpdateCurrentStreakData
    extends FutureUsecaseWithParams<void, UpdateCurrentStreakDataParams> {
  final StreakDetailsRepo _streakDetailsRepo;

  UpdateCurrentStreakData({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;
  @override
  ResultFuture<void> call(UpdateCurrentStreakDataParams params) =>
      _streakDetailsRepo.updateCurrentStreakData(
          params.id, params.count, params.status);
}

class UpdateCurrentStreakDataParams extends Equatable {
  final String id;
  final int count;
  final StreakStatus status;
  const UpdateCurrentStreakDataParams({
    required this.id,
    required this.count,
    required this.status,
  });

  @override
  List<Object?> get props => [id, count, status];
}
