import 'package:dartz/dartz.dart';
import 'package:flutter/widgets.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_details.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_summary.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_streak_status.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/mark_streak_as_inactive.dart';

class FetchCurrentStreakDayCount extends FutureUsecaseWithoutParams<int> {
  final StreakDetailsRepo _streakDetailsRepo;
  final FetchCurrentStreakSummary _fetchCurrentStreakSummary;
  final FetchStreakStatus _fetchStreakStatus;
  final MarkStreakAsInactive _markStreakAsInactive;
  final maxSkipAllowed = 2;

  FetchCurrentStreakDayCount(
      {required StreakDetailsRepo streakDetailsRepo,
      required FetchCurrentStreakSummary fetchCurrentStreakSummary,
      required FetchStreakStatus fetchStreakStatus,
      required MarkStreakAsInactive markStreakAsInactive,
      required FetchCurrentStreakDetails fetchCurrentStreakDetails})
      : _streakDetailsRepo = streakDetailsRepo,
        _fetchCurrentStreakSummary = fetchCurrentStreakSummary,
        _fetchStreakStatus = fetchStreakStatus,
        _markStreakAsInactive = markStreakAsInactive;

  @override
  ResultFuture<int> call() async {
    try {
      final streakResult = await _fetchCurrentStreakSummary();

      CurrentStreak streakSummary = const CurrentStreak.empty();
      if (streakResult is Right) {
        streakSummary =
            streakResult.getOrElse(() => const CurrentStreak.empty());
        //fetch current streak status and validate if activve
        String streakId = streakSummary.currentStreakId;

        if (streakId == '') {
          return const Left(ServerFailure(
              message: "Unable to fetch current streak day count",
              statusCode: 500));
        }

        final streakStatusResult = await _fetchStreakStatus(streakId);

        if (streakStatusResult is Right) {
          StreakStatus streakStatus =
              streakStatusResult.getOrElse(() => StreakStatus.notAvailable);
          if (streakStatus == StreakStatus.active) {
            //bool isInactive = await _verifyIfStreakIsInactive(streakId);
            bool isInactive = await isStreakInactive(streakId);
            //Validate if streak is actually active and update accordingly
            if (isInactive) {
              return const Right(0);
            }
          } else if (streakStatus == StreakStatus.inactive) {
            return const Right(0);
          }
        } else {
          return const Left(ServerFailure(
              message: "Unable to fetch current streak day count",
              statusCode: 500));
        }
      }

      return Right(streakSummary.currentStreakLength);
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      return const Left(ServerFailure(
          message: "Unable to fetch current streak day count",
          statusCode: 500));
    }
  }

  /*
  Future<bool> _verifyIfStreakIsInactive(String streakId) async {
    //iterate over streakEntries and add the entries to the map where the entry.streakDay is in the current week

    final streakEither = await _streakDetailsRepo.fetchStreakFromId(streakId);
    if (streakEither is Left) {
      throw const ServerException(
          message: "Unable to fetch current streak day count",
          statusCode: '500');
    }

    Streak streak = streakEither.getOrElse(() => const Streak.empty());
    List<StreakEntry> streakEntries = streak.streakEntries;
    DateTime lastCompletedStreakDay = getLowestDateTime();
    DateTime firstStreakDay = getHighestDateTime();
    bool atleastOneDayCompleted = false;

    for (StreakEntry entry in streakEntries) {
      DateTime entryDate = convertYYYYMMDDToDate(entry.streakDay);
      if (!atleastOneDayCompleted &&
          entry.status == StreakEntryStatus.completed) {
        atleastOneDayCompleted = true;
      }

      if (entry.status == StreakEntryStatus.completed &&
          entryDate.isAfter(lastCompletedStreakDay)) {
        lastCompletedStreakDay = entryDate;
      }

      if (firstStreakDay.isAfter(entryDate)) {
        firstStreakDay = entryDate;
      }
    }

    DateTime now = DateTime.now();
    DateTime startDayOfWeek =
        DateTime(now.year, now.month, now.day - now.weekday + 1);
    bool markAsInactive = false;

    if (firstStreakDay.isAtSameMomentAs(getHighestDateTime())) {
      //No streak days present in the streak. Continue using the same streak.
      return false;
    }

    if (!firstStreakDay.isBefore(startDayOfWeek)) {
      //Current streak is started this week. Check if user has skipped more than allowed days after the start of the streak.
      DateTime now = DateTime.now();

      print(now.difference(startDayOfWeek).inDays);
      final temp = now.difference(startDayOfWeek).inDays;
      if (now.difference(startDayOfWeek).inDays >= maxSkipAllowed) {
        return true;
      } else {
        return false;
      }
    }
    //Add else if for lastCompletedStreakDay is after or equals to start
    else if (lastCompletedStreakDay.isBefore(startDayOfWeek)) {
      //Streak was started last week. Check if user has skipped more than allowed days in the current week.
      if (_currentWeekHasMoreThanAllowedDays()) {
        markAsInactive = true;
      } else if (_previousWeekHasMoreThanAllowedSkippedDays(streakEntries)) {
        markAsInactive = true;
      }
    } else if (_currentWeekHasMoreThanAllowedSkippedDays(streakEntries)) {
      //Streak
      markAsInactive = true;
    }

    if (markAsInactive) {
      //Add code here to mark streak as inactive
      await _markStreakAsInactive(streakId);
      return true;
    } else {
      return false;
    }
  }*/

  Future<bool> isStreakInactive(String streakId) async {
    final streakEither = await _streakDetailsRepo.fetchStreakFromId(streakId);
    if (streakEither is Left) {
      throw const ServerException(
          message: "Unable to fetch current streak day count",
          statusCode: '500');
    }

    Streak streak = streakEither.getOrElse(() => const Streak.empty());
    List<StreakEntry> streakEntries = streak.streakEntries;
    DateTime lastCompletedStreakDay = getLowestDateTime();

    Map<int, StreakEntry> streakEntriesMap = {};

    for (StreakEntry entry in streakEntries) {
      DateTime entryDate = convertYYYYMMDDToDate(entry.streakDay);
      streakEntriesMap[entry.streakDay] = entry;
      if (entry.status == StreakEntryStatus.completed &&
          entryDate.isAfter(lastCompletedStreakDay)) {
        lastCompletedStreakDay = entryDate;
      }
    }
    DateTime now = DateTime.now();

    //Set current date as the end day
    DateTime currentDay = DateTime(now.year, now.month, now.day);
    print("Today is $currentDay");

    if (lastCompletedStreakDay.isAtSameMomentAs(getLowestDateTime()) ||
        lastCompletedStreakDay.isAtSameMomentAs(currentDay)) {
      //No streak days present in the streak or last completed day is today. Continue using the same streak.
      return false;
    }
    DateTime endDay = currentDay.subtract(const Duration(days: 1));
    print("Yesterday is $endDay");
    //Set start day of the week as the start date
    DateTime startDay =
        DateTime(now.year, now.month, now.day - now.weekday + 1);
    print("Start day of current week is $startDay");
    // lastCompletedStreakDay is before startDayOfWeek
    if (lastCompletedStreakDay.isBefore(startDay)) {
      //Change start day to start of previous week
      startDay = DateTime(now.year, now.month, now.day - now.weekday - 6);
      print("Start day of previous week is $startDay");
      //Change end day to end of previous week
      endDay = DateTime(now.year, now.month, now.day - now.weekday);
      print("End day of previous week is $endDay");
    }
    // Check if more than allowed days were skipped previous week
    int daysSkipped =
        _getNumberOfDaysSkipped(streakEntriesMap, startDay, endDay);
    if (daysSkipped > maxSkipAllowed) {
      // Mark as inactive by returning true
      await _markStreakAsInactive(streakId);
      return true;
    } else {
      // Mark as active by returning false
      return false;
    }
  }

  int _getNumberOfDaysSkipped(Map<int, StreakEntry> streakEntriesMap,
      DateTime startDay, DateTime endDay) {
    int skippedDays = 0;
    for (DateTime date = startDay;
        date.isBefore(endDay) || date.isAtSameMomentAs(endDay);
        date = date.add(const Duration(days: 1))) {
      int intDate = convertDateTimeToYYYYMMDD(date);
      if (!streakEntriesMap.containsKey(intDate) ||
          streakEntriesMap[intDate]!.status != StreakEntryStatus.completed) {
        skippedDays++;
      }
    }
    print(
        "Number of days skipped for the give criteria $startDay and $endDay is $skippedDays");
    return skippedDays;
  }

  /*
  bool _currentWeekHasMoreThanAllowedDays() {
    DateTime now = DateTime.now();
    DateTime startDayOfWeek =
        DateTime(now.year, now.month, now.day - now.weekday + 1);
    print(now.difference(startDayOfWeek).inDays);
    final temp = now.difference(startDayOfWeek).inDays;
    if (now.difference(startDayOfWeek).inDays >= maxSkipAllowed) {
      return true;
    } else {
      return false;
    }
  }

  bool _previousWeekHasMoreThanAllowedSkippedDays(
      List<StreakEntry> streakEntries) {
    DateTime now = DateTime.now();
    DateTime startOfPreviousWeekCheck =
        DateTime(now.year, now.month, now.day - now.weekday - 7);
    DateTime endOfPreviousWeekCheck =
        DateTime(now.year, now.month, now.day - now.weekday + 1);
    int completedCount = 0;
    for (StreakEntry entry in streakEntries) {
      DateTime entryDate = convertYYYYMMDDToDate(entry.streakDay);

      if (entryDate.isAfter(startOfPreviousWeekCheck) &&
          entryDate.isBefore(endOfPreviousWeekCheck) &&
          entry.status == StreakEntryStatus.completed) {
        completedCount++;
      }
    }

    if (completedCount < 5) {
      return true;
    } else {
      return false;
    }
  }

  bool _currentWeekHasMoreThanAllowedSkippedDays(
      List<StreakEntry> streakEntries) {
    DateTime now = DateTime.now();
    DateTime startDayOfWeekCheck =
        DateTime(now.year, now.month, now.day - now.weekday);
    int completedCount = 0;
    for (StreakEntry entry in streakEntries) {
      DateTime entryDate = convertYYYYMMDDToDate(entry.streakDay);

      if (entryDate.isAfter(startDayOfWeekCheck) &&
          entry.status == StreakEntryStatus.completed) {
        completedCount++;
      }
    }
    int daysPassedInCurrentWeek =
        now.difference(startDayOfWeekCheck).inDays - 1;
    if (daysPassedInCurrentWeek - completedCount > maxSkipAllowed) {
      return true;
    } else {
      return false;
    }
  }*/

  int convertDateTimeToYYYYMMDD(DateTime dateTime) {
    int year = dateTime.year;
    int month = dateTime.month;
    int day = dateTime.day;

    // Format the date components into YYYYMMDD format
    return year * 10000 + month * 100 + day;
  }

  DateTime convertYYYYMMDDToDate(int yyyyddmmDateIntValue) {
    int year = yyyyddmmDateIntValue ~/ 10000;
    int month = (yyyyddmmDateIntValue % 10000) ~/ 100;
    int day = yyyyddmmDateIntValue % 100;
    DateTime entryDate = DateTime(year, month, day);
    return entryDate;
  }

  DateTime getLowestDateTime() {
    return DateTime(2000, 12, 31, 23, 59, 59, 999, 999);
  }

  DateTime getHighestDateTime() {
    return DateTime(2099, 12, 31, 23, 59, 59, 999, 999);
  }
}
