import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchStreakDetails extends FutureUsecaseWithParams<Streak, String> {
  final StreakDetailsRepo _streakDetailsRepo;

  FetchStreakDetails({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;

  @override
  ResultFuture<Streak> call(String params) =>
      _streakDetailsRepo.fetchStreakFromId(params);
}
