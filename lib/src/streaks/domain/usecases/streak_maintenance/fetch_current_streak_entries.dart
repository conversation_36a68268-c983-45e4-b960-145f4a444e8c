import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchCurrentStreakEntries extends FutureUsecaseWithoutParams<Streak> {
  final StreakDetailsRepo _streakDetailsRepo;
  final StreaksCache _streaksCache;

  FetchCurrentStreakEntries({
    required StreakDetailsRepo streakDetailsRepo,
    required StreaksCache streaksCache,
  })  : _streakDetailsRepo = streakDetailsRepo,
        _streaksCache = streaksCache;

  @override
  ResultFuture<Streak> call() async {
    try {
      String currentStreakId = _streaksCache.getCurrentStreakId();
      if (currentStreakId == '') {
        final currentStreakEither =
            await _streakDetailsRepo.getCurrentStreakInfo();
        CurrentStreak currentStreak = currentStreakEither.getOrElse(() =>
            throw const ServerException(
                message: "Something went wrong", statusCode: '500'));
        currentStreakId = currentStreak.currentStreakId;
      }

      final streakEither =
          await _streakDetailsRepo.fetchStreakFromId(currentStreakId);

      if (streakEither.isRight()) {
        Streak streak = streakEither.getOrElse(() =>
            throw const ServerException(
                message: "Unable to fetch current streak details",
                statusCode: '500'));
        return Right(streak);
      } else {
        throw streakEither.getOrElse(() => throw const ServerException(
            message: "Unable to fetch current streak entries",
            statusCode: '500'));
      }
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      return const Left(ServerFailure(
          message: "Unable to fetch current streak entries", statusCode: 500));
    }
  }
}
