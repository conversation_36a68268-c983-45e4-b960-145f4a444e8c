import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class AddNewStreakForTheUser
    extends FutureUsecaseWithParams<Streak, StreakType> {
  final StreakDetailsRepo _streakRepository;

  AddNewStreakForTheUser({required StreakDetailsRepo streakDetailsRepo})
      : _streakRepository = streakDetailsRepo;

  @override
  ResultFuture<Streak> call(StreakType params) =>
      _streakRepository.addNewStreak(params);
}
