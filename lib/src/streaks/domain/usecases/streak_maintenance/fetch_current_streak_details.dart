import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchCurrentStreakDetails
    extends FutureUsecaseWithoutParams<CurrentStreak> {
  final StreakDetailsRepo _streakDetailsRepo;

  FetchCurrentStreakDetails({
    required StreakDetailsRepo streakDetailsRepo,
  }) : _streakDetailsRepo = streakDetailsRepo;

  @override
  ResultFuture<CurrentStreak> call() async {
    CurrentStreak currentStreak;

    try {
      final currentStreakEither =
          await _streakDetailsRepo.getCurrentStreakInfo();

      if (currentStreakEither.isRight()) {
        currentStreak = currentStreakEither.getOrElse(() =>
            throw const ServerException(
                message: "Something went wrong", statusCode: '500'));

        final streakEither = await _streakDetailsRepo
            .fetchStreakFromId(currentStreak.currentStreakId);

        if (streakEither.isRight()) {
          Streak streak = streakEither.getOrElse(() =>
              throw const ServerException(
                  message: "Unable to fetch current streak details",
                  statusCode: '500'));

          Map<int, StreakEntry> currentWeekStreakEntries =
              _fetchCurrentWeekStreakEntries(streak.streakEntries);

          currentStreak = currentStreak.copyWith(
              streak: streak,
              currentWeekStreakEntries: currentWeekStreakEntries);
          return Right(currentStreak);
        } else {
          throw streakEither.getOrElse(() => throw const ServerException(
              message: "Unable to fetch current streak details",
              statusCode: '500'));
        }
      } else {
        throw currentStreakEither.getOrElse(() => throw const ServerException(
            message: "Unable to fetch current streak details",
            statusCode: '500'));
      }
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      return const Left(ServerFailure(
          message: "Unable to fetch current streak details", statusCode: 500));
    }

    //await _streakDetailsRepo.fetchStreakFromId(r.currentStreakId)
  }

  Map<int, StreakEntry> _fetchCurrentWeekStreakEntries(
      List<StreakEntry> streakEntries) {
    //iterate over streakEntries and add the entries to the map where the entry.streakDay is in the current week

    Map<int, StreakEntry> map = {};
    DateTime now = DateTime.now();
    DateTime startOfWeek =
        DateTime(now.year, now.month, now.day - now.weekday + 1);
    DateTime endOfWeek =
        DateTime(now.year, now.month, now.day - now.weekday + 7);
    //Add all the days of current week as key to the map with value as null
    for (int i = 0; i < 7; i++) {
      DateTime date = startOfWeek.add(Duration(days: i));
      int dateInt = date.year * 10000 + date.month * 100 + date.day;
      map[dateInt] = const StreakEntry.empty();
    }
    for (StreakEntry entry in streakEntries) {
      DateTime entryDate = convertYYYYMMDDToDate(entry.streakDay);

      if (entryDate.isAfter(startOfWeek) && entryDate.isBefore(endOfWeek)) {
        map[entry.streakDay] = entry;
      }
    }
    return map;
  }

  DateTime convertYYYYMMDDToDate(int yyyyddmmDateIntValue) {
    int year = yyyyddmmDateIntValue ~/ 10000;
    int month = (yyyyddmmDateIntValue % 10000) ~/ 100;
    int day = yyyyddmmDateIntValue % 100;
    DateTime entryDate = DateTime(year, month, day);
    return entryDate;
  }
}
