import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class MarkStreakAsInactive extends FutureUsecaseWithParams<void, String> {
  final StreakDetailsRepo _streakDetailsRepo;

  MarkStreakAsInactive({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;

  //This will not contain individual streak days
  @override
  ResultFuture<void> call(String params) =>
      _streakDetailsRepo.markStreakAsInactive(params);
}
