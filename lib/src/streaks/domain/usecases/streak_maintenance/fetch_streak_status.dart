import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchStreakStatus extends FutureUsecaseWithParams<StreakStatus, String> {
  final StreakDetailsRepo _streakDetailsRepo;

  FetchStreakStatus({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;

  //This will not contain individual streak days
  @override
  ResultFuture<StreakStatus> call(String params) =>
      _streakDetailsRepo.getCurrentStreakStatus(params);
}
