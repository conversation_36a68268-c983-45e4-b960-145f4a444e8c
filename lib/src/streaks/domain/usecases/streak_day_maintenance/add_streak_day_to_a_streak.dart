import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class AddStreakDayEntryToStreak
    extends FutureUsecaseWithParams<void, AddStreakDayEntryToStreakParams> {
  final StreakDetailsRepo _streakDetailsRepo;

  AddStreakDayEntryToStreak({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;
  @override
  ResultFuture<void> call(AddStreakDayEntryToStreakParams params) =>
      _streakDetailsRepo.addStreakDayEntryToStreak(
          params.streakId, params.streakDay);
}

class AddStreakDayEntryToStreakParams extends Equatable {
  final String streakId;
  final String streakDay;

  const AddStreakDayEntryToStreakParams(
      {required this.streakId, required this.streakDay});

  @override
  List<Object?> get props => [streakId, streakDay];
}
