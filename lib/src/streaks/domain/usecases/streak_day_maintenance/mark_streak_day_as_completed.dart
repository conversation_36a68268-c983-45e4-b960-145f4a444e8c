import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class MarkStreakDayEntryAsCompleted
    extends FutureUsecaseWithParams<void, MarkStreakDayEntryAsCompletedParams> {
  final StreakDetailsRepo _streakDetailsRepo;

  MarkStreakDayEntryAsCompleted({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;
  @override
  ResultFuture<void> call(MarkStreakDayEntryAsCompletedParams params) =>
      _streakDetailsRepo.markStreakDayEntryAsCompleted(
          params.streakId, params.streakDay, StreakEntryStatus.completed);
}

class MarkStreakDayEntryAsCompletedParams extends Equatable {
  final String streakDay;
  final String streakId;

  const MarkStreakDayEntryAsCompletedParams(
      {required this.streakDay, required this.streakId});

  @override
  List<Object?> get props => [streakDay, streakId];
}
