import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class AddCurrentDayEntryToCurrentStreak
    extends FutureUsecaseWithoutParams<void> {
  final StreakDetailsRepo _streakDetailsRepo;
  final StreaksCache _streaksCache;

  AddCurrentDayEntryToCurrentStreak(
      {required StreakDetailsRepo streakDetailsRepo,
      required StreaksCache streaksCache})
      : _streakDetailsRepo = streakDetailsRepo,
        _streaksCache = streaksCache;

  @override
  ResultFuture<void> call() => _streakDetailsRepo.addStreakDayEntryToStreak(
      _streaksCache.getCurrentStreakId(), getTodayAsString());

  String getTodayAsString() {
    return DateTime.now().toString().substring(0, 10).replaceAll('-', '');
  }
}
