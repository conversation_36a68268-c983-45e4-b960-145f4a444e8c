import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/add_streak_day_to_a_streak.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/fetch_current_day_streak_entry.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/add_new_streak_for_the_user.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_summary.dart';

class IncrementCurrentStreakDayAlongWithAddingIfNotPresent
    extends FutureUsecaseWithoutParams<void> {
  final StreaksCache _streaksCache;
  final StreakDetailsRepo _streakDetailsRepo;
  final FetchCurrentStreakSummary _fetchCurrentStreakSummary;
  final FetchCurrentDayStreakEntry _fetchCurrentDayStreakStatus;
  final AddNewStreakForTheUser _addNewStreakForTheUser;
  final AddStreakDayEntryToStreak _addStreakDayEntryToStreak;

  IncrementCurrentStreakDayAlongWithAddingIfNotPresent(
      {required StreaksCache streaksCache,
      required StreakDetailsRepo streakDetailsRepo,
      required FetchCurrentStreakSummary fetchCurrentStreakSummary,
      required FetchCurrentDayStreakEntry fetchCurrentDayStreakStatus,
      required AddNewStreakForTheUser addNewStreakForTheUser,
      required AddStreakDayEntryToStreak addStreakDayEntryToStreak})
      : _streaksCache = streaksCache,
        _streakDetailsRepo = streakDetailsRepo,
        _fetchCurrentStreakSummary = fetchCurrentStreakSummary,
        _fetchCurrentDayStreakStatus = fetchCurrentDayStreakStatus,
        _addNewStreakForTheUser = addNewStreakForTheUser,
        _addStreakDayEntryToStreak = addStreakDayEntryToStreak;

  @override
  ResultFuture<void> call() async {
    if (_streaksCache.isCurrentDayStreakCompleted()) {
      //No action needed
      return const Right(null);
    }
    //String currentStreakId = '';
    //String currentStreakDayId = '';
    if (!_streaksCache.isCurrentStreakDetailsAvailable()) {
      //Fetches and populate current streak data to cache
      await _fetchCurrentStreakSummary();
    }
    if (!_streaksCache.isCurrentStreakActive()) {
      final newStreakResult =
          await _addNewStreakForTheUser(StreakType.infinity);

      //Add new streak to cache

      if (newStreakResult.isRight()) {
        String currentStreakId = newStreakResult
            .getOrElse(() => throw const ServerException(
                message: "Something went wrong", statusCode: '500'))
            .id;
        _streaksCache.clearCurrentDayStreak();
        _streaksCache.setCurrentStreak(_streaksCache
            .getCurrentStreak()
            .copyWith(
                currentStreakId: currentStreakId, status: StreakStatus.active));
      }
    }

    if (!_streaksCache.isCurrentDayStreakDetailsAvailable()) {
      final resultEither = await _fetchCurrentDayStreakStatus();

      if (resultEither.isRight()) {
        resultEither
            .getOrElse(() => throw const ServerException(
                message: "Something went wrong", statusCode: '500'))
            .streakDay
            .toString();
      } else {
        //get today's date in YYYYMMDD format
        String today = getTodayAsString();
        await _addStreakDayEntryToStreak(AddStreakDayEntryToStreakParams(
            streakId: _streaksCache.getCurrentStreakId(), streakDay: today));
        //TODO-Populate currentstreakday in cache here.
        //refactor this code
        await _fetchCurrentDayStreakStatus();
      }
    }

    return await _streakDetailsRepo
        .updateStreakDayDetailsAndIncrementCacheCount(
            _streaksCache.getCurrentStreakId(),
            _streaksCache.getCurrentDayStreak().streakDay.toString(),
            _streaksCache.isCurrentDayStreakCompletesInNextIncrement()
                ? StreakEntryStatus.completed
                : StreakEntryStatus.inProgress,
            _streaksCache.getCurrentDayStreak().completedCount + 1);
  }
}

String getTodayAsString() {
  return DateTime.now().toString().substring(0, 10).replaceAll('-', '');
}
