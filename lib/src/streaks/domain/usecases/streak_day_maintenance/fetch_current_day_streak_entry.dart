import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_details.dart';

class FetchCurrentDayStreakEntry
    extends FutureUsecaseWithoutParams<StreakEntry> {
  final StreakDetailsRepo _streakDetailsRepo;
  final StreaksCache _streaksCache;
  final FetchCurrentStreakDetails _fetchCurrentStreakDetails;

  FetchCurrentDayStreakEntry(
      {required StreakDetailsRepo streakDetailsRepo,
      required StreaksCache streaksCache,
      required FetchCurrentStreakDetails fetchCurrentStreakDetails})
      : _streakDetailsRepo = streakDetailsRepo,
        _streaksCache = streaksCache,
        _fetchCurrentStreakDetails = fetchCurrentStreakDetails;

  @override
  ResultFuture<StreakEntry> call() async {
    try {
      if (!_streaksCache.isCurrentStreakDetailsAvailable()) {
        await _fetchCurrentStreakDetails();
      }
      return await _streakDetailsRepo
          .getCurrentDayStreakStatus(_streaksCache.getCurrentStreakId());
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      return const Left(ServerFailure(
          message: "Unable to fetch current streak details", statusCode: 500));
    }
  }
}
