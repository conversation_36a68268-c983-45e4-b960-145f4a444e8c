import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchStreakConfigsData
    extends FutureUsecaseWithoutParams<List<StreakConfig>> {
  final StreakDetailsRepo _streakDetailsRepo;

  FetchStreakConfigsData({required StreakDetailsRepo streakDetailsRepo})
      : _streakDetailsRepo = streakDetailsRepo;

  @override
  ResultFuture<List<StreakConfig>> call() =>
      _streakDetailsRepo.fetchStreakConfigs();
}
