import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';

class FetchStreakConfigById
    extends FutureUsecaseWithParams<StreakConfig, String> {
  final StreakDetailsRepo _streakDetailsRepo;
  final StreaksCache _streaksCache;
  FetchStreakConfigById(
      {required StreakDetailsRepo streakDetailsRepo,
      required StreaksCache streaksCache})
      : _streakDetailsRepo = streakDetailsRepo,
        _streaksCache = streaksCache;

  @override
  ResultFuture<StreakConfig> call(String params) async {
    StreakConfig config = _streaksCache.getStreakConfigById(params);
    if (config.isEmpty()) {
      return _streakDetailsRepo.fetchStreakConfigById(params);
    } else {
      return Right(config);
    }
  }
}
