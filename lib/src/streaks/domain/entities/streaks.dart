import 'package:equatable/equatable.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';

class StreakEntry extends Equatable implements Comparable<StreakEntry> {
  final int streakDay;
  final int completedCount;
  final int targetCount;
  final StreakEntryStatus status;

  const StreakEntry({
    required this.streakDay,
    required this.completedCount,
    required this.targetCount,
    required this.status,
  });

  @override
  List<Object?> get props => [streakDay, completedCount, targetCount, status];

  //tostring
  @override
  String toString() {
    return 'StreakEntry { streakDay: $streakDay, completedCount: $completedCount, targetCount: $targetCount, status: $status}';
  }

  const StreakEntry.empty()
      : streakDay = -1,
        completedCount = -1,
        targetCount = -1,
        status = StreakEntryStatus.notAvailable;

  bool isEmpty() => this == const StreakEntry.empty();

  @override
  int compareTo(StreakEntry other) {
    return streakDay.compareTo(other.streakDay);
  }

  StreakEntry copyWith({
    int? streakDay,
    int? completedCount,
    int? targetCount,
    StreakEntryStatus? status,
  }) {
    return StreakEntry(
      streakDay: streakDay ?? this.streakDay,
      completedCount: completedCount ?? this.completedCount,
      targetCount: targetCount ?? this.targetCount,
      status: status ?? this.status,
    );
  }
}

class Streak extends Equatable {
  final List<StreakEntry> streakEntries;
  final String id;
  final StreakType type;
  final StreakStatus status;

  const Streak({
    required this.id,
    required this.streakEntries,
    required this.type,
    required this.status,
  });

  const Streak.empty()
      : streakEntries = const [],
        id = '',
        type = StreakType.notAvailable,
        status = StreakStatus.notAvailable;

  bool isEmpty() => this == const Streak.empty();

  @override
  List<Object?> get props => [id];

  @override
  String toString() {
    return 'Streak { id: $id, type: $type, status: $status, entries: $streakEntries';
  }
}

class CompleteStreaksData extends Equatable {
  final List<Streak> streaks;
  final Set<StreakEntry>
      combinedStreakEntries; //change type to a sorted collection

  const CompleteStreaksData(
      {required this.streaks, required this.combinedStreakEntries});

  @override
  List<Object?> get props => [streaks];
}

class CurrentStreak extends Equatable {
  final int currentStreakLength;
  final int longestStreakLength;
  final String currentStreakId;
  final StreakType type;
  final StreakStatus status;
  final Streak streak;
  final Map<int, StreakEntry> currentWeekStreakEntries;

  const CurrentStreak({
    required this.currentStreakLength,
    required this.longestStreakLength,
    required this.currentStreakId,
    required this.streak,
    required this.type,
    required this.status,
    required this.currentWeekStreakEntries,
  });

  @override
  List<Object?> get props =>
      [currentStreakLength, longestStreakLength, currentStreakId];

  CurrentStreak copyWith({
    int? currentStreakLength,
    int? longestStreakLength,
    String? currentStreakId,
    Streak? streak,
    StreakType? type,
    StreakStatus? status,
    Map<int, StreakEntry>? currentWeekStreakEntries,
  }) {
    return CurrentStreak(
      currentStreakLength: currentStreakLength ?? this.currentStreakLength,
      longestStreakLength: longestStreakLength ?? this.longestStreakLength,
      currentStreakId: currentStreakId ?? this.currentStreakId,
      streak: streak ?? this.streak,
      type: type ?? this.type,
      status: status ?? this.status,
      currentWeekStreakEntries:
          currentWeekStreakEntries ?? this.currentWeekStreakEntries,
    );
  }

  const CurrentStreak.empty()
      : currentStreakLength = -1,
        longestStreakLength = -1,
        currentStreakId = '',
        streak = const Streak.empty(),
        type = StreakType.notAvailable,
        status = StreakStatus.notAvailable,
        currentWeekStreakEntries = const {};

  bool isEmpty() => this == const CurrentStreak.empty();
}

class StreakConfig extends Equatable {
  final String id;
  final String name;
  final Map<StreakConfigTypes, dynamic> config;

  const StreakConfig(
      {required this.id, required this.name, required this.config});

  @override
  List<Object?> get props => [id];

  const StreakConfig.empty()
      : id = '',
        name = '',
        config = const {};

  bool isEmpty() => this == const StreakConfig.empty();
}
