import 'package:skillapp/core/errors/exceptions.dart';

enum StreakType { infinity, daily, weekly, monthly, notAvailable }

enum StreakStatus { active, inactive, completed, notAvailable }

enum StreakEntryStatus { completed, inProgress, notAvailable, notStarted }

enum StreakConfigTypes {
  maxDays,
  minQuestionsPerDay,
  holidaySkipAllowed,
  minDaysPerCalendarMonth,
  minDaysPerCalendarWeek,
  minDaysPerCalendarYear
}

Map<String, StreakConfigTypes> streakConfigTypeToStringMap = {
  'r_max_days': StreakConfigTypes.maxDays,
  'r_min_questions_per_day': StreakConfigTypes.minQuestionsPerDay,
  'r_holiday_skip_allowed': StreakConfigTypes.holidaySkipAllowed,
  'r_min_days_cal_week': StreakConfigTypes.minDaysPerCalendarMonth,
  'r_min_days_cal_month': StreakConfigTypes.minDaysPerCalendarWeek,
  'r_min_days_cal_year': StreakConfigTypes.minDaysPerCalendarYear
};

StreakEntryStatus stringToStreakEntryStatus(String status) {
  switch (status) {
    case 'completed':
      return StreakEntryStatus.completed;
    case 'inProgress':
      return StreakEntryStatus.inProgress;
    case 'notAvailable':
      return StreakEntryStatus.notAvailable;
    case 'notStarted':
      return StreakEntryStatus.notStarted;
    default:
      throw ServerException(
          message: 'Invalid Streak entry status value $status',
          statusCode: '500');
  }
}

StreakStatus stringToStatus(String status) {
  switch (status) {
    case 'active':
      return StreakStatus.active;
    case 'inactive':
      return StreakStatus.inactive;
    case 'completed':
      return StreakStatus.completed;
    case 'notAvailable':
      return StreakStatus.notAvailable;
    default:
      throw ServerException(
          message: 'Invalid Streak status value $status', statusCode: '500');
  }
}

StreakType stringToType(String type) {
  switch (type) {
    case 'infinity':
      return StreakType.infinity;
    case 'daily':
      return StreakType.daily;
    case 'weekly':
      return StreakType.weekly;
    case 'monthly':
      return StreakType.monthly;
    case 'notAvailable':
      return StreakType.notAvailable;
    default:
      throw ServerException(
          message: 'Invalid Streak type value $type', statusCode: '500');
  }
}
