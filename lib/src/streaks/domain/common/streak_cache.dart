import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';

class StreaksCache {
  CurrentStreak _currentStreak = const CurrentStreak.empty();
  StreakEntry _currentDayStreak = const StreakEntry.empty();
  List<StreakConfig> _streakConfigList = [];

  void setCurrentStreak(CurrentStreak currentStreak) {
    _currentStreak = currentStreak;
  }

  CurrentStreak getCurrentStreak() {
    return _currentStreak;
  }

  String getCurrentStreakId() {
    return _currentStreak.currentStreakId;
  }

  void setCurrentDayStreak(StreakEntry currentDayStreak) {
    _currentDayStreak = currentDayStreak;
  }

  StreakEntry getCurrentDayStreak() {
    return _currentDayStreak;
  }

  void clear() {
    _currentStreak = const CurrentStreak.empty();
    _currentDayStreak = const StreakEntry.empty();
  }

  void clearCurrentStreak() {
    _currentStreak = const CurrentStreak.empty();
  }

  void clearCurrentDayStreak() {
    _currentDayStreak = const StreakEntry.empty();
  }

  bool isCurrentStreakDetailsAvailable() {
    return !_currentStreak.isEmpty();
  }

  bool isCurrentStreakActive() {
    return !_currentStreak.isEmpty() &&
        _currentStreak.status == StreakStatus.active;
  }

  bool isCurrentDayStreakDetailsAvailable() {
    return !_currentDayStreak.isEmpty();
  }

  void incrementCurrentDayStreakCount() {
    int currentCount = _currentDayStreak.completedCount == -1
        ? 0
        : _currentDayStreak.completedCount;
    _currentDayStreak = _currentDayStreak.copyWith(
      completedCount: currentCount + 1,
      status: currentCount + 1 == _currentDayStreak.targetCount
          ? StreakEntryStatus.completed
          : _currentDayStreak.status,
    );
  }

  bool isCurrentDayStreakCompleted() {
    return !_currentDayStreak.isEmpty() &&
        (_currentDayStreak.completedCount >= _currentDayStreak.targetCount);
  }

  bool isCurrentDayStreakCompletesInNextIncrement() {
    return _currentDayStreak.completedCount + 1 ==
        _currentDayStreak.targetCount;
  }

  void setStreakConfigList(List<StreakConfig> streakConfigList) {
    _streakConfigList = streakConfigList;
  }

  List<StreakConfig> getStreakConfigList() {
    return _streakConfigList;
  }

  StreakConfig getStreakConfigById(String id) {
    return _streakConfigList.firstWhere(((element) => element.id == id),
        orElse: () => const StreakConfig.empty());
  }
}
