import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/streaks/domain/common/streak_enums.dart';
import 'package:skillapp/src/streaks/domain/entities/streaks.dart';

abstract class StreakDetailsRepo {
  const StreakDetailsRepo();

  ResultFuture<CurrentStreak> getCurrentStreakInfo();

  ResultFuture<StreakStatus> getCurrentStreakStatus(String streakId);

  ResultFuture<Streak> fetchStreakFromId(String currentStreakId);

  ResultFuture<List<StreakConfig>> fetchStreakConfigs();

  ResultFuture<StreakConfig> fetchStreakConfigById(String id);

  ResultFuture<void> updateCurrentStreakData(
      String streakId, int currentStreakCount, StreakStatus streakStatus);

  ResultFuture<void> updateCurrentStreakId(String streakId);

  ResultFuture<void> updateCurrentStreakStatus(StreakStatus streakStatus);

  ResultFuture<void> updateCurrentStreakCount(int currentStreakCount);

  ResultFuture<void> markStreakDayEntryAsCompleted(
      String streakId, String streakDay, StreakEntryStatus completed);

  ResultFuture<void> addStreakDayEntryToStreak(
      String streakId, String streakDay);

  ResultFuture<Streak> addNewStreak(StreakType type);

  ResultFuture<CompleteStreaksData> fetchCompleteStreaksData();

  ResultFuture<StreakEntry> getCurrentDayStreakStatus(String currentStreakId);

  ResultFuture<StreakEntry> getStreakDayStatus(String streakId, String date);

  ResultFuture<void> updateStreakDayDetails(String streakId, String streakDayId,
      StreakEntryStatus status, int completedCount);

  ResultFuture<void> updateStreakDayDetailsAndIncrementCacheCount(
      String streakId,
      String streakDayId,
      StreakEntryStatus status,
      int completedCount);

  ResultFuture<void> markStreakAsInactive(String params);
}
