import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';

class QuizDemoScreen extends StatefulWidget {
  const QuizDemoScreen({super.key});

  factory QuizDemoScreen.routeBuilder(
      BuildContext context, GoRouterState state) {
    return const QuizDemoScreen();
  }

  @override
  State<QuizDemoScreen> createState() => _QuizDemoScreenState();
}

class _QuizDemoScreenState extends State<QuizDemoScreen> {
  // Selected answer index
  int? selectedAnswerIndex = 2; // Option C is selected (1425g)
  bool showResult = true; // To show the result indicators

  // Answer options data
  final List<Map<String, dynamic>> answerOptions = [
    {'id': 'A', 'text': '975 g', 'isCorrect': false},
    {'id': 'B', 'text': '1175 g', 'isCorrect': false},
    {'id': 'C', 'text': '1425 g', 'isCorrect': true},
    {'id': 'D', 'text': '1825 g', 'isCorrect': false},
    {'id': 'E', 'text': '2425 g', 'isCorrect': false},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          // App Bar
          _buildAppBar(),

          // Main Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left side - Question description
                    Expanded(
                      child: _buildQuestionDescription(),
                    ),

                    // Divider
                    Container(
                      width: 1,
                      color: Colors.grey[300],
                    ),

                    // Right side - Answer options
                    Expanded(
                      child: _buildAnswerOptions(),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom Navigation
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // App Logo and Name
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.purple[100],
                radius: 16,
                child: Image.asset('assets/images/logo/ninja-icon-face.png',
                    height: 20),
              ),
              const SizedBox(width: 8),
              const Text(
                'Selective Ninja',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),

          const SizedBox(width: 16),

          // Timer
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.blue),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Row(
              children: [
                Icon(Icons.timer, size: 16, color: Colors.blue),
                SizedBox(width: 4),
                Text(
                  '00 Hr 30 Min',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Question Counter
          const Row(
            children: [
              Text(
                'Question 1 of 35',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              SizedBox(width: 8),
              Icon(Icons.list_alt, size: 20),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionDescription() {
    double fem = ScreenUtil.getFem(context);

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Three packages are weighted together. Their total mass is shown on the scale.',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 24),

          // Scale Image
          Center(
            child: Column(
              children: [
                // Packages image
                Image.asset(
                  'assets/images/quiz_demo/calculator.png',
                  height: 80,
                ),

                // Scale image
                Image.asset(
                  'assets/images/quiz_demo/calculator.png',
                  height: 120,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Bullet points
          Row(
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              RichText(
                text: const TextSpan(
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  ),
                  children: [
                    TextSpan(
                      text: 'Package 1 ',
                      style: TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(text: 'has a mass of 850g.'),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              RichText(
                text: const TextSpan(
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  ),
                  children: [
                    TextSpan(
                      text: 'Package 2 ',
                      style: TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(text: 'has a mass of 225 g.'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOptions() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'What is the mass of package 3?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),

          const SizedBox(height: 24),

          // Answer options
          ...answerOptions.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = selectedAnswerIndex == index;
            final isCorrect = option['isCorrect'] as bool;

            // Determine the option style based on selection and correctness
            Color borderColor = Colors.grey[300]!;
            Color backgroundColor = Colors.white;
            Widget? leadingIcon;

            if (showResult && isSelected) {
              if (isCorrect) {
                borderColor = Colors.green[200]!;
                backgroundColor = Colors.green[50]!;
                leadingIcon = Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 16),
                );
              } else {
                borderColor = Colors.red[200]!;
                backgroundColor = Colors.white;
                leadingIcon = Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.close, color: Colors.white, size: 16),
                );
              }
            }

            // Special case for option E which has a purple indicator
            if (index == 4) {
              borderColor = Colors.purple;
              leadingIcon = Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.purple[400],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.diamond, color: Colors.white, size: 16),
                    SizedBox(width: 4),
                    Text(
                      'Option number',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              );
            }

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                border: Border.all(color: borderColor),
                borderRadius: BorderRadius.circular(8),
                color: backgroundColor,
              ),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                leading: Container(
                  width: 24,
                  height: 24,
                  alignment: Alignment.center,
                  child: Text(
                    option['id'],
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: index == 4 ? Colors.purple : Colors.black54,
                    ),
                  ),
                ),
                title: Text(
                  option['text'],
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
                trailing: leadingIcon,
                onTap: () {
                  if (!showResult) {
                    setState(() {
                      selectedAnswerIndex = index;
                    });
                  }
                },
              ),
            );
          }),

          const SizedBox(height: 16),

          // Incorrect indicator at the bottom
          if (showResult &&
              selectedAnswerIndex != null &&
              !answerOptions[selectedAnswerIndex!]['isCorrect'])
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child:
                        const Icon(Icons.close, color: Colors.white, size: 16),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Incorrect',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          OutlinedButton.icon(
            onPressed: () {
              context.go('/home');
            },
            icon: const Icon(Icons.arrow_back_ios, size: 16),
            label: const Text('Back'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.black54,
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),

          const Spacer(),

          // Flag button
          OutlinedButton.icon(
            onPressed: () {},
            icon: const Icon(Icons.flag_outlined, size: 16),
            label: const Text('Flag'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.black54,
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),

          const SizedBox(width: 16),

          // Next button
          ElevatedButton.icon(
            onPressed: () {},
            icon: const Text('Next'),
            label: const Icon(Icons.arrow_forward_ios, size: 16),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo[700],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}
