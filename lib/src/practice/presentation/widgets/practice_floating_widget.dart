import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/presentation/reusable_widgets/reusable_widgets.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:super_tooltip/super_tooltip.dart';

class HintAndSolutionWidget extends StatefulWidget {
  final PracticeState state;

  const HintAndSolutionWidget({super.key, required this.state});

  @override
  State<StatefulWidget> createState() => _HintAndSolutionState();
}

@override
class _HintAndSolutionState extends State<HintAndSolutionWidget> {
  final _controller = SuperTooltipController();

  void showTooltip() {
    _controller.showTooltip();
  }

  void hideToolTip() {
    _controller.hideTooltip();
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    // double fem = MediaQuery.of(context).size.width / baseWidth;
    double fem = ScreenUtil.getFem(context);
    // double ffem = fem * 0.97;
    Color hintBgColor = const Color(0XFF964EC2);
    bool showExplanation = false;
    var correctAnswer = '';

    if (widget.state.lastAttemptStatus == AttemptStatus.success ||
        widget.state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
      showExplanation = true;
    }

    QuestionAndAnswers question = widget.state.questionAndAnswer;
    List<OptionEntry> options = question.answerOptions.entries;
    for (int i = 0; i < options.length; i++) {
      if (options[i].id == question.correctAnswer) {
        correctAnswer = options[i].displayId;
      }
    }

    return FloatingActionButton(
      onPressed: () {
        if (_controller.isVisible) {
          _controller.hideTooltip();
          hintBgColor = const Color(0XFF964EC2);
        } else {
          _controller.showTooltip();
          hintBgColor = const Color(0XFFFF7BBF);
        }
      },

      backgroundColor:
          hintBgColor, //_controller.isVisible?Color(0XFFFF7BBF):Color(0XFF964EC2),
      shape: const CircleBorder(),
      child: Container(
        height: 40 * fem,
        width: 40 * fem,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25),
              offset: const Offset(0, 4),
              blurRadius: 4,
            ),
          ],
        ),
        child: SuperTooltip(
          controller: _controller,
          popupDirection: TooltipDirection.up,
          borderColor: kSecondaryColor,
          arrowTipDistance: 30.0,
          content: SingleChildScrollView(
            child: Wrap(children: [
              showExplanation
                  ? Center(
                      child: Text(
                        'Correct answer is option $correctAnswer',
                        softWrap: true,
                        style: kPraticeHintTextTs,
                      ),
                    )
                  : Container(),
              showExplanation
                  ? getDescriptionFormat(
                      question.explanation.descAndImgDataList, context)
                  : getDescriptionFormat(
                      question.helpTip.descAndImgDataList, context),
              /* getDescriptionFormat(
                      question.explanation.descAndImgDataList[0]
                          .descAndImgDataEntryList,
                      context)
                  : getDescriptionFormat(
                      question
                          .helpTip.descAndImgDataList[0].descAndImgDataEntryList,
                      context),*/
            ]),
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 7.0 * fem, right: 1 * fem),
            child: SvgPicture.asset(
              'assets/images/practice/hint.svg',
              width: 22.5,
              height: 22,
            ),
          ),
        ),
      ),
    );
  }
}
