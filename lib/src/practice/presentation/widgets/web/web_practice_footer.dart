import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class WebPraticeScreenFooter extends StatefulWidget {
  const WebPraticeScreenFooter({super.key});

  @override
  State<WebPraticeScreenFooter> createState() => _WebPraticeScreenFooter();
}

class _WebPraticeScreenFooter extends State<WebPraticeScreenFooter> {
  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    double hfem = ScreenUtil.getHfem(context);
    double ffem = fem * 0.97;
    String subject = '';

    return Row(
      //  mainAxisAlignment: MainAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // const FlagPopupWidget(),
        BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          if (!state.isError) {
            return GestureDetector(
              onTap: () {
                context.read<PracticeBloc>().add(NextButtonClicked());
              },
              child: BlocListener<PracticeBloc, PracticeState>(
                listener: (context, state) {
                  subject = state.subjectId;
                },
                child: Container(
                  //  height: 60 * hfem,
                  height: 48,
                  margin:
                      EdgeInsets.fromLTRB(40 * fem, 0 * fem, 0 * fem, 0 * fem),
                  padding: EdgeInsets.fromLTRB(28 * fem, 12, 28 * fem, 12),
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side:
                          const BorderSide(width: 1, color: Color(0xFF50409A)),
                      //  borderRadius: BorderRadius.circular(40 * fem),
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      state.lastAttemptStatus == AttemptStatus.notAttempted
                          ? 'Skip'
                          : 'Next',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: kPrimaryColor,
                        //  fontSize: 16 * ffem,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 0,
                      ),
                    ),
                  ),
                ),
              ),
            );
          } else {
            return Container();
          }
        }),
        //SizedBox(width: 80 * fem),
        BlocBuilder<PracticeBloc, PracticeState>(
          builder: (context, state) {
            if (!state.isError) {
              final isSubmitted = state.lastAttemptStatus !=
                      AttemptStatus.notAttempted &&
                  state.lastAttemptStatus != AttemptStatus.retryingAfterFailure;
              const buttonText = 'Submit';

              bool isOptionSelected = state.optionSelected;
              bool isCorrectAnswer =
                  state.lastAttemptStatus == AttemptStatus.success;

              return GestureDetector(
                onTap: () {
                  if ((isCorrectAnswer ||
                      state.lastAttemptStatus ==
                          AttemptStatus.lastAttemptDone)) {
                    // condition for dummy click
                    return;
                  } else if (isSubmitted) {
                    // Reset the question state to default
                    BlocProvider.of<PracticeBloc>(context)
                        .add(ResetQuestionForRetryEvent());
                  } else {
                    // Handle submit button press event here
                    BlocProvider.of<PracticeBloc>(context)
                        .add(SubmitQuestionEvent());
                  }
                },
                child: Container(
                  //  margin:EdgeInsets.fromLTRB(16 * fem, 0 * fem, 32 * fem, 0 * fem),
                  margin: EdgeInsets.fromLTRB(0 * fem, 0, 45 * fem, 0),

                  padding: EdgeInsets.fromLTRB(34 * fem, 12, 34 * fem, 12),
                  //  height: 60 * hfem,
                  height: 48,
                  decoration: BoxDecoration(
                    color: getSubmitButtonColor(
                        isOptionSelected, state.lastAttemptStatus),
                    // borderRadius: BorderRadius.circular(40 * fem),
                    borderRadius: BorderRadius.circular(28),
                  ),
                  child: Center(
                      child: isSubmitted && !isCorrectAnswer
                          ? SvgPicture.asset(
                              'assets/images/Retry.svg',
                            )
                          : const Text(
                              buttonText,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                            )),
                ),
              );
            } else {
              return Container();
            }
          },
        ),
      ],
    );
  }
}
