import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/web/web_description_image_display.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import 'package:skillapp/core/configs/themes/custom_text_styles.dart';

class WebHintAndSolutionDrawer extends StatelessWidget {
  const WebHintAndSolutionDrawer({
    super.key,
    required this.showExplanation,
    required this.correctAnswer,
    required this.question,
    required GlobalKey<ScaffoldState> scaffoldKey,
  }) : _scaffoldKey = scaffoldKey;

  final bool showExplanation;
  final String correctAnswer;
  final QuestionAndAnswers question;
  final GlobalKey<ScaffoldState> _scaffoldKey;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return Drawer(
      backgroundColor: kOnSurfaceTextColor,
      elevation: 0.2,
      width: 600 * fem,
      child: Stack(
        children: [
          SingleChildScrollView(
            child: Container(
              padding: EdgeInsets.fromLTRB(48 * fem, 50, 48 * fem, 64),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.only(bottom: 24),
                      decoration: const BoxDecoration(
                        border: Border(
                          left: BorderSide(color: kOnSurfaceTextColor),
                          top: BorderSide(color: kOnSurfaceTextColor),
                          right: BorderSide(color: kOnSurfaceTextColor),
                          bottom:
                              BorderSide(width: 1, color: Color(0xFFCCCCCC)),
                        ),
                      ),
                      child: showExplanation
                          ? const Text(
                              'Answer Explanation',
                              style: TextStyle(
                                color: Color(0xFF121212),
                                fontSize: 20,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                              textAlign: TextAlign.left,
                            )
                          : const Text(
                              'We got you ! Here is your hint',
                              style: TextStyle(
                                color: Color(0xFF121212),
                                fontSize: 20,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                              textAlign: TextAlign.left,
                            ),
                    ),
                    const SizedBox(height: 20),
                    showExplanation
                        ? Center(
                            child: Text(
                              'Correct answer is option $correctAnswer',
                              softWrap: true,
                              style: kPraticeHintTextTs,
                            ),
                          )
                        : Container(),
                    showExplanation
                        ? const SizedBox(height: 20)
                        : const SizedBox(height: 0),
                    showExplanation
                        ? WebDescriptionAndImageFormatDisplay(
                            descriptionAndImageDataList:
                                question.explanation.descAndImgDataList)
                        : WebDescriptionAndImageFormatDisplay(
                            descriptionAndImageDataList:
                                question.explanation.descAndImgDataList),
                  ]),
            ),
          ),
          Positioned(
            bottom: 0,
            height: 120,
            width: 600 * fem,
            // Adjust this value as needed
            child: Container(
              padding: const EdgeInsets.only(
                top: 24,
                left: 48,
                right: 48,
                bottom: 24,
              ),
              clipBehavior: Clip.antiAlias,
              decoration: const ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x1E000000),
                    blurRadius: 32,
                    offset: Offset(0, 0),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        side: const BorderSide(
                            width: 1, color: Color(0xFF50409A)),
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                    child: GestureDetector(
                      onTap: () {
                        _scaffoldKey.currentState!.closeEndDrawer();
                      },
                      child: const Text(
                        'Back',
                        style: TextStyle(
                          color: Color(0xFF50409A),
                          fontSize: 16,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
