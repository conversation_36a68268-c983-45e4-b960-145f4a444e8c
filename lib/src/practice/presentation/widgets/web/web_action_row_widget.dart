
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';

class ActionRowWidget extends StatelessWidget {
  final String labelText;
  final String leadingIcon;
  final VoidCallback onTap;

  const ActionRowWidget(
      {super.key,
      required this.labelText,
      required this.leadingIcon,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return GestureDetector(
      onTap: onTap,
      //() {
      //  log("AW: Action row is clicked");
      // },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                // child: SvgPicture.asset('assets/images/practice/web/list.svg'),
                child: SvgPicture.asset(leadingIcon),
              ),
              SizedBox(width: 16 * fem),
              Text(
                labelText,
                style: const TextStyle(
                  color: Color(0xFF121212),
                  fontSize: 16,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0,
                ),
              ),
            ],
          ),
          SizedBox(
            width: 24,
            height: 24,
            child: SvgPicture.asset(
                'assets/images/practice/web/arrow_forward.svg'),
          ),
        ],
      ),
    );
  }
}
