import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_action_row_widget.dart';

class MoreActionsWidget extends StatelessWidget {
  const MoreActionsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 32 * fem, vertical: 36),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 24,
            offset: Offset(0, 0),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.only(bottom: 20),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: kOnSurfaceTextColor),
                top: BorderSide(color: kOnSurfaceTextColor),
                right: BorderSide(color: kOnSurfaceTextColor),
                bottom: BorderSide(width: 1, color: Color(0xFFE5E5E5)),
              ),
            ),
            child: const Text(
              'More Actions',
              style: TextStyle(
                color: Color(0xFF121212),
                fontSize: 20,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                height: 0,
              ),
            ),
          ),
          const SizedBox(height: 32),
          ActionRowWidget(
              labelText: 'Flagged questions',
              leadingIcon: 'assets/images/practice/web/save.svg',
              onTap: () {
                log("AW: Flag question row is clicked");
                context.go('/flaggedSubjects');
              }),
          const SizedBox(height: 32),
          ActionRowWidget(
              labelText: 'Attempted questions',
              leadingIcon: 'assets/images/practice/web/list.svg',
              onTap: () {
                log("AW: Attempted question row is clicked");
                context.go('/history');
              }),
        ],
      ),
    );
  }
}
