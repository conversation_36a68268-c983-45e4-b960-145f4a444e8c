import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class PraticeScreenFooter extends StatefulWidget {
  const PraticeScreenFooter({super.key});

  @override
  State<PraticeScreenFooter> createState() => _PracticeScreenFooter();
}

class _PracticeScreenFooter extends State<PraticeScreenFooter> {
  FlaggedStatus? selectedFlag;

  @override
  Widget build(BuildContext context) {
    // double baseWidth = 375;
    // double fem = MediaQuery.of(context).size.width / baseWidth;
    double fem = ScreenUtil.getFem(context);
    double ffem = fem * 0.97;
    String subject = '';

    /*   final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        Offset.zero,
        Offset.zero.translate(0, -50), // Adjust the y-coordinate offset here
      ),
      Offset.zero & MediaQuery.of(context).size,
    );*/

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const FlagPopupWidget(),
        Expanded(
          child: BlocBuilder<PracticeBloc, PracticeState>(
            builder: (context, state) {
              if (!state.isError) {
                final isSubmitted =
                    state.lastAttemptStatus != AttemptStatus.notAttempted &&
                        state.lastAttemptStatus !=
                            AttemptStatus.retryingAfterFailure;
                const buttonText = 'Submit';
                // bool isOptionSelected = false;
                bool isOptionSelected = state.optionSelected;
                bool isCorrectAnswer =
                    state.lastAttemptStatus == AttemptStatus.success;

                /*   state.question.options.forEach((options) {
                  if (options.status == AnswerOptionStatuses.selected)
                    isOptionSelected = true;
                  return;
                }); */

                return GestureDetector(
                  onTap: () {
                    if ((isCorrectAnswer ||
                        state.lastAttemptStatus ==
                            AttemptStatus.lastAttemptDone)) {
                      // condition for dummy click
                      return;
                    } else if (isSubmitted) {
                      // Reset the question state to default
                      BlocProvider.of<PracticeBloc>(context)
                          .add(ResetQuestionForRetryEvent());
                    } else {
                      // Handle submit button press event here
                      BlocProvider.of<PracticeBloc>(context)
                          .add(SubmitQuestionEvent());
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.fromLTRB(
                        16 * fem, 0 * fem, 16 * fem, 0 * fem),
                    padding: EdgeInsets.fromLTRB(
                        16 * fem, 0 * fem, 16 * fem, 0 * fem),
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: getSubmitButtonColor(
                          isOptionSelected, state.lastAttemptStatus),
                      borderRadius: BorderRadius.circular(40 * fem),
                    ),
                    child: Center(
                        child: isSubmitted && !isCorrectAnswer
                            ? SvgPicture.asset(
                                'assets/images/Retry.svg',
                              )
                            : Text(
                                buttonText,
                                textAlign: TextAlign.center,
                                style: SafeGoogleFont(
                                  'Poppins',
                                  fontSize: 14 * ffem,
                                  fontWeight: FontWeight.w500,
                                  height: 1.5 * ffem / fem,
                                  color: const Color(0xffffffff),
                                ),
                              )),
                  ),
                );
              } else {
                return Container();
              }
            },
          ),
        ),
        BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          if (!state.isError) {
            return GestureDetector(
              onTap: () {
                context.read<PracticeBloc>().add(NextButtonClicked());
              },
              child: BlocListener<PracticeBloc, PracticeState>(
                listener: (context, state) {
                  subject = state.subjectId;
                },
                child: Container(
                  // autogroupnuwnFeE (T7i5JEGnq2AwBkS2pPNuWn)
                  width: 48 * fem,
                  height: 48 * fem,
                  margin:
                      EdgeInsets.fromLTRB(0 * fem, 0 * fem, 16 * fem, 0 * fem),
                  decoration: BoxDecoration(
                      color: const Color(0xff50409a),
                      borderRadius: BorderRadius.circular(24 * fem),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xff50409a),
                          offset: Offset(0 * fem, 0 * fem),
                          blurRadius: 1 * fem,
                        ),
                      ]),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: SvgPicture.asset(
                      'assets/images/practice/next.svg',
                      //    width: 48 * fem,
                      //    height: 48 * fem,
                    ),
                  ),
                ),
              ),
            );
          } else {
            return Container();
          }
        }),
      ],
    );
  }

  /* BlocBuilder<PracticeBloc, PracticeState> flagPopupWdiget(double fem) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return BlocBuilder<PracticeBloc, PracticeState>(
      buildWhen: (previous, current) => previous.flagValue != current.flagValue,
      builder: (context, state) {
        ColorFiltered coloredIcon = ColorFiltered(
          colorFilter: ColorFilter.mode(
              getFlagColor(state.flagValue.name), BlendMode.srcIn),
          /* child: state.flagValue.name == FlaggedStatus.notFlagged.name
                ? icon
                : flagSelectedicon, */

        /*  child: state.flagValue.name == FlaggedStatus.notFlagged.name
              ? SvgPicture.asset(
                  'assets/images/practice/flag.svg',
                )
              : SvgPicture.asset(
                  'assets/images/practice/flagSelected.svg',
                ),*/
              child:  SvgPicture.asset(
                  'assets/images/practice/flag.svg',
                ), 
        );

        return Container(
          width: 48 * fem,
          height: 48 * fem,
          margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 0 * fem, 0 * fem),
          decoration: BoxDecoration(
              color: const Color(0xffefefef),
              borderRadius: BorderRadius.circular(24 * fem),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xff50409a),
                  offset: Offset(0 * fem, 0 * fem),
                  blurRadius: 1 * fem,
                ),
              ]),
          child: Padding(
            padding: const EdgeInsets.all(0.0),
            child: PopupMenuButton<FlaggedStatus>(
              color: kOnSurfaceTextColor,
              initialValue: selectedFlag,
              elevation: 0.2,
              position: PopupMenuPosition.over,
              offset: Offset.fromDirection(0, -8),
              icon: coloredIcon,
              // Callback that sets the selected popup menu item.
              onSelected: (FlaggedStatus item) {
                print("AW:Selected item-->${item.name}");
                /*  coloredIcon = ColorFiltered(
                    colorFilter: ColorFilter.mode(
                        getFlagColor(item.name), BlendMode.srcIn),
                    child: icon,
                  );*/

                context
                    .read<PracticeBloc>()
                    .add(FlagUnflagQuestion(selectedFlagId: item));
              },
              itemBuilder: (BuildContext context) {
                //=>

                List<PopupMenuItem<FlaggedStatus>> items = [];

                // <PopupMenuEntry<SampleItem>>[

                items.add(flagMenuItemWidget(
                    fem, FlaggedStatus.veryhard, AppIcons.veryhard));
                items.add(
                    flagMenuItemWidget(fem, FlaggedStatus.hard, AppIcons.hard));
                items.add(flagMenuItemWidget(
                    fem, FlaggedStatus.moderate, AppIcons.moderate));
                items.add(
                    flagMenuItemWidget(fem, FlaggedStatus.easy, AppIcons.easy));

                if (state.flagValue != FlaggedStatus.notFlagged) {
                  items.add(PopupMenuItem<FlaggedStatus>(
                    value: FlaggedStatus.notFlagged,
                    child: Expanded(
                      child: Container(
                        padding: EdgeInsets.fromLTRB(
                            2 * fem, 0 * fem, 0 * fem, 0 * fem),
                        //height: 24 * fem,
                        width: 95 * fem,
                        decoration: BoxDecoration(
                          color: kOnSurfaceTextColor,
                          borderRadius: BorderRadius.circular(12 * fem),
                          border:
                              Border.all(color: kDefaultFlagColor, width: 1.0),
                        ),
                        child: Row(
                          children: [
                            Container(
                              child: SvgPicture.asset(
                                  'assets/images/practice/flag.svg'),
                            ),
                            SizedBox(width: 4.0),
                            Text('Unflag'),
                          ],
                        ),
                      ),
                    ),
                  ));
                }
                return items;
              },
              //  ],
            ),
          ),
        );
      },
    );
  }

  //flagstatus, imagepath
  PopupMenuItem<FlaggedStatus> flagMenuItemWidget(
      double fem, FlaggedStatus flaggedStatus, String imagePath) {
    return PopupMenuItem<FlaggedStatus>(
      value: flaggedStatus,
      child: Container(
        padding: EdgeInsets.fromLTRB(2 * fem, 0 * fem, 0 * fem, 0 * fem),
        height: 24 * fem,
        width: UIParameters.getFlagWidth(flaggedStatus.name),
        decoration: BoxDecoration(
          color: getFlagBgColor(flaggedStatus.name), //kVeryHardBgColor,
          borderRadius: BorderRadius.circular(12 * fem),
          border:
              Border.all(color: getFlagColor(flaggedStatus.name), width: 1.0),
        ),
        child: Row(
          children: [
            Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24 * fem),
                  border: Border.all(
                      color: getFlagColor(flaggedStatus.name), width: 2.0),
                ),
                child: SvgPicture.asset(imagePath)),
            const SizedBox(width: 4.0),
            Text(UIParameters.getFlagText(flaggedStatus.name)),
          ],
        ),
      ),
    );
  }*/
}
