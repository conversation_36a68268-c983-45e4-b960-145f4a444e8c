import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/domain/usecases/fetch_single_question_data.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/experience_score/common/experience_points_enums.dart';
import 'package:skillapp/src/experience_score/domain/usecases/update_exp_points_practice.dart';
import 'package:skillapp/src/flag_question/domain/usecases/check_question_flagged.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_next_questionid_from_flagged_list.dart';
import 'package:skillapp/src/flag_question/domain/usecases/flag_question.dart';
import 'package:skillapp/src/flag_question/domain/usecases/remove_flag.dart';

import 'package:skillapp/src/practice/domain/usecases/fetch_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/load_next_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/populate_practice_questionbank.dart';
import 'package:skillapp/src/practice/domain/usecases/reset_question_for_retry.dart';
import 'package:skillapp/src/practice/domain/usecases/submit_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/update_selected_option.dart';
import 'package:skillapp/src/practice/domain/usecases/util/practice_helper_util.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';
import 'package:skillapp/src/skillpoints/domain/usecases/update_skill_points.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/increment_streakday_along_with_adding_if_not_present.dart';
import 'package:skillapp/src/practice/domain/usecases/check_practice_limit_exceeded_usecase.dart';

part 'practice_event.dart';
part 'practice_state.dart';

class PracticeBloc extends Bloc<PracticeEvent, PracticeState> {
  PracticeBloc({
    required FetchPracticeQuestion fetchQuestion,
    required LoadNextPracticeQuestion loadNextQuestion,
    required SubmitPracticeQuestion submitQuestion,
    required ResetPracticeQuestionForRetry resetQuestionForRetry,
    required PopulatePracticeQuestionBank populateQuestionBank,
    required UpdateSelectedOption updateSelectedOption,
    required FlagQuestion flagQuestion,
    required RemoveFlag removeFlag,
    required CheckQuestionFlagged checkQuestionFlagged,
    required FetchSingleQuestionData fetchSingleQuestionData,
    required FetchNextQuestionIdFromFlaggedList
        fetchNextQuestionIdFromFlaggedList,
    required IncrementCurrentStreakDayAlongWithAddingIfNotPresent
        incrementCurrentStreakDayAlongWithAddingIfNotPresent,
    required UpdateSkillpoints updateSkillpoints,
    required UpdateExperienceScore updateExperienceScore,
    required CacheContext cacheContext,
    required CheckPracticeLimitExceededUseCase
        checkPracticeLimitExceededUseCase,
  })  : _cacheContext = cacheContext,
        _checkPracticeLimitExceededUseCase = checkPracticeLimitExceededUseCase,
        _fetchPracticeQuestion = fetchQuestion,
        _loadNextPracticeQuestion = loadNextQuestion,
        _submitPracticeQuestion = submitQuestion,
        _resetQuestionForRetry = resetQuestionForRetry,
        _populatePracticeQuestionBank = populateQuestionBank,
        _updateSelectedOption = updateSelectedOption,
        _flagQuestion = flagQuestion,
        _removeFlag = removeFlag,
        _checkQuestionFlagged = checkQuestionFlagged,
        _fetchSingleQuestionData = fetchSingleQuestionData,
        _fetchNextQuestionIdFromFlaggedList =
            fetchNextQuestionIdFromFlaggedList,
        _incrementCurrentStreakDayAlongWithAddingIfNotPresent =
            incrementCurrentStreakDayAlongWithAddingIfNotPresent,
        _updateSkillpoints = updateSkillpoints,
        _updateExperienceScore = updateExperienceScore,
        super(PracticeState.createEmptyState()) {
    on<LoadQuestion>(_loadQuestionEventHandler);
    on<SubmitQuestionEvent>(_submitQuestionEventHandler);
    on<SelectAnOption>(_selectAnOptionEventHandler);
    on<ResetQuestionForRetryEvent>(_resetQuestionForRetryEventHandler);
    on<NextButtonClicked>(_nextButtonClickedEventHandler);
    on<FlagUnflagQuestion>(_flagUnflagEventHandler);
    on<FetchSingleQuestionEvent>(_fetchSingleQuestionEventHandler);
  }

  final FetchPracticeQuestion _fetchPracticeQuestion;
  final LoadNextPracticeQuestion _loadNextPracticeQuestion;
  final SubmitPracticeQuestion _submitPracticeQuestion;
  final ResetPracticeQuestionForRetry _resetQuestionForRetry;
  final PopulatePracticeQuestionBank _populatePracticeQuestionBank;
  final UpdateSelectedOption _updateSelectedOption;
  final FlagQuestion _flagQuestion;
  final RemoveFlag _removeFlag;
  final CheckQuestionFlagged _checkQuestionFlagged;
  final FetchSingleQuestionData _fetchSingleQuestionData;
  final FetchNextQuestionIdFromFlaggedList _fetchNextQuestionIdFromFlaggedList;
  final IncrementCurrentStreakDayAlongWithAddingIfNotPresent
      _incrementCurrentStreakDayAlongWithAddingIfNotPresent;
  final UpdateSkillpoints _updateSkillpoints;
  final UpdateExperienceScore _updateExperienceScore;
  final CacheContext _cacheContext;
  final CheckPracticeLimitExceededUseCase _checkPracticeLimitExceededUseCase;

  Future<void> _loadQuestionEventHandler(
    LoadQuestion event,
    Emitter<PracticeState> emit,
  ) async {
    emit(PracticeQuestionLoadingState(state));
    final res = await _populatePracticeQuestionBank(event.subject);

    await res.fold(
      (failure) async {
        emit(PracticeState.createErrorState(
            errorMessage: "Unable to populate question bank"));
      },
      (success) async {
        final result = await _fetchPracticeQuestion(
          FetchPracticeQuestionParams(
              subjectId: event.subject, displayId: event.displayId),
        );

        await result.fold(
          (failure) async {
            emit(PracticeState.createErrorState(errorMessage: failure.message));
          },
          (practiceQuestion) async {
            emit(
              PracticeState(
                  questionAndAnswer: practiceQuestion.questionAndAnswer,
                  subjectId: practiceQuestion.subjectId,
                  lastAttemptStatus: AttemptStatus.notAttempted,
                  displayId: practiceQuestion.displayId,
                  currentFlow: PracticeQuestionFlows.generalPractice),
            );

            // Check if the question is flagged
            final flagResult = await _checkQuestionFlagged(
                CheckQuestionFlaggedParams(
                    practiceQuestion.questionAndAnswer.id, event.subject));

            if (!emit.isDone && flagResult.isRight()) {
              flagResult.fold(
                (_) {},
                (flaggedStatus) {
                  emit(PracticeStateWithFlaggedStatus(state, flaggedStatus));
                },
              );
            }
          },
        );
      },
    );
  }

  void _validateQuestionBuffer() {
    //Below call will check the repo for min questions and populate if needed.
    _populatePracticeQuestionBank(state.subjectId);
  }

  Future<void> _submitQuestionEventHandler(
    SubmitQuestionEvent event,
    Emitter<PracticeState> emit,
  ) async {
    // 1. Check cache for practice_limit_exceeded synchronously
    var cachedLimit = _cacheContext.get('practice_limit_exceeded');
    if (cachedLimit == "exceeded") {
      emit(PracticeState.createErrorState(
          errorMessage: "Practice limit exceeded"));
      return;
    }
    if (cachedLimit == null) {
      // 2. If not present, call use case and cache the value before submitting
      final limitResult = await _checkPracticeLimitExceededUseCase();
      limitResult.fold(
        (failure) {
          // Optionally handle error, but allow submission
          _cacheContext.set('practice_limit_exceeded', "not_exceeded");
        },
        (exceeded) {
          _cacheContext.set('practice_limit_exceeded', "exceeded");
          if (exceeded) {
            emit(PracticeState.createErrorState(
                errorMessage: "Practice limit exceeded"));
            return;
          }
        },
      );
    }

    // 3. Submit the question as usual
    final result = await _submitPracticeQuestion(
      SubmitPracticeQuestionParams(
        question: state.questionAndAnswer,
        subjectId: state.subjectId,
        retryCount: state.retryCount,
      ),
    );

    String questionLevel = "";
    bool isSuccess = false;
    PracticeSuccessState successState = PracticeSuccessState(state);
    result.fold(
        (failure) =>
            emit(PracticeState.createErrorState(errorMessage: failure.message)),
        (practiceQuestion) {
      PracticeState resultState = state.copyWith(
        questionAndAnswer: practiceQuestion.questionAndAnswer,
        lastAttemptStatus: practiceQuestion.lastAttemptStatus,
        retryCount: practiceQuestion.retryCount,
      );
      if (practiceQuestion.lastAttemptStatus == AttemptStatus.success) {
        isSuccess = true;
        successState = PracticeSuccessState(resultState);
        emit(successState);

        //Add code to update the skillpoints
        //TODO- Should the points differ based on the attempt count?

        //Send a new state to increment CurrentDayStreakStatusProvider TODO
      } else {
        emit(resultState);
      }
      questionLevel = practiceQuestion.questionAndAnswer.complexity;
    });

    try {
      _updateExperienceScore(
        UpdateExperienceScoreParams(
            activity: ExperiencePointsActivityType.practice,
            result: isSuccess
                ? ExperiencePointsResultType.success
                : ExperiencePointsResultType.failure,
            complexity: stringToExperiencePointsLevelType(questionLevel)),
      );
    } catch (e) {
      print("Error in updating experience score: $e");
    }

    if (isSuccess) {
      _updateSkillpoints(
        UpdateSkillpointsParams(
          skillEventType: SkillEventType.practiceQuestionSuccess,
          description: "Practice question success",
        ),
      );

      await _incrementCurrentStreakDayAlongWithAddingIfNotPresent();
      emit(PracticeCountIncrementedState(successState));
    }

    // 4. After submission, if cache was present, update it asynchronously
    if (cachedLimit != null) {
      final limitResult = await _checkPracticeLimitExceededUseCase();
      limitResult.fold(
        (failure) {
          // Optionally handle error, do not update cache
        },
        (exceeded) {
          _cacheContext.set('practice_limit_exceeded', exceeded);
        },
      );
    }
  }

  Future<void> _selectAnOptionEventHandler(
    SelectAnOption event,
    Emitter<PracticeState> emit,
  ) async {
    final result = await _updateSelectedOption(
      UpdateSelectedOptionParams(
        selectedOption: event.selectedOption,
        entries: state.questionAndAnswer.answerOptions.entries,
      ),
    );

    result.fold(
      (failure) =>
          emit(PracticeState.createErrorState(errorMessage: failure.message)),
      (entries) => emit(
        state.copyWith(
            questionAndAnswer: state.questionAndAnswer.copyWith(
              answerOptions: state.questionAndAnswer.answerOptions
                  .copyWith(entries: entries),
            ),
            optionSelected: true),
      ),
    );
  }

  Future<void> _resetQuestionForRetryEventHandler(
    ResetQuestionForRetryEvent event,
    Emitter<PracticeState> emit,
  ) async {
    final result =
        await _resetQuestionForRetry(ResetPracticeQuestionForRetryParams(
      question: state.questionAndAnswer,
      retryCount: state.retryCount,
    ));

    await result.fold<Future<void>>(
      (failure) async {
        if (!emit.isDone) {
          emit(PracticeState.createErrorState(errorMessage: failure.message));
        }
      },
      (practiceQuestion) async {
        if (!emit.isDone) {
          emit(state.copyWith(
            questionAndAnswer: practiceQuestion.questionAndAnswer,
            retryCount: practiceQuestion.retryCount,
            lastAttemptStatus: practiceQuestion.lastAttemptStatus,
            optionSelected: practiceQuestion.optionSelected,
          ));
        }
      },
    );
  }

  FutureOr<void> _nextButtonClickedEventHandler(
      NextButtonClicked event, Emitter<PracticeState> emit) async {
    emit(PracticeQuestionLoadingState(state));
    print(
        "AW:PracticeBloc:Next button clicked:state.currentFlow=${state.currentFlow}");
    if (state.currentFlow == PracticeQuestionFlows.generalPractice) {
      await fetchNextQuestionForGeneralPractice(event, emit);
    } else if (state.currentFlow ==
        PracticeQuestionFlows.flaggedQuestionsFlow) {
      await fetchNextQuestionForFlaggedFlow(event, emit);
    }
    //TODO-Add code for remaining flows as applicable
  }

  Future<void> fetchNextQuestionForFlaggedFlow(
    NextButtonClicked event,
    Emitter<PracticeState> emit,
  ) async {
    final result = await _fetchNextQuestionIdFromFlaggedList(
      FetchNextQuestionIdFromFlaggedListParams(
        state.subjectId,
        state.questionAndAnswer.id,
      ),
    );

    result.fold(
        (failure) => emit(
            PracticeState.createErrorState(errorMessage: failure.errorMessage)),
        (success) => add(FetchSingleQuestionEvent(
              success.questionId,
              success.bundleId,
              state.subjectId,
            )));
  }

  Future<void> fetchNextQuestionForGeneralPractice(
    NextButtonClicked event,
    Emitter<PracticeState> emit,
  ) async {
    // 1. Check cache for practice_limit_exceeded synchronously
    final cachedLimit = _cacheContext.get('practice_limit_exceeded');
    if (cachedLimit == "exceeded") {
      emit(PracticeState.createErrorState(
          errorMessage: "Practice limit exceeded"));
      return;
    }
    if (cachedLimit == null) {
      // 2. If not present, call use case and cache the value before loading next question
      final limitResult = await _checkPracticeLimitExceededUseCase();

      bool returnOnExceededLimit = false;
      await limitResult.fold<Future<void>>(
        (failure) async {
          _cacheContext.set('practice_limit_exceeded', "not_exceeded");
        },
        (exceeded) async {
          _cacheContext.set('practice_limit_exceeded', "exceeded");
          if (exceeded) {
            _cacheContext.set('practice_limit_exceeded', "exceeded");
            emit(PracticeState.createErrorState(
                errorMessage: "Practice limit exceeded"));

            returnOnExceededLimit = true;
          } else {
            _cacheContext.set('practice_limit_exceeded', "not_exceeded");
          }
        },
      );

      if (returnOnExceededLimit) {
        return;
      }
    }

    final result = await _loadNextPracticeQuestion(
      LoadNextPracticeQuestionParams(
          subjectId: state.subjectId,
          questionId: state.questionAndAnswer.id,
          displayId: state.displayId,
          bundleId: state.questionAndAnswer.bundleId,
          shortDescription: state.questionAndAnswer.shortDescription,
          lastAttemptStatus: state.lastAttemptStatus),
    );

    result.fold(
      (failure) =>
          emit(PracticeState.createErrorState(errorMessage: failure.message)),
      (practiceQuestion) => emit(
        PracticeState(
            questionAndAnswer: practiceQuestion.questionAndAnswer,
            subjectId: practiceQuestion.subjectId,
            lastAttemptStatus: AttemptStatus.notAttempted,
            displayId: practiceQuestion.displayId,
            currentFlow: PracticeQuestionFlows.generalPractice),
      ),
    );

    // 3. After loading, if cache was present, update it asynchronously
    if (cachedLimit != null) {
      final limitResult = await _checkPracticeLimitExceededUseCase();
      await limitResult.fold<Future<void>>(
        (failure) async {
          _cacheContext.set('practice_limit_exceeded', "not_exceeded");
        },
        (exceeded) async {
          _cacheContext.set('practice_limit_exceeded', "exceeded");
        },
      );
    }
  }

  //This can be called when single question data needs to be loaded in practice screen
  //This will be used in saved and history flows.
  FutureOr<void> _fetchSingleQuestionEventHandler(
      FetchSingleQuestionEvent event, Emitter<PracticeState> emit) async {
    try {
      final result = await _fetchSingleQuestionData(
          FetchSingleQuestionDataParams(
              questionId: event.questionId,
              bundleId: event.bundleId,
              subjectId: event.subject));

      final flagStatusResult = await _checkQuestionFlagged(
          CheckQuestionFlaggedParams(event.questionId, event.subject));

      FlaggedStatus flaggedStatus = FlaggedStatus.notFlagged;

      flagStatusResult.fold((error) => flaggedStatus = FlaggedStatus.notFlagged,
          (sucess) => flaggedStatus = sucess);

      result.fold(
          (failure) => emit(PracticeState.createErrorState(
              errorMessage: "Unable to fetch the question")),
          (success) => emit(PracticeState(
                questionAndAnswer:
                    PracticeHelperUtil.shuffleAndSetDisplayId(success),
                subjectId: event.subject,
                lastAttemptStatus: AttemptStatus.notAttempted,
                displayId: 1,
                currentFlow: state.currentFlow,
                flagValue: flaggedStatus,
              )));
    } catch (e) {
      print("AW:Exception = $e");
      emit(PracticeState.createErrorState(
          errorMessage: "Unable to fetch the question"));
    }
  }

  FutureOr<void> _flagUnflagEventHandler(
      FlagUnflagQuestion event, Emitter<PracticeState> emit) async {
    if (event.selectedFlagId == FlaggedStatus.notFlagged) {
      final result = await _removeFlag(
          RemoveFlagParams(state.questionAndAnswer.id, state.subjectId));
      result.fold(
          (failure) => emit(PracticeState.createErrorState(
              errorMessage: "Unable to remove flag for the question")),
          (success) =>
              emit(state.copyWith(flagValue: FlaggedStatus.notFlagged)));
    } else {
      //Flagging flow
      final result = await _flagQuestion(FlagQuestionParams(
          questionId: state.questionAndAnswer.id,
          bundleId: state.questionAndAnswer.bundleId,
          subjectId: state.subjectId,
          selectedFlagId: event.selectedFlagId,
          shortDescription: state.questionAndAnswer.shortDescription));

      result.fold(
          (failure) => emit(PracticeState.createErrorState(
              errorMessage: "Unable to remove flag for the question")),
          (success) => emit(state.copyWith(flagValue: event.selectedFlagId)));
    }
  }
}
