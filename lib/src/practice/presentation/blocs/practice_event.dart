part of 'practice_bloc.dart';

abstract class PracticeEvent extends Equatable {
  const PracticeEvent();
  @override
  List<Object> get props => [];
}

class LoadQuestion extends PracticeEvent {
  final String subject;
  final int displayId;
  const LoadQuestion({required this.subject, required this.displayId});
}

class SubmitQuestionEvent extends PracticeEvent {}

class ResetQuestionForRetryEvent extends PracticeEvent {}

class SelectAnOption extends PracticeEvent {
  final String selectedOption;

  const SelectAnOption({required this.selectedOption});
}

class NextButtonClicked extends PracticeEvent {}

class FlagUnflagQuestion extends PracticeEvent {
  final FlaggedStatus selectedFlagId;

  const FlagUnflagQuestion({required this.selectedFlagId});
}
//TODO -Fetch questions from history if all bundles are done!!

class FetchSingleQuestionEvent extends PracticeEvent {
  final String questionId;
  final String bundleId;
  final String subject;

  const FetchSingleQuestionEvent(
    this.questionId,
    this.bundleId,
    this.subject,
  );
}

// class FetchSingleFlaggedQuestionEvent extends FetchSingleQuestionEvent {
//   const FetchSingleFlaggedQuestionEvent(
//       super.questionId, super.bundleId, super.subject);
// }

// class FetchSingleHistoryQuestionEvent extends FetchSingleQuestionEvent {
//   const FetchSingleHistoryQuestionEvent(
//       super.questionId, super.bundleId, super.subject);
// }
