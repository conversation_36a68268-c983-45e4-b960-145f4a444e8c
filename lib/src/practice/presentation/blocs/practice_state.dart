part of 'practice_bloc.dart';

class PracticeState extends Equatable {
  final String subjectId;
  final int displayId;
  final QuestionAndAnswers questionAndAnswer;
  final AttemptStatus lastAttemptStatus;
  final int retryCount;
  final bool optionSelected;
  final bool isError;
  final String errorMessage;
  final FlaggedStatus flagValue;
  final PracticeQuestionFlows currentFlow;

  @override
  List<Object> get props => [
        questionAndAnswer.id,
        questionAndAnswer.answerOptions,
        optionSelected,
        lastAttemptStatus,
        isError,
        errorMessage,
        flagValue
      ];

  factory PracticeState.createEmptyState() {
    return PracticeState(
      questionAndAnswer: QuestionAndAnswers.emptyQuestion(),
      subjectId: '',
      lastAttemptStatus: AttemptStatus.notAttempted,
      displayId: 0,
      currentFlow: PracticeQuestionFlows.notAvailable,
    );
  }

  factory PracticeState.createErrorState({required errorMessage}) {
    return PracticeState(
      questionAndAnswer: QuestionAndAnswers.emptyQuestion(),
      subjectId: '',
      lastAttemptStatus: AttemptStatus.notAttempted,
      isError: true,
      errorMessage: errorMessage,
      displayId: 0,
      currentFlow: PracticeQuestionFlows.notAvailable,
    );
  }

  const PracticeState({
    required this.questionAndAnswer,
    required this.subjectId,
    required this.lastAttemptStatus,
    this.retryCount = 0,
    this.optionSelected = false,
    this.isError = false,
    this.errorMessage = '',
    required this.displayId,
    this.flagValue = FlaggedStatus.notFlagged,
    required this.currentFlow,
  });

  PracticeState copyWith(
      {QuestionAndAnswers? questionAndAnswer,
      String? subjectId,
      AttemptStatus? lastAttemptStatus,
      int? retryCount,
      bool? optionSelected,
      bool? isError,
      String? errorMessage,
      int? displayId,
      FlaggedStatus? flagValue,
      PracticeQuestionFlows? currentFlow}) {
    return PracticeState(
        questionAndAnswer: questionAndAnswer ?? this.questionAndAnswer,
        subjectId: subjectId ?? this.subjectId,
        lastAttemptStatus: lastAttemptStatus ?? this.lastAttemptStatus,
        retryCount: retryCount ?? this.retryCount,
        optionSelected: optionSelected ?? this.optionSelected,
        isError: isError ?? this.isError,
        errorMessage: errorMessage ?? this.errorMessage,
        displayId: displayId ?? this.displayId,
        flagValue: flagValue ?? this.flagValue,
        currentFlow: currentFlow ?? this.currentFlow);
  }
}

class PracticeStateWithFlaggedStatus extends PracticeState {
  PracticeStateWithFlaggedStatus(
      PracticeState practiceState, FlaggedStatus flagValue)
      : super(
            questionAndAnswer: practiceState.questionAndAnswer,
            subjectId: practiceState.subjectId,
            lastAttemptStatus: practiceState.lastAttemptStatus,
            retryCount: practiceState.retryCount,
            optionSelected: practiceState.optionSelected,
            isError: practiceState.isError,
            errorMessage: practiceState.errorMessage,
            displayId: practiceState.displayId,
            flagValue: flagValue,
            currentFlow: practiceState.currentFlow);

  //create a factory method which will create the object from PracticeState and set flag value
}

class PracticeQuestionLoadingState extends PracticeState {
  PracticeQuestionLoadingState(PracticeState practiceState)
      : super(
            questionAndAnswer: practiceState.questionAndAnswer,
            subjectId: practiceState.subjectId,
            lastAttemptStatus: practiceState.lastAttemptStatus,
            retryCount: practiceState.retryCount,
            optionSelected: practiceState.optionSelected,
            isError: practiceState.isError,
            errorMessage: practiceState.errorMessage,
            displayId: practiceState.displayId,
            currentFlow: practiceState.currentFlow);

  //create a factory method which will create the object from PracticeState and set flag value
}

class PracticeSuccessState extends PracticeState {
  PracticeSuccessState(PracticeState practiceState)
      : super(
            questionAndAnswer: practiceState.questionAndAnswer,
            subjectId: practiceState.subjectId,
            lastAttemptStatus: practiceState.lastAttemptStatus,
            retryCount: practiceState.retryCount,
            optionSelected: practiceState.optionSelected,
            isError: practiceState.isError,
            errorMessage: practiceState.errorMessage,
            displayId: practiceState.displayId,
            currentFlow: practiceState.currentFlow);
}

class PracticeCountIncrementedState extends PracticeSuccessState {
  PracticeCountIncrementedState(PracticeSuccessState super.practiceState);
}

//class UnableToFetchQuestion extends PracticeStateWithQuestion {}
