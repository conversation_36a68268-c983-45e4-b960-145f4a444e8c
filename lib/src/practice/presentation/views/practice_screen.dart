import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/common/widgets/web/web_answer_options_bloc_widget.dart';
import 'package:skillapp/core/common/widgets/web/web_dailytest_banner.dart';
import 'package:skillapp/core/common/widgets/web/web_question_bloc_widget.dart';
import 'package:skillapp/core/configs/configs.dart';

//import 'package:skillapp/presentation/providers/blocs/blocs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:skillapp/src/practice/presentation/widgets/practice_floating_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/practice_footer.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_hint_and_solution_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_more_actions_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_practice_footer.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_pratice_floating_widget.dart';
import 'package:skillapp/src/streaks/presentation/providers/current_day_streak_status_provider.dart';

class PracticeScreen extends StatefulWidget {
  final String subject;
  const PracticeScreen({super.key, required this.subject});

  factory PracticeScreen.routeBuilder(
      BuildContext context, GoRouterState state, String subject) {
    return PracticeScreen(subject: subject);
  }

  @override
  State<PracticeScreen> createState() => _PracticeScreenState();
}

class _PracticeScreenState extends State<PracticeScreen> {
  @override
  void initState() {
    super.initState();
    context
        .read<PracticeBloc>()
        .add(LoadQuestion(subject: widget.subject, displayId: 0));
  }

  @override
  Widget build(BuildContext context) {
    String appBarTitle = widget.subject;

    return AdaptiveLayout(
        mobileBody: PracticeScreenMobile(
          appBarTitle: appBarTitle,
        ),
        tabletBody: PracticeScreenTablet(
          appBarTitle: appBarTitle,
        ),
        desktopBody: PracticeScreenDesktop(
          appBarTitle: appBarTitle,
        ));

    /* return BlocListener<PracticeBloc, PracticeState>(
      listener: (context, state) {
        if (state.isError) {
          //TODO-Handle error here
        }
        if (state is PracticeStateWithFlaggedStatus) {
          //TODO-@ranjith - Code to set the flagged status can be done here.
          print("AW:Fetched flagged value is ${state.flagValue}");
        }

        if (state is PracticeCountIncrementedState) {
          context
              .read<CurrentDayStreakStatusProvider>()
              .incrementCurrentDayStreakStatus();
        }
      },
      child: Scaffold(
        backgroundColor: practiceScaffoldBgColor, //Colors.blue,
        appBar: AppBarTemplate(
          appBarTitle: appBarTitle,
          actionType: 'Menu',
        ),
        body: const SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: QuestionAnswerWidget(),
        ),
        floatingActionButton:
            BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          return Builder(builder: (context) {
            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.failure ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              return HintAndSolutionWidget(state: state);
            } else {
              return Container();
            }
          });
        }),
        persistentFooterButtons: const [
          PraticeScreenFooter(),
        ],
      ),
    ); */
  }
}

class PracticeScreenMobile extends StatelessWidget {
  final String appBarTitle;

  const PracticeScreenMobile({super.key, required this.appBarTitle});

  @override
  Widget build(BuildContext context) {
    return BlocListener<PracticeBloc, PracticeState>(
      listener: (context, state) {
        if (state.isError) {
          //TODO-Handle error here
        }
        if (state is PracticeStateWithFlaggedStatus) {
          //TODO-@ranjith - Code to set the flagged status can be done here.
          print("AW:Fetched flagged value is ${state.flagValue}");
        }

        if (state is PracticeSuccessState) {
          context
              .read<CurrentDayStreakStatusProvider>()
              .incrementCurrentDayStreakStatus();
        }
      },
      child: Scaffold(
        backgroundColor: practiceScaffoldBgColor, //Colors.blue,
        appBar: AppBarTemplate(
          appBarTitle: appBarTitle,
          actionType: 'Menu',
        ),
        body: const SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: QuestionAnswerWidget(),
        ),
        floatingActionButton:
            BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          return Builder(builder: (context) {
            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.failure ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              return HintAndSolutionWidget(state: state);
            } else {
              return Container();
            }
          });
        }),
        persistentFooterButtons: const [
          PraticeScreenFooter(),
        ],
      ),
    );
  }
}

class PracticeScreenTablet extends StatelessWidget {
  final String appBarTitle;

  const PracticeScreenTablet({super.key, required this.appBarTitle});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.red,
      child: const Text("Tablet Screen"),
    );
  }
}

class PracticeScreenDesktop extends StatelessWidget {
  final String appBarTitle;

  const PracticeScreenDesktop({super.key, required this.appBarTitle});

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

    return BlocListener<PracticeBloc, PracticeState>(
      listener: (context, state) {
        if (state.isError) {
          //TODO-Handle error here
        }
        if (state is PracticeStateWithFlaggedStatus) {
          //TODO-@ranjith - Code to set the flagged status can be done here.
          print("AW:Fetched flagged value is ${state.flagValue}");
        }

        if (state is PracticeSuccessState) {
          context
              .read<CurrentDayStreakStatusProvider>()
              .incrementCurrentDayStreakStatus();
        }
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: kWebMainContentBgColor,
        body: Padding(
          padding: const EdgeInsets.all(40),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const Expanded(
                flex: 2,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      WebQuestionBlocBuilder(),
                      SizedBox(height: 40),
                      WebAnswerOptionsBlocBuilder(),
                      SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 40 * fem),
              const Expanded(
                flex: 1,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      MoreActionsWidget(),
                      SizedBox(height: 40),
                      WebDailyTestBanner(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        endDrawer: BlocBuilder<PracticeBloc, PracticeState>(
          builder: (context, state) {
            bool showExplanation = false;
            var correctAnswer = '';

            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              showExplanation = true;
            }

            QuestionAndAnswers question = state.questionAndAnswer;
            List<OptionEntry> options = question.answerOptions.entries;
            for (int i = 0; i < options.length; i++) {
              if (options[i].id == question.correctAnswer) {
                correctAnswer = options[i].displayId;
              }
            }

            return WebHintAndSolutionDrawer(
                showExplanation: showExplanation,
                correctAnswer: correctAnswer,
                question: question,
                scaffoldKey: scaffoldKey);
          },
        ),
        floatingActionButton:
            BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          return Builder(builder: (context) {
            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.failure ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    flex: 2,
                    child: Padding(
                        padding: EdgeInsets.fromLTRB(1 * fem, 0, 10 * fem, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            WebHintAndSolutionWidget(
                                state: state, scaffoldKey: scaffoldKey),
                          ],
                        )),
                  ),
                  Expanded(flex: 1, child: Container()),
                ],
              );
            } else {
              return Container();
            }
          });
        }),
        bottomNavigationBar: BottomAppBar(
          height: 130,
          notchMargin: 0,
          padding: const EdgeInsets.all(0),
          color: kWebMainContentBgColor,
          //  color: Colors.yellow,
          child: Padding(
            padding: const EdgeInsets.only(left: 40, right: 40),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 90,
                    decoration: ShapeDecoration(
                      color: kOnSurfaceTextColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 32,
                          offset: Offset(0, 0),
                          spreadRadius: 0,
                        )
                      ],
                    ),
                    child: const WebPraticeScreenFooter(),
                  ),
                ),
                Expanded(flex: 1, child: Container()),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
