import 'package:skillapp/src/home/<USER>/models/subjects_model.dart';
import 'package:skillapp/src/practice/domain/entities/attempted_question.dart';

class AttemptedBundleDetailsModel extends AttemptedBundleDetails {
  Set<QuestionBundleModel> attemptedBundleIds = {};

  AttemptedBundleDetailsModel() : super();

  @override
  String toString() {
    return "AttemptedQuestionDetailsModel: attemptedBundleIds.length=${attemptedBundleIds.length} & inprogressBundleIds length= ${inprogressBundleIds.length} & completerBundle length = ${completedBundleIds.length}";
  }
}
