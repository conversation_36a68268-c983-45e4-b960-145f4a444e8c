import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/enums/enum_master.dart';

class PracticeQuestion extends Equatable {
  final String subjectId;
  final int displayId;
  final QuestionAndAnswers questionAndAnswer;
  final AttemptStatus lastAttemptStatus;
  final int retryCount;
  final bool optionSelected;
  final bool isError;
  final String errorMessage;
  final FlaggedStatus flagValue;
  final PracticeQuestionFlows currentFlow;
  final String selectedOption;

  @override
  List<Object> get props => [
        questionAndAnswer.id,
        questionAndAnswer.answerOptions,
        optionSelected,
        lastAttemptStatus,
        isError,
        errorMessage,
        flagValue
      ];

  const PracticeQuestion({
    required this.questionAndAnswer,
    this.subjectId = '',
    this.lastAttemptStatus = AttemptStatus.notAvailable,
    this.retryCount = 0,
    this.optionSelected = false,
    this.isError = false,
    this.errorMessage = '',
    this.displayId = 0,
    this.flagValue = FlaggedStatus.notAvailable,
    this.currentFlow = PracticeQuestionFlows.notAvailable,
    this.selectedOption = '',
  });

  PracticeQuestion copyWith(
      {QuestionAndAnswers? questionAndAnswer,
      String? subjectId,
      AttemptStatus? lastAttemptStatus,
      int? retryCount,
      bool? optionSelected,
      bool? isError,
      String? errorMessage,
      int? displayId,
      FlaggedStatus? flagValue,
      PracticeQuestionFlows? currentFlow,
      String? selectedOption}) {
    return PracticeQuestion(
        questionAndAnswer: questionAndAnswer ?? this.questionAndAnswer,
        subjectId: subjectId ?? this.subjectId,
        lastAttemptStatus: lastAttemptStatus ?? this.lastAttemptStatus,
        retryCount: retryCount ?? this.retryCount,
        optionSelected: optionSelected ?? this.optionSelected,
        isError: isError ?? this.isError,
        errorMessage: errorMessage ?? this.errorMessage,
        displayId: displayId ?? this.displayId,
        flagValue: flagValue ?? this.flagValue,
        currentFlow: currentFlow ?? this.currentFlow,
        selectedOption: selectedOption ?? this.selectedOption);
  }
}
