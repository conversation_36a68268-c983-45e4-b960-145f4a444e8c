import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/practice/domain/entities/practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/populate_practice_questionbank.dart';

class FetchPracticeQuestion extends FutureUsecaseWithParams<PracticeQuestion,
    FetchPracticeQuestionParams> {
  final PopulatePracticeQuestionBank _populatePracticeQuestionBank;
  final CacheContext _cacheContext;

  FetchPracticeQuestion(
      {required PopulatePracticeQuestionBank populateQuestionBank,
      required CacheContext cacheContext})
      : _populatePracticeQuestionBank = populateQuestionBank,
        _cacheContext = cacheContext;

  @override
  ResultFuture<PracticeQuestion> call(
      FetchPracticeQuestionParams params) async {
    try {
      String subjectId = params.subjectId;
      int displayId = params.displayId;
      QuestionAndAnswers result =
          _cacheContext.getSingleRandomQuestionForGivenSubject(subjectId);

      if (result.isEmpty) {
        //await _populatePracticeQuestionBank(subjectId);
        result =
            _cacheContext.getSingleRandomQuestionForGivenSubject(subjectId);
      }

      /*if (!_cacheContext.questionBankHasMinimumBuffer(subjectId)) {
        _populateQuestionBank(subjectId);
      }*/

      return Right(PracticeQuestion(
          subjectId: subjectId,
          questionAndAnswer: result,
          lastAttemptStatus: AttemptStatus.notAttempted,
          retryCount: 0,
          optionSelected: false,
          currentFlow: PracticeQuestionFlows.generalPractice,
          displayId: displayId + 1));
    } catch (e) {
      debugPrint(e.toString());
      return const Left(ServerFailure(
          message: "Unable to fetch next question", statusCode: 500));
    }
  }
}

class FetchPracticeQuestionParams extends Equatable {
  final String subjectId;
  final int displayId;

  const FetchPracticeQuestionParams(
      {required this.subjectId, required this.displayId});

  @override
  List<Object?> get props => [subjectId, displayId];
}
