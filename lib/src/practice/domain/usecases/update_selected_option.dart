import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/practice/domain/usecases/util/practice_helper_util.dart';

class UpdateSelectedOption extends FutureUsecaseWithParams<List<OptionEntry>,
    UpdateSelectedOptionParams> {
  @override
  ResultFuture<List<OptionEntry>> call(
      UpdateSelectedOptionParams params) async {
    try {
      return Right(
        params.entries
            .map(
              (e) => PracticeHelperUtil.updateOptionStatus(
                  e.id == params.selectedOption
                      ? AnswerOptionStatuses.selected
                      : AnswerOptionStatuses.notSelected,
                  e),
            )
            .toList()
            .cast<OptionEntry>(),
      );
    } catch (e) {
      debugPrint(e.toString());
      return const Left(
          ServerFailure(message: "Unknown error", statusCode: 500));
    }
  }
}

class UpdateSelectedOptionParams extends Equatable {
  final List<OptionEntry> entries;
  final String selectedOption;
  const UpdateSelectedOptionParams(
      {required this.selectedOption, required this.entries});

  @override
  List<Object?> get props => [entries];
}
