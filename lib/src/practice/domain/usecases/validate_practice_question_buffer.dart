import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/practice/domain/usecases/populate_practice_questionbank.dart';

class ValidatePracticeQuestionBuffer
    implements FutureUsecaseWithParams<void, String> {
  final PopulatePracticeQuestionBank _populatePracticeQuestionBank;
  final CacheContext _cacheContext;

  ValidatePracticeQuestionBuffer(
      {required PopulatePracticeQuestionBank populateQuestionBank,
      required CacheContext cacheContext})
      : _populatePracticeQuestionBank = populateQuestionBank,
        _cacheContext = cacheContext;

  @override
  ResultFuture<void> call(String params) async {
    String subjectId = params;
    if (!_cacheContext.questionBankHasMinimumBuffer(subjectId)) {
      _populatePracticeQuestionBank(subjectId);
    }
    return const Right(null);
  }
}
