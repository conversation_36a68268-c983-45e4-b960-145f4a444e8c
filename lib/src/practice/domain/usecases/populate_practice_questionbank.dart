import 'dart:math';

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/core/utils/repository_constants.dart';
import 'package:skillapp/src/practice/domain/entities/attempted_question.dart';
import 'package:skillapp/src/practice/domain/repos/practice_questionbank_repo.dart';

class PopulatePracticeQuestionBank
    extends FutureUsecaseWithParams<void, String> {
  final PracticeQuestionBankRepo _questionBankRepo;
  final CacheContext _cacheContext;

  const PopulatePracticeQuestionBank(
      {required PracticeQuestionBankRepo questionBankRepo,
      required CacheContext cacheContext})
      : _questionBankRepo = questionBankRepo,
        _cacheContext = cacheContext;

  @override
  ResultFuture<void> call(String params) async {
    try {
      //synchronized(() async {
      print("JB:check1");
      // AttemptedBundleDetails attemptedBundleDetails =
      //     await fetchAttemptedBundles(params);

      Either<Failure, AttemptedBundleDetails> resEither =
          await _questionBankRepo.getAttemptedBundles(params);

      Set<QuestionAndAnswers> pendingQuestionsInInProgressBundles;
      AttemptedBundleDetails attemptedBundleDetails;
      if (resEither.isRight()) {
        attemptedBundleDetails = resEither.getOrElse(
            () => throwServerException("Unable to fetch bundle Ids"));
        print("JB:check2: $attemptedBundleDetails");
        pendingQuestionsInInProgressBundles =
            await getPendingQuestionsInInProgressBundles(
                attemptedBundleDetails.inprogressBundleIds, params);
      } else {
        print("JB:check3");
        pendingQuestionsInInProgressBundles = {};
        attemptedBundleDetails = AttemptedBundleDetails();
      }

      if (pendingQuestionsInInProgressBundles.length <
          RepositoryConstants.minimumBufferLength) {
        //TODO-Should this be replaced with for-loop?
        //It will be needed if a bundle can contain less number of questions than minimum buffer length!!
        String nextBundleId =
            await fetchRandomBundleId(params, attemptedBundleDetails);
        print("JB:check4:nextBundleId= $nextBundleId");
        Either<Failure, Set<QuestionAndAnswers>> result =
            await _questionBankRepo.getQuestionsFromBundle(
                params, nextBundleId);
        if (resEither.isRight()) {
          Set<QuestionAndAnswers> questions = result.getOrElse(() => {});

          pendingQuestionsInInProgressBundles.addAll(questions);
        } else {
          debugPrint(
              "Unable to fetch questions from bundleId: $nextBundleId, subjectId: $params");
        }
      }

      Map<String, QuestionAndAnswers> pendingQuestionsMap = {
        for (var question in pendingQuestionsInInProgressBundles)
          question.id: question
      };
      _cacheContext.addQuestionListToQuestionBank(params, pendingQuestionsMap);
      //});
      return const Right(null);
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      return const Left(
          ServerFailure(message: "Unable to load questions", statusCode: 500));
    }
  }

  Future<Set<String>> fetchBundleList(String subjectId) async {
    Either<Failure, Set<String>> bundleListResult =
        await _questionBankRepo.getBundleList(subjectId);

    if (bundleListResult.isLeft()) {
      throwServerException("Unable to fetch bundle Ids");
    }

    Set<String> bundleSet = bundleListResult
        .getOrElse(() => throwServerException("Unable to fetch bundle Ids"));

    return bundleSet;
  }

  Future<AttemptedBundleDetails> fetchAttemptedBundles(String subjectId) async {
    dynamic attemptedBundleResult =
        _questionBankRepo.getAttemptedBundles(subjectId);

    if (attemptedBundleResult.isLeft()) {
      throwServerException("Unable to attempted fetch bundle Ids");
    } else {
      print("JB:attemptedBundleResult: $attemptedBundleResult");
      return attemptedBundleResult;
    }
    return attemptedBundleResult;
    /*return await attemptedBundleResult.getOrElse(
        () => throwServerException("Unable to fetch attempted bundle Ids"));*/
  }

  throwServerException(String errorMessage) {
    throw ServerException(
      message: errorMessage,
      statusCode: '500',
    );
  }

  Future<Set<QuestionAndAnswers>> getPendingQuestionsInInProgressBundles(
      Set<String> inprogressBundleIds, String subjectId) async {
    Set<QuestionAndAnswers> pendingQuestionsInInProgressBundles = {};

    for (String bundleId in inprogressBundleIds) {
      dynamic questionsFromBundleE =
          await _questionBankRepo.getQuestionsFromBundle(subjectId, bundleId);
      Set<QuestionAndAnswers> allTheQuestionsFromBundle = {};
      if (questionsFromBundleE.isRight()) {
        allTheQuestionsFromBundle =
            questionsFromBundleE.getOrElse(() => <QuestionAndAnswers>{});
      } else if (questionsFromBundleE.isLeft()) {
        debugPrint(
            "Unable to fetch questions from bundleId: $bundleId, subjectId: $subjectId");
        continue;
      }

      dynamic attemptedQuestionsE = await _questionBankRepo
          .getAttemptedQuestionsFromBundle(subjectId, bundleId);

      Set<String> attemptedQuestionsFromBundle = {};
      if (attemptedQuestionsE.isRight()) {
        attemptedQuestionsFromBundle =
            attemptedQuestionsE.getOrElse(() => <String>{});
      } else if (attemptedQuestionsE.isLeft()) {
        debugPrint(
            "Unable to fetch attempted questions from bundleId: $bundleId, subjectId: $subjectId");
        continue;
      }

      for (var question in allTheQuestionsFromBundle) {
        print("allTheQuestionsFromBundle: ${question.id} ");
      }
      print("JB11:attemptedQuestionsFromBundle: $attemptedQuestionsFromBundle");
      pendingQuestionsInInProgressBundles.addAll(
          allTheQuestionsFromBundle.where((question) =>
              !attemptedQuestionsFromBundle.contains(question.id)));

      for (var question in pendingQuestionsInInProgressBundles) {
        print("JB11:pendingQuestionsInInProgressBundles: ${question.id} ");
      }
    }

    return Future.value(pendingQuestionsInInProgressBundles);
  }

  Future<String> fetchRandomBundleId(
      String subjectId, AttemptedBundleDetails attemptedBundleDetails) async {
    Set<String> bundleSet = await fetchBundleList(subjectId);
    print("JB:bundleSet: $bundleSet");
    print(
        "JB:attemptedBundleDetails.completedBundleIds: ${attemptedBundleDetails.completedBundleIds}");
    print(
        "JB:attemptedBundleDetails.inprogressBundleIds: ${attemptedBundleDetails.inprogressBundleIds}");
    Set<String> bunldleSetCopy = Set.from(bundleSet);

    if (attemptedBundleDetails.completedBundleIds.isNotEmpty ||
        attemptedBundleDetails.inprogressBundleIds.isNotEmpty) {
      bundleSet.removeWhere(
        (bundleId) =>
            attemptedBundleDetails.completedBundleIds.contains(bundleId) ||
            attemptedBundleDetails.inprogressBundleIds.contains(bundleId),
      );
    }
    Random random = Random();
    if (bundleSet.isNotEmpty) {
      List<String> bundleList = bundleSet.toList();

      return bundleList[random.nextInt(bundleList.length)];
    } else {
      //Code to return random bundle id when all questions are already attempted by the user.
      return bunldleSetCopy.toList()[random.nextInt(bunldleSetCopy.length)];
    }
  }
}
