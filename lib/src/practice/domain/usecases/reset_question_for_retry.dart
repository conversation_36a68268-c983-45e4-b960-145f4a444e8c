import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/practice/domain/entities/practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/util/practice_helper_util.dart';

class ResetPracticeQuestionForRetry extends FutureUsecaseWithParams<
    PracticeQuestion, ResetPracticeQuestionForRetryParams> {
  @override
  ResultFuture<PracticeQuestion> call(
      ResetPracticeQuestionForRetryParams params) async {
    try {
      QuestionAndAnswers updatedQuestion = params.question.copyWith(
          answerOptions: params.question.answerOptions.copyWith(
        entries: params.question.answerOptions.entries
            .map((e) => PracticeHelperUtil.updateOptionStatus(
                AnswerOptionStatuses.notSelected, e))
            .toList()
            .cast<OptionEntry>(),
      ));

      PracticeQuestion updatedPracticeQuestion = PracticeQuestion(
        questionAndAnswer:
            PracticeHelperUtil.shuffleAndSetDisplayId(updatedQuestion),
        retryCount: params.retryCount + 1,
        lastAttemptStatus: AttemptStatus.retryingAfterFailure,
        optionSelected: false,
      );

      return Right(updatedPracticeQuestion);
    } catch (e) {
      debugPrint(e.toString());
      return const Left(
          ServerFailure(message: "Unknown error", statusCode: 500));
    }
  }
}

class ResetPracticeQuestionForRetryParams extends Equatable {
  final QuestionAndAnswers question;
  final int retryCount;

  const ResetPracticeQuestionForRetryParams(
      {required this.question, required this.retryCount});

  @override
  List<Object?> get props => [question.id];
}
