import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/enums/enum_master.dart';

class PracticeHelperUtil {
  static int kMaxRetryCount = 1;

  static OptionEntry updateOptionStatus(
      AnswerOptionStatuses status, OptionEntry optionEntry) {
    return optionEntry.copyWith(status: status);
  }

  static QuestionAndAnswers shuffleAndSetDisplayId(
      QuestionAndAnswers question) {
    List<String> optionIdList =
        question.answerOptions.entries.map((e) => e.id).toList();
    optionIdList.shuffle();
    List<OptionEntry> optionsList = question.answerOptions.entries
        .map(
          (e) => e.copyWith(
            displayId: optionIdList.removeLast(),
          ),
        )
        .toList();

    optionsList.sort(
      (a, b) => a.displayId.compareTo(b.displayId),
    );
    QuestionAndAnswers q = question.copyWith(
        answerOptions: question.answerOptions.copyWith(entries: optionsList));
    return q;
  }
}
