import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/practice/domain/entities/attempted_question.dart';

abstract class PracticeQuestionBankRepo {
  const PracticeQuestionBankRepo();

  ResultFuture<AttemptedBundleDetails> getAttemptedBundles(String subjectId);

  ResultFuture<Set<String>> getBundleList(String subjectId);

  ResultFuture<Set<QuestionAndAnswers>> getQuestionsFromBundle(
      String subjectId, String bundleId);

  ResultFuture<Set<String>> getAttemptedQuestionsFromBundle(
      String subjectId, String bundleId);

  ResultFuture<bool> addToAttemptHistory(String id, String bundleId,
      AttemptStatus attemptStatus, String shortDescription, String subjectId);

  ResultFuture<void> markAsAttemptedInUserProfile(
      String subjectId, String bundleId, String questionId);
}
