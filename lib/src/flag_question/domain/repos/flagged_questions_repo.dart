import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';

abstract class FlaggedQuestionsRepo {
  const FlaggedQuestionsRepo();

  ResultFuture<List<Subject>> fetchFlaggedSubjects();

  ResultFuture<List<FlaggedQuestion>> fetchFlaggedQuestionsUnderGivenSubject(
      String subjectId, String flagId);

  ResultFuture<FlaggedStatus> checkQuestionFlagged(
      String questionId, String subject);

  ResultFuture<bool> removeFlag(String questionId, String subject);

  ResultFuture<bool> flagQuestion(String questionId, String bundleId,
      String subjectId, FlaggedStatus selectedFlagId, String shortDescription);
}
