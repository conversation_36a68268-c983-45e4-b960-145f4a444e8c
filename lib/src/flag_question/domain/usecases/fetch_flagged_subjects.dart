import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';

class FetchFlaggedSubjects extends FutureUsecaseWithoutParams {
  final FlaggedQuestionsRepo _flaggedQuestionsRepo;

  FetchFlaggedSubjects({required FlaggedQuestionsRepo flaggedQuestionsRepo})
      : _flaggedQuestionsRepo = flaggedQuestionsRepo;

  @override
  ResultFuture<List<Subject>> call() =>
      _flaggedQuestionsRepo.fetchFlaggedSubjects();
}
