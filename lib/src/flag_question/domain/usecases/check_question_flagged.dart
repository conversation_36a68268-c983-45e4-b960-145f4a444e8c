import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';

class CheckQuestionFlagged
    extends FutureUsecaseWithParams<FlaggedStatus, CheckQuestionFlaggedParams> {
   final FlaggedQuestionsRepo _flaggedQuestionsRepo;

   CheckQuestionFlagged({required FlaggedQuestionsRepo  flaggedQuestionsRepo})
       : _flaggedQuestionsRepo = flaggedQuestionsRepo;

  

   @override
   ResultFuture<FlaggedStatus> call(CheckQuestionFlaggedParams params) =>
       _flaggedQuestionsRepo.checkQuestionFlagged(
           params.questionId, params.subject);
}

class CheckQuestionFlaggedParams extends Equatable {
  final String questionId;
  final String subject;

  const CheckQuestionFlaggedParams(this.questionId, this.subject);

  @override
  List<Object?> get props => [questionId];
}
