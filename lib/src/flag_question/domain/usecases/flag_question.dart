// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';

class FlagQuestion
    implements FutureUsecaseWithParams<bool, FlagQuestionParams> {
  final FlaggedQuestionsRepo _flaggedQuestionsRepo;

  FlagQuestion({required FlaggedQuestionsRepo flaggedQuestionsRepo})
      : _flaggedQuestionsRepo = flaggedQuestionsRepo;
  @override
  ResultFuture<bool> call(FlagQuestionParams params) =>
      _flaggedQuestionsRepo.flagQuestion(
        params.questionId,
        params.bundleId,
        params.subjectId,
        params.selectedFlagId,
        params.shortDescription,
      );
}

class FlagQuestionParams extends Equatable {
  final String questionId;
  final String bundleId;
  final String subjectId;
  final FlaggedStatus selectedFlagId;
  final String shortDescription;

  const FlagQuestionParams({
    required this.questionId,
    required this.bundleId,
    required this.subjectId,
    required this.selectedFlagId,
    required this.shortDescription,
  });

  @override
  List<Object?> get props => [questionId];
}
