import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';

class RemoveFlag implements FutureUsecaseWithParams<bool, RemoveFlagParams> {
  final FlaggedQuestionsRepo _flaggedQuestionsRepo;

  RemoveFlag({required FlaggedQuestionsRepo flaggedQuestionsRepo})
      : _flaggedQuestionsRepo = flaggedQuestionsRepo;

  @override
  ResultFuture<bool> call(RemoveFlagParams params) =>
      _flaggedQuestionsRepo.removeFlag(params.questionId, params.subject);
}

class RemoveFlagParams extends Equatable {
  final String questionId;
  final String subject;

  const RemoveFlagParams(this.questionId, this.subject);

  @override
  List<Object?> get props => [questionId];
}
