import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/domain/usecases/fetch_single_question_data.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';

class FetchNextQuestionDataFromFlaggedList
    implements
        FutureUsecaseWithParams<QuestionAndAnswers,
            FetchNextQuestionDataFromFlaggedListParams> {
  final CacheContext _cacheContext;
  final FetchSingleQuestionData _fetchSingleQuestionData;

  FetchNextQuestionDataFromFlaggedList(
      {required CacheContext cacheContext,
      required FetchSingleQuestionData fetchSingleQuestionData})
      : _cacheContext = cacheContext,
        _fetchSingleQuestionData = fetchSingleQuestionData;

  @override
  ResultFuture<QuestionAndAnswers> call(
      FetchNextQuestionDataFromFlaggedListParams params) async {
    final flaggedQuestion = _cacheContext.getNextFlaggedQuestion(
        params.subjectId, params.questionId);

    if (flaggedQuestion.isEmpty) {
      return const Left(CacheFailure(
          message: "No more flagged questions available", statusCode: 400));
    }

    final result = await _fetchSingleQuestionData(FetchSingleQuestionDataParams(
        subjectId: params.subjectId,
        questionId: flaggedQuestion.questionId,
        bundleId: flaggedQuestion.bundleId));

    return result;
  }
}

class FetchNextQuestionDataFromFlaggedListParams extends Equatable {
  final String subjectId;
  final String questionId;

  const FetchNextQuestionDataFromFlaggedListParams(
      this.subjectId, this.questionId);

  @override
  List<Object> get props => [questionId];
}
