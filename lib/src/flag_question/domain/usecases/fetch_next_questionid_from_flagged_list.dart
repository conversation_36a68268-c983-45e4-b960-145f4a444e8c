import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';

class FetchNextQuestionIdFromFlaggedList
    implements
        FutureUsecaseWithParams<FlaggedQuestion,
            FetchNextQuestionIdFromFlaggedListParams> {
  final CacheContext _cacheContext;

  FetchNextQuestionIdFromFlaggedList({required CacheContext cacheContext})
      : _cacheContext = cacheContext;

  @override
  ResultFuture<FlaggedQuestion> call(
      FetchNextQuestionIdFromFlaggedListParams params) async {
    final flaggedQuestion = _cacheContext.getNextFlaggedQuestion(
        params.subjectId, params.questionId);

    if (flaggedQuestion.isEmpty) {
      return const Left(CacheFailure(
          message: "No more flagged questions available", statusCode: 400));
    }

    return Right(flaggedQuestion);
  }
}

class FetchNextQuestionIdFromFlaggedListParams extends Equatable {
  final String subjectId;
  final String questionId;

  const FetchNextQuestionIdFromFlaggedListParams(
      this.subjectId, this.questionId);

  @override
  List<Object> get props => [questionId];
}
