import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';

class FetchFlaggedQuestions extends FutureUsecaseWithParams<
    List<FlaggedQuestion>, FetchFlaggedQuestionsParams> {
  final FlaggedQuestionsRepo _flaggedQuestionsRepo;

  FetchFlaggedQuestions({required FlaggedQuestionsRepo flaggedQuestionsRepo})
      : _flaggedQuestionsRepo = flaggedQuestionsRepo;

  @override
  ResultFuture<List<FlaggedQuestion>> call(
          FetchFlaggedQuestionsParams params) =>
      _flaggedQuestionsRepo.fetchFlaggedQuestionsUnderGivenSubject(
          params.subjectId, params.flagId);
}

class FetchFlaggedQuestionsParams extends Equatable {
  final String subjectId;
  final String flagId;

  const FetchFlaggedQuestionsParams({
    required this.subjectId,
    required this.flagId,
  });

  @override
  List<Object?> get props => [subjectId, flagId];
}
