import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/flag_question/presentation/blocs/flagged_questions_bloc.dart';

class FlaggedQuestionList extends StatefulWidget {
  final String subject;

  const FlaggedQuestionList({required this.subject, super.key});

  factory FlaggedQuestionList.routeBuilder(
      BuildContext context, GoRouterState state, String subject) {
    return FlaggedQuestionList(subject: subject);
  }

  @override
  State<FlaggedQuestionList> createState() => _FlaggedQuestionListState();
}

class _FlaggedQuestionListState extends State<FlaggedQuestionList> {
  String selectedFlag = 'all';

  @override
  void initState() {
    super.initState();
    String flagId = "";
    print("AW:Before initState in FlaggedQuestionList");
    context
        .read<FlaggedQuestionsBloc>()
        .add(FecthFlaggedQuestionsEvent(widget.subject, flagId));
    print("AW:After initState in FlaggedQuestionList");
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayout(
        mobileBody: FlaggedQuestionsMobile(subject: widget.subject),
        tabletBody: const FlaggedQuestionsTablet(),
        desktopBody: FlaggedQuestionsDesktop(subject: widget.subject));
  }
}

class FlaggedQuestionsMobile extends StatefulWidget {
  final String subject;

  const FlaggedQuestionsMobile({required this.subject, super.key});

  @override
  State<FlaggedQuestionsMobile> createState() => _FlaggedQuestionsMobileState();
}

class _FlaggedQuestionsMobileState extends State<FlaggedQuestionsMobile> {
  String selectedFlag = 'all';

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return Scaffold(
      backgroundColor: kProfileSavedQuestionBgColor,
      appBar: const AppBarTemplate(
          appBarTitle: 'Flagged Questions', actionType: 'NA'),
      body: ListView(
        children: [
          Container(
            width: double.infinity,
            margin: EdgeInsets.fromLTRB(16 * fem, 8 * fem, 0 * fem, 0 * fem),
            child: Wrap(
              alignment: WrapAlignment.start,
              spacing: 24 * fem,
              children: [
                filterChipWidget(fem, kAllBgColor,
                    'assets/images/savedQuestions/allFlag.png', 'All', 'all'),
                filterChipWidget(
                    fem,
                    kVeryHardBgColor,
                    'assets/images/savedQuestions/veryhard.png',
                    'Very Hard',
                    'veryhard'),
                filterChipWidget(fem, kHardBgColor,
                    'assets/images/savedQuestions/hard.png', 'Hard', 'hard'),
                filterChipWidget(
                    fem,
                    kModerateBgColor,
                    'assets/images/savedQuestions/moderate.png',
                    'Moderate',
                    'moderate'),
                filterChipWidget(fem, kEasyBgColor,
                    'assets/images/savedQuestions/easy.png', 'Easy', 'easy'),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: BlocBuilder<FlaggedQuestionsBloc, FlaggedQuestionsState>(
              builder: (context, state) {
                print("state = $state");
                if (state is FlaggedQuestionsFetchedState) {
                  if (state.flaggedQuestions.isEmpty) {
                    // Handle empty flagged subjects case
                    return const Center(
                        child: Text('No flagged Questions available.'));
                  } else {
                    return displaySavedQuestionsWidget(fem, state);
                  }
                } else {
                  return const Center(child: Text('No Questions loaded'));
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  ChoiceChip filterChipWidget(double fem, Color backgroundColor,
      String imagePath, String labeltext, String flagType) {
    String selectedImagePath = 'assets/images/savedQuestions/selected.png';

    bool selectionStatus = false;

    if (selectedFlag == flagType) {
      selectionStatus = true;
    } else {
      selectionStatus = false;
    }

    return ChoiceChip(
      backgroundColor: backgroundColor,
      selectedColor: backgroundColor,
      side: BorderSide(
        color: flagType == 'all'
            ? kProfileMenuSeparationLine
            : getFlagColor(flagType),
      ),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(24 * fem))),
      avatar: CircleAvatar(
        backgroundImage: (selectionStatus
            ? ExactAssetImage(selectedImagePath)
            : ExactAssetImage(imagePath)),
        backgroundColor:
            selectionStatus ? Colors.white : getFlagColor(flagType),
      ),
      label: Text(labeltext,
          style: flagType == 'all'
              ? kSavedQuestionFlagTs(Colors.black)
              : kSavedQuestionFlagTs(getFlagColor(flagType))),
      selected: selectionStatus,
      onSelected: (bool selected) {
        setState(() {
          //_value = selected ? index : null;
          selectedFlag = flagType;

          context
              .read<FlaggedQuestionsBloc>()
              .add(FecthFlaggedQuestionsEvent(widget.subject, flagType));
        });
      },
    );
  }

  Container displaySavedQuestionsWidget(
      double fem, FlaggedQuestionsFetchedState state) {
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 0 * fem),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16 * fem),
      ),
      child: ListView.builder(
        itemCount: state.flaggedQuestions.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final FlaggedQuestion flaggedQuestion = state.flaggedQuestions[index];
          return Container(
            margin: EdgeInsets.fromLTRB(16 * fem, 0, 16 * fem, 0),
            decoration: index == state.flaggedQuestions.length - 1
                ? const BoxDecoration()
                : const BoxDecoration(
                    border: Border(
                    bottom: BorderSide(
                      color: kProfileMenuSeparationLine,
                      width: 1.0,
                    ),
                  )),
            child: ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Container(
                padding:
                    EdgeInsets.fromLTRB(4 * fem, 4 * fem, 4 * fem, 4 * fem),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4 * fem),
                ),
                child: IconTheme(
                  data: const IconThemeData(size: 10),
                  child: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                        getFlagColor(flaggedQuestion.flagId), BlendMode.srcIn),
                    child: SvgPicture.asset(
                      //'assets/images/practice/flag.svg',
                      'assets/images/practice/flagSelected.svg',
                    ),
                  ), // Adjust the icon size here
                  /*  child: SvgPicture.asset('assets/images/practice/flag.svg',
                      height: 24 * fem, width: 24 * fem),*/
                ),
              ),
              title: Text(
                flaggedQuestion.shortDescriptionOrId,
                style: kPracticeAppBarMenuItemTs,
              ),
              trailing: const IconTheme(
                data: IconThemeData(size: 12), // Adjust the icon size here
                child: Icon(Icons.arrow_forward_ios),
              ),
              onTap: () {
                // Handle tap action
                String bundleId = flaggedQuestion.bundleId;
                String questionId = flaggedQuestion.questionId;

                context.push(
                    '/singleQuestionFlow/${Uri.encodeComponent(bundleId)}/${Uri.encodeComponent(questionId)}/${Uri.encodeComponent(widget.subject)}');
              },
            ),
          );
        },
      ),
    );
  }
}

class FlaggedQuestionsTablet extends StatefulWidget {
  const FlaggedQuestionsTablet({super.key});

  @override
  State<FlaggedQuestionsTablet> createState() => _FlaggedQuestionsTabletState();
}

class _FlaggedQuestionsTabletState extends State<FlaggedQuestionsTablet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: const Text('FlaggedQuestionsTablet'),
    );
  }
}

class FlaggedQuestionsDesktop extends StatefulWidget {
  final String subject;

  const FlaggedQuestionsDesktop({required this.subject, super.key});

  @override
  State<FlaggedQuestionsDesktop> createState() =>
      _FlaggedQuestionsDesktopState();
}

class _FlaggedQuestionsDesktopState extends State<FlaggedQuestionsDesktop> {
  String selectedFlag = 'all';

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return SingleChildScrollView(
      child: Container(
        //  padding: EdgeInsets.fromLTRB(40 * fem, 40 * fem, 40 * fem, 40 * fem),
        padding: const EdgeInsets.all(40),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    // padding: EdgeInsets.fromLTRB(28 * fem, 0, 0 * fem, 0),
                    child: Text(
                      'Flagged questions - ${widget.subject}',
                      style: kWebSavedQuestionHeaderTs,
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    // margin: EdgeInsets.fromLTRB(28 * fem, 8, 0 * fem, 0),
                    child: Wrap(
                      alignment: WrapAlignment.start,
                      spacing: 24 * fem,
                      children: [
                        filterChipWidget(
                            fem,
                            kAllBgColor,
                            'assets/images/savedQuestions/allFlag.png',
                            'All',
                            'all'),
                        filterChipWidget(
                            fem,
                            kVeryHardBgColor,
                            'assets/images/savedQuestions/veryhard.png',
                            'Very Hard',
                            'veryhard'),
                        filterChipWidget(
                            fem,
                            kHardBgColor,
                            'assets/images/savedQuestions/hard.png',
                            'Hard',
                            'hard'),
                        filterChipWidget(
                            fem,
                            kModerateBgColor,
                            'assets/images/savedQuestions/moderate.png',
                            'Moderate',
                            'moderate'),
                        filterChipWidget(
                            fem,
                            kEasyBgColor,
                            'assets/images/savedQuestions/easy.png',
                            'Easy',
                            'easy'),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: BlocBuilder<FlaggedQuestionsBloc,
                        FlaggedQuestionsState>(
                      builder: (context, state) {
                        print("state = $state");
                        if (state is FlaggedQuestionsFetchedState) {
                          if (state.flaggedQuestions.isEmpty) {
                            // Handle empty flagged subjects case
                            return const Center(
                                child: Text('No flagged Questions available.'));
                          } else {
                            return displaySavedQuestionsWidget(fem, state);
                          }
                        } else {
                          return const Center(
                              child: Text('No Questions loaded'));
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                // color: Colors.red,
                child: const Text(''),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container displaySavedQuestionsWidget(
      double fem, FlaggedQuestionsFetchedState state) {
    return Container(
        //  margin: EdgeInsets.fromLTRB(16 * fem, 0, 16 * fem, 0 * fem),
        padding: EdgeInsets.fromLTRB(16 * fem, 12, 16 * fem, 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16 * fem),
        ),
        child: Column(
          children: [
            for (var flaggedQuestion in state.flaggedQuestions)
              //  Text("Flagged question ${flaggedQuestion.shortDescriptionOrId}"),

              Card(
                color: Colors.white,
                shadowColor: Colors.white,
                borderOnForeground: false,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(0),
                  side: BorderSide.none,
                ),
                child: ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: Container(
                    padding: EdgeInsets.fromLTRB(4 * fem, 4, 4 * fem, 4),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      //   borderRadius: BorderRadius.circular(4 * fem),
                    ),
                    child: IconTheme(
                      data: const IconThemeData(size: 10),
                      child: ColorFiltered(
                        colorFilter: ColorFilter.mode(
                            getFlagColor(flaggedQuestion.flagId),
                            BlendMode.srcIn),
                        child: SvgPicture.asset(
                          //'assets/images/practice/flag.svg',
                          'assets/images/practice/flagSelected.svg',
                        ),
                      ), // Adjust the icon size here
                      /*  child: SvgPicture.asset('assets/images/practice/flag.svg',
                        height: 24 * fem, width: 24 * fem),*/
                    ),
                  ),
                  title: Text(
                    flaggedQuestion.shortDescriptionOrId,
                    style: kWebSavedQuestionTitleTs,
                  ),
                  trailing: const IconTheme(
                    data: IconThemeData(size: 24), // Adjust the icon size here
                    //   child: Icon(Icons.arrow_forward_ios),
                    child: Icon(Icons.chevron_right),
                  ),
                  onTap: () {
                    // Handle tap action
                    String bundleId = flaggedQuestion.bundleId;
                    String questionId = flaggedQuestion.questionId;

                    context.push(
                        '/singleQuestionFlow/${Uri.encodeComponent(bundleId)}/${Uri.encodeComponent(questionId)}/${Uri.encodeComponent(widget.subject)}');
                  },
                ),
              ),
          ],
        ));
  }

  ChoiceChip filterChipWidget(double fem, Color backgroundColor,
      String imagePath, String labeltext, String flagType) {
    String selectedImagePath = 'assets/images/savedQuestions/selected.png';

    bool selectionStatus = false;

    if (selectedFlag == flagType) {
      selectionStatus = true;
    } else {
      selectionStatus = false;
    }

    return ChoiceChip(
      backgroundColor: backgroundColor,
      selectedColor: backgroundColor,
      side: BorderSide(
        color: flagType == 'all'
            ? kProfileMenuSeparationLine
            : getFlagColor(flagType),
      ),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(24 * fem))),
      avatar: CircleAvatar(
        backgroundImage: (selectionStatus
            ? ExactAssetImage(selectedImagePath)
            : ExactAssetImage(imagePath)),
        backgroundColor:
            selectionStatus ? Colors.white : getFlagColor(flagType),
      ),
      label: Text(labeltext,
          style: flagType == 'all'
              ? kWebSavedQuestionFlagTs(Colors.black)
              : kWebSavedQuestionFlagTs(getFlagColor(flagType))),
      selected: selectionStatus,
      onSelected: (bool selected) {
        setState(() {
          //_value = selected ? index : null;
          selectedFlag = flagType;

          context
              .read<FlaggedQuestionsBloc>()
              .add(FecthFlaggedQuestionsEvent(widget.subject, flagType));
        });
      },
    );
  }
}
