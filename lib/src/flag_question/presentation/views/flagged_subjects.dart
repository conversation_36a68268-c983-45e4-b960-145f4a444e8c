import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/flag_question/presentation/blocs/flagged_questions_bloc.dart';

class FlaggedSubjects extends StatefulWidget {
  const FlaggedSubjects({super.key});
  factory FlaggedSubjects.routeBuilder(_, __) {
    return const FlaggedSubjects();
  }
  @override
  State<FlaggedSubjects> createState() => _FlaggedSubjectsState();
}

class _FlaggedSubjectsState extends State<FlaggedSubjects> {
  @override
  void initState() {
    super.initState();
    context.read<FlaggedQuestionsBloc>().add(FetchFlaggedSubjectsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return const AdaptiveLayout(
        mobileBody: FlaggedSubjectsMobile(),
        tabletBody: FlaggedSubjectsTablet(),
        desktopBody: FlaggedSubjectsDesktop());
  }
}

class FlaggedSubjectsTablet extends StatelessWidget {
  const FlaggedSubjectsTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return const Text("This is tablet view");
  }
}

class FlaggedSubjectsMobile extends StatelessWidget {
  const FlaggedSubjectsMobile({super.key});

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    double ffem = fem * 0.97;
    return Scaffold(
      backgroundColor: kProfileSavedQuestionBgColor,
      appBar: const AppBarTemplate(
          appBarTitle: 'Saved Questions', actionType: 'NA'),
      body: Container(
        padding: EdgeInsets.only(top: 7 * fem),
        child: BlocBuilder<FlaggedQuestionsBloc, FlaggedQuestionsState>(
          builder: (context, state) {
            if (state is FlaggedSubjectsFetchedState) {
              if (state.flaggedSubjects.isEmpty) {
                // Handle empty flagged subjects case
                return const Center(
                    child: Text('No flagged subjects available.'));
              } else {
                // Render the list of flagged subjects
                return ListView.builder(
                  itemCount: state.flaggedSubjects.length,
                  itemBuilder: (context, index) {
                    final subject = state.flaggedSubjects[index];
                    return subjectsListViewWidget(fem, subject, context);
                  },
                );
              }
            } else {
              // Handle other states if needed
              return const Center(child: Text('Loading...'));
            }
          },
        ),
      ),
    );
  }

  Container subjectsListViewWidget(
      double fem, Subject subject, BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 16 * fem, 17 * fem, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8 * fem),
      ),
      child: ListTile(
        contentPadding:
            EdgeInsets.fromLTRB(8 * fem, 0 * fem, 14 * fem, 0 * fem),
        leading: Container(
          padding: EdgeInsets.fromLTRB(4 * fem, 4 * fem, 4 * fem, 4 * fem),
          decoration: BoxDecoration(
            color: getBoxDecColor(subject.description),
            borderRadius: BorderRadius.circular(4 * fem),
          ),
          child: IconTheme(
            data: const IconThemeData(size: 10), // Adjust the icon size here
            child:
                Image.asset(subject.image, height: 28 * fem, width: 28 * fem),
          ),
        ),
        trailing: const IconTheme(
          data: IconThemeData(size: 12), // Adjust the icon size here
          child: Icon(Icons.arrow_forward_ios),
        ),
        title: Text(subject.description, style: kPracticeAppBarMenuItemTs),
        onTap: () {
          context
              .push('/flaggedQuestionList/${Uri.encodeComponent(subject.id)}');
        },
      ),
    );
  }
}

class FlaggedSubjectsDesktop extends StatelessWidget {
  const FlaggedSubjectsDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    return Container(
      padding: EdgeInsets.only(top: 7 * fem),
      // padding: const EdgeInsets.all(40),
      child: BlocBuilder<FlaggedQuestionsBloc, FlaggedQuestionsState>(
        builder: (context, state) {
          if (state is FlaggedSubjectsFetchedState) {
            if (state.flaggedSubjects.isEmpty) {
              // Handle empty flagged subjects case
              return const Center(
                  child: Text('No flagged subjects available.'));
            } else {
              // Render the list of flagged subjects
              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2, // Set the number of columns
                  childAspectRatio: 7.0,
                  // childAspectRatio: 7.0 * fem,
                ),
                itemCount: state.flaggedSubjects.length,
                itemBuilder: (context, index) {
                  final subject = state.flaggedSubjects[index];
                  return subjectsListViewWidget(fem, subject, context);
                },
              );
            }
          } else if (state is FlaggedErrorState) {
            // Handle other states if needed
            return Center(
                child: Text(
                    'Error fetching flagged subjects ${state.errorMessage}'));
          } else {
            return const Center(child: Text('Loading...'));
          }
        },
      ),
    );
  }

  Container subjectsListViewWidget(
      double fem, Subject subject, BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(20 * fem, 20, 20, 20 * fem),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.fromLTRB(8 * fem, 0, 8 * fem, 0),
        leading: Container(
          padding: EdgeInsets.fromLTRB(4 * fem, 4, 4 * fem, 4),
          decoration: BoxDecoration(
            color: getBoxDecColor(subject.description),
            borderRadius: BorderRadius.circular(4),
          ),
          child: IconTheme(
            data: const IconThemeData(size: 10), // Adjust the icon size here
            child: Image.asset(subject.image, height: 28, width: 28),
          ),
        ),
        trailing: const IconTheme(
          data: IconThemeData(size: 12), // Adjust the icon size here
          child: Icon(Icons.arrow_forward_ios),
        ),
        title: Text(subject.description, style: kPracticeAppBarMenuItemTs),
        onTap: () {
          context
              .push('/flaggedQuestionList/${Uri.encodeComponent(subject.id)}');
        },
      ),
    );
  }
}
