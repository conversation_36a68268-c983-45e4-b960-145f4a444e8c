import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_flagged_questions.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_flagged_subjects.dart';

part 'flagged_questions_event.dart';
part 'flagged_questions_state.dart';

class FlaggedQuestionsBloc
    extends Bloc<FlaggedQuestionsEvent, FlaggedQuestionsState> {
  final FetchFlaggedSubjects _fetchFlaggedSubjects;
  final FetchFlaggedQuestions _fetchFlaggedQuestions;

  FlaggedQuestionsBloc({
    required FetchFlaggedQuestions fetchFlaggedQuestions,
    required FetchFlaggedSubjects fetchFlaggedSubjects,
  })  : _fetchFlaggedQuestions = fetchFlaggedQuestions,
        _fetchFlaggedSubjects = fetchFlaggedSubjects,
        super(FlaggedQuestionsState.empty()) {
    on<FetchFlaggedSubjectsEvent>(_populateFlaggedSubjectsEventHandler);
    on<FecthFlaggedQuestionsEvent>(_populateFlaggedQuestionsEventHandler);
  }

  FutureOr<void> _populateFlaggedSubjectsEventHandler(
      FetchFlaggedSubjectsEvent event,
      Emitter<FlaggedQuestionsState> emit) async {
    final result = await _fetchFlaggedSubjects();
    result.fold(
      (failure) => emit(FlaggedErrorState(errorMessage: failure.message)),
      (success) => emit(FlaggedSubjectsFetchedState(
          flaggedQuestions: List.empty(), flaggedSubjects: success)),
    );
  }

  FutureOr<void> _populateFlaggedQuestionsEventHandler(
      FecthFlaggedQuestionsEvent event,
      Emitter<FlaggedQuestionsState> emit) async {
    final result = await _fetchFlaggedQuestions(FetchFlaggedQuestionsParams(
        subjectId: event.subject, flagId: event.flagId));

    result.fold(
        (failure) => emit(FlaggedErrorState(
            errorMessage: "Flagged questions not available",
            flaggedSubjects: state.flaggedSubjects)),
        (success) => emit(FlaggedQuestionsFetchedState(
            flaggedSubjects: state.flaggedSubjects,
            flaggedQuestions: success)));
  }

  /*_populateFlaggedSubjects(FetchFlaggedSubjectsEvent event,
      Emitter<FlaggedQuestionsState> emit) async {
    List<SubjectModel>? subjectDataList =
        await _repository.fetchFlaggedSubjects();
    if (subjectDataList.isNotEmpty) {
      emit(FlaggedSubjectsFetchedState(
        flaggedQuestions: List.empty(),
        flaggedSubjects: convertList(subjectDataList, convertSubjectToModel),
      ));
    } else {
      emit(const FlaggedErrorState(
          errorMessage: "No flagged questions present"));
    }
  }*/

  /*_populateFlaggedQuestions(FecthFlaggedQuestionsEvent event,
      Emitter<FlaggedQuestionsState> emit) async {
    List<FlaggedQuestionModel> questionsData =
        await _repository.fetchFlaggedQuestionsUnderGivenSubject(
                event.subject, event.flagId) ??
            [];
    emit(questionsData.isNotEmpty
        ? FlaggedQuestionsFetchedState(
            flaggedSubjects: state.flaggedSubjects,
            flaggedQuestions:
                convertList(questionsData, convertFlaggedQuestionToModel),
          )
        : FlaggedErrorState(
            errorMessage: "Flagged questions not available",
            flaggedSubjects: state.flaggedSubjects));
  }*/
}
