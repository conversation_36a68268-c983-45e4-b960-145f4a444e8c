part of 'flagged_questions_bloc.dart';

class FlaggedQuestionsState extends Equatable {
  const FlaggedQuestionsState(
      {required this.flaggedQuestions, required this.flaggedSubjects});

  final List<Subject> flaggedSubjects;
  final List<FlaggedQuestion> flaggedQuestions;

  factory FlaggedQuestionsState.empty() {
    return const FlaggedQuestionsState(
        flaggedSubjects: [], flaggedQuestions: []);
  }

  FlaggedQuestionsState copyWith(
      {List<Subject>? flaggedSubjects,
      List<FlaggedQuestion>? flaggedQuestions}) {
    return FlaggedQuestionsState(
        flaggedQuestions: flaggedQuestions ?? this.flaggedQuestions,
        flaggedSubjects: flaggedSubjects ?? this.flaggedSubjects);
  }

  @override
  List<Object> get props => [flaggedSubjects, flaggedQuestions];
}

class FlaggedErrorState extends FlaggedQuestionsState {
  final String errorMessage;
  const FlaggedErrorState(
      {required this.errorMessage,
      super.flaggedQuestions = const [],
      super.flaggedSubjects = const []});
}

class FlaggedSubjectsFetchedState extends FlaggedQuestionsState {
  const FlaggedSubjectsFetchedState(
      {required super.flaggedQuestions, required super.flaggedSubjects});
}

class FlaggedQuestionsFetchedState extends FlaggedQuestionsState {
  const FlaggedQuestionsFetchedState(
      {required super.flaggedQuestions, required super.flaggedSubjects});
}
