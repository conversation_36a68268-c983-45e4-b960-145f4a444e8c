part of 'flagged_questions_bloc.dart';

sealed class FlaggedQuestionsEvent extends Equatable {
  const FlaggedQuestionsEvent();

  @override
  List<Object> get props => [];
}

class FetchFlaggedSubjectsEvent extends FlaggedQuestionsEvent {}

class FecthFlaggedQuestionsEvent extends FlaggedQuestionsEvent {
  final String subject;
  final String flagId;

  const FecthFlaggedQuestionsEvent(this.subject, this.flagId);
}
