import 'dart:convert';

import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';

class FlaggedQuestionModel extends FlaggedQuestion {
  const FlaggedQuestionModel({
    required super.questionId,
    required super.bundleId,
    required super.flagId,
    required super.shortDescriptionOrId,
  });

  @override
  String toString() {
    return "FlaggedQuestionModel[ id=$questionId and flagid=$flagId and bundleid=$bundleId and shortdescription=$shortDescriptionOrId]";
  }
}

class FlaggedSubjectModel extends Subject {
  const FlaggedSubjectModel({
    required super.id,
    required super.description,
    required super.image,
  });

  factory FlaggedSubjectModel.fromMap(DataMap map) {
    return FlaggedSubjectModel(
      id: map['id'] as String,
      description: map['description'] as String,
      image: map['image'] as String,
    );
  }

  factory FlaggedSubjectModel.fromJson(String source) =>
      FlaggedSubjectModel.fromMap(json.decode(source) as DataMap);

  @override
  List<Object?> get props => [id, description];
}
