import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/flag_question/data/datasources/flag_question_remote_datasources.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';

class FlaggedQuestionsRepoImpl implements FlaggedQuestionsRepo {
  const FlaggedQuestionsRepoImpl({
    required FlagQuestionRemoteDataSource remoteDataSource,
    required CacheContext cacheContext,
  })  : _remoteDataSource = remoteDataSource,
        _cacheContext = cacheContext;

  final FlagQuestionRemoteDataSource _remoteDataSource;
  final CacheContext _cacheContext;

  @override
  ResultFuture<List<FlaggedQuestion>> fetchFlaggedQuestionsUnderGivenSubject(
      String subjectId, String flagId) async {
    try {
      final result =
          await _remoteDataSource.fetchFlaggedQuestionsUnderGivenSubject(
              subjectId, flagId, _cacheContext.profileId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<List<Subject>> fetchFlaggedSubjects() async {
    try {
      final result = await _remoteDataSource.fetchFlaggedSubjects(
          _cacheContext.profileId, _cacheContext.getSubjectList());

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<FlaggedStatus> checkQuestionFlagged(
      String questionId, String subject) async {
    try {
      final result = await _remoteDataSource.checkIfQuestionIsFlagged(
          _cacheContext.profileId, questionId, subject);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<bool> flagQuestion(
      String questionId,
      String bundleId,
      String subjectId,
      FlaggedStatus selectedFlagId,
      String shortDescription) async {
    try {
      final result = await _remoteDataSource.flagQuestion(
          _cacheContext.profileId,
          questionId,
          bundleId,
          subjectId,
          selectedFlagId,
          shortDescription);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<bool> removeFlag(String questionId, String subject) async {
    try {
      final result = await _remoteDataSource.removeFlagForQuestion(
          _cacheContext.profileId, questionId, subject);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }
}
