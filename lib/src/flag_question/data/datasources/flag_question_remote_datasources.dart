import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/flag_question/data/models/flag_question.dart';

abstract class FlagQuestionRemoteDataSource {
  const FlagQuestionRemoteDataSource();

  Future<FlaggedStatus> checkIfQuestionIsFlagged(
      String profileId, String id, String subject);

  Future<List<Subject>> fetchFlaggedSubjects(
      String profileId, List<Subject> subjectList);

  Future<List<FlaggedQuestionModel>> fetchFlaggedQuestionsUnderGivenSubject(
      String subjectId, String flagId, String profileId);

  Future<bool> removeFlagForQuestion(
      String profileId, String questionId, String subjectId);

  Future<bool> flagQuestion(
      String profileId,
      String questionId,
      String bundleId,
      String subjectId,
      FlaggedStatus selectedFlagId,
      String shortDescription);
}

class FlagQuestionRemoteDataSourceImpl implements FlagQuestionRemoteDataSource {
  const FlagQuestionRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  final FirebaseFirestore _firestore;

  @override
  Future<List<FlaggedQuestionModel>> fetchFlaggedQuestionsUnderGivenSubject(
      String subjectId, String flagId, String profileId) async {
    CollectionReference questionsCollection = _firestore.collection(
        'flagged_questions/$profileId/subjects/$subjectId/questions');
    QuerySnapshot querySnapshot;
    if (flagId.isNotEmpty && flagId != 'all') {
      querySnapshot =
          await questionsCollection.where('flag_id', isEqualTo: flagId).get();
    } else {
      querySnapshot = await questionsCollection.get();
    }

    List<FlaggedQuestionModel> flaggedQuestions = querySnapshot.docs.map(
      (doc) {
        Map savedQuestionsMap = doc.data() as Map<String, dynamic>;

        return FlaggedQuestionModel(
          questionId: doc.id,
          bundleId: savedQuestionsMap['bundle_id'] as String,
          flagId: savedQuestionsMap['flag_id'] as String,
          shortDescriptionOrId: savedQuestionsMap['shortDescription'] ??
              doc.id, //TODO-Remove default value later
        );
      },
    ).toList();
    print(
        "AW:fetchFlaggedQuestionsUnderGivenSubject:flaggedQuestions list = $flaggedQuestions");
    return flaggedQuestions;
  }

  @override
  Future<List<Subject>> fetchFlaggedSubjects(
      String profileId, List<Subject> subjectList) async {
    try {
      CollectionReference subjectsCollection =
          _firestore.collection('flagged_questions/$profileId/subjects');
      QuerySnapshot querySnapshot = await subjectsCollection.get();
      List<Subject> completeSubjectList = subjectList;
      List<Subject> flaggedSubjects = querySnapshot.docs.map((doc) {
        //doc.data() as Map<String, dynamic>;
        print("AW:fetchFlaggedSubjects:doc.id = ${doc.id}");
        return completeSubjectList
            .firstWhere((element) => element.id == doc.id);
      }).toList();
      print("AW:fetchFlaggedSubjects:flaggedSubjects list = $flaggedSubjects");
      return flaggedSubjects;
    } on FirebaseException catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<bool> flagQuestion(
      String profileId,
      String questionId,
      String bundleId,
      String subjectId,
      FlaggedStatus selectedFlagId,
      String shortDescription) async {
    //print all input fields
    print(
        "AW:Inside flagQuestion:questionId= $questionId, bundleId= $bundleId, subjectId= $subjectId, selectedFlagId= $selectedFlagId, shortDescription= $shortDescription");

    CollectionReference flaggedQuestions =
        FirebaseFirestore.instance.collection('flagged_questions');

    DocumentSnapshot profileSnapshot =
        await flaggedQuestions.doc(profileId).get();
    if (!profileSnapshot.exists) {
      flaggedQuestions.doc(profileId).set({'defaultValue': ''});
    }

    CollectionReference subjectsCollection = FirebaseFirestore.instance
        .collection('flagged_questions/$profileId/subjects');

    DocumentSnapshot subjectsSnapshot =
        await subjectsCollection.doc(subjectId).get();
    if (!subjectsSnapshot.exists) {
      subjectsCollection.doc(subjectId).set({'defaultValue': ''});
    }

    FirebaseFirestore.instance
        .collection(
            'flagged_questions/$profileId/subjects/$subjectId/questions')
        .doc(questionId)
        .set({
      "bundle_id": bundleId,
      "flag_id": selectedFlagId.name,
      "shortDescription": shortDescription
    });

    return true;
  }

  @override
  Future<bool> removeFlagForQuestion(
      String profileId, String questionId, String subjectId) async {
    print("AW:Inside removeFlagForQuestion");
    CollectionReference questionsCollection = FirebaseFirestore.instance
        .collection(
            'flagged_questions/$profileId/subjects/$subjectId/questions');
    await questionsCollection.doc(questionId).delete();
    return true;
  }

  @override
  Future<FlaggedStatus> checkIfQuestionIsFlagged(
      String profileId, String id, String subject) async {
    DocumentSnapshot<Map<String, dynamic>> doc = await FirebaseFirestore
        .instance
        .collection('flagged_questions/$profileId/subjects/$subject/questions')
        .doc(id)
        .get();

    //if doc presents
    FlaggedStatus flaggedStatus = doc.exists
        ? parseFlaggedStatus(doc['flag_id'])
        : FlaggedStatus.notFlagged;
    print("AW::res= $flaggedStatus");
    return flaggedStatus;
  }
}
