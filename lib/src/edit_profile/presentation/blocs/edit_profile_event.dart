import 'package:equatable/equatable.dart';

abstract class EditPro<PERSON>leEvent extends Equatable {
  const EditProfileEvent();
}

class LoadUserProfileEvent extends EditProfileEvent {
  final String userEmail;

  const LoadUserProfileEvent({required this.userEmail});

  @override
  List<Object> get props => [userEmail];
}

class UpdateYearOfStudyEvent extends EditProfileEvent {
  final String newYear;
  final String userEmail;

  const UpdateYearOfStudyEvent({
    required this.newYear,
    required this.userEmail,
  });

  int get standardNumber {
    return int.tryParse(newYear.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
  }

  @override
  List<Object> get props => [newYear, userEmail];
}