abstract class EditProfileState {}

class EditProfileInitial extends EditProfileState {}

class EditProfileLoading extends EditProfileState {}

class EditProfileLoaded extends EditProfileState {
  final String name;
  final String email;
  final String yearOfStudy;
  final int standard;
  final int level;

  EditProfileLoaded({
    required this.name,
    required this.email,
    required this.yearOfStudy,
    required this.standard,
    required this.level,
  });
}

class EditProfileUpdating extends EditProfileState {}

class EditProfileUpdateSuccess extends EditProfileState {
  final String message;

  EditProfileUpdateSuccess({required this.message});
}

class EditProfileError extends EditProfileState {
  final String errorMessage;

  EditProfileError({required this.errorMessage});
}