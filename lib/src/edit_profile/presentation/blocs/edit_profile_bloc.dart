import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/fetch_profile.dart';
import '../../domain/usecases/update_profile.dart';
import 'edit_profile_event.dart';
import 'edit_profile_state.dart';

class EditProfileBloc extends Bloc<EditProfileEvent, EditProfileState> {
  final FetchProfile fetchProfile;
  final UpdateProfile updateProfile;

  EditProfileBloc({
    required this.fetchProfile,
    required this.updateProfile,
  }) : super(EditProfileInitial()) {
    on<LoadUserProfileEvent>(_onLoadUserProfile);
    on<UpdateYearOfStudyEvent>(_onUpdateYearOfStudy);
  }

  Future<void> _onLoadUserProfile(
      LoadUserProfileEvent event,
      Emitter<EditProfileState> emit,
      ) async {
    emit(EditProfileLoading());
    final result = await fetchProfile(
      FetchProfileParams(userEmail: event.userEmail),
    );

    result.fold(
          (failure) => emit(EditProfileError(errorMessage: failure.message)),
          (profileData) => emit(EditProfileLoaded(
        name: profileData.name,
        email: profileData.email,
        yearOfStudy: profileData.yearOfStudy,
        standard: profileData.standard,
        level: profileData.level,
      )),
    );
  }

  Future<void> _onUpdateYearOfStudy(
      UpdateYearOfStudyEvent event,
      Emitter<EditProfileState> emit,
      ) async {
    emit(EditProfileUpdating());

    final updateResult = await updateProfile(
      UpdateProfileParams(
        userEmail: event.userEmail,
        newYear: event.newYear,
      ),
    );

    await updateResult.fold(
          (failure) async {
        emit(EditProfileError(errorMessage: failure.message));
        add(LoadUserProfileEvent(userEmail: event.userEmail));
      },
          (_) async {
        emit(EditProfileUpdateSuccess(message: 'Profile updated successfully!'));

        final profileResult = await fetchProfile(
          FetchProfileParams(userEmail: event.userEmail),
        );

        profileResult.fold(
              (failure) => emit(EditProfileError(errorMessage: failure.message)),
              (profileData) => emit(EditProfileLoaded(
            name: profileData.name,
            email: profileData.email,
            yearOfStudy: profileData.yearOfStudy,
            standard: profileData.standard,
            level: profileData.level,
          )),
        );
      },
    );
  }
}