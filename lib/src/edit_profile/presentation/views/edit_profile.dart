import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:form_validator/form_validator.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/common/widgets/web/web_dailytest_banner.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';
import 'package:skillapp/src/edit_profile/presentation/blocs/edit_profile_bloc.dart';
import 'package:skillapp/src/edit_profile/presentation/blocs/edit_profile_event.dart';
import 'package:skillapp/src/edit_profile/presentation/blocs/edit_profile_state.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  factory EditProfile.routeBuilder(_, __) {
    return const EditProfile();
  }

  @override
  State<StatefulWidget> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  @override
  Widget build(BuildContext context) {
    return AdaptiveLayout(
        mobileBody: EditProfileMobile(),
        tabletBody: const EditProfileTablet(),
        desktopBody: EditProfileDesktop());
  }
}

class EditProfileMobile extends StatelessWidget {
  EditProfileMobile({super.key});

  final double coverHeight = 136;
  final double profileHeight = 88;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    //double baseWidth = 375;
    //double fem = MediaQuery.of(context).size.width / baseWidth;
    double fem = ScreenUtil.getFem(context);

    double ffem = fem * 0.97;
    final topPos = coverHeight - profileHeight / 2;

    return Scaffold(
      backgroundColor: kProfileSavedQuestionBgColor,
      // backgroundColor: Colors.white,
      appBar:
          const AppBarTemplate(appBarTitle: 'Edit Profile', actionType: 'NA'),
      body: Container(
        padding: EdgeInsets.fromLTRB(16 * fem, 16 * fem, 16 * fem, 0),
        child: Container(
          child: ListView(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: kProfileHeaderBarColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: kProfileHeaderBarColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        height: coverHeight,
                        padding: EdgeInsets.zero,
                        margin:
                            EdgeInsets.only(bottom: (profileHeight / 2) + 5),
                        child: buildTop(topPos, context, fem),
                      ),
                      Container(
                        alignment: Alignment.center,
                        margin: const EdgeInsets.all(0),
                        child: ProfileEditIconWidget(),
                      ),
                      SizedBox(
                        height: 24 * fem,
                      ),
                      ProfileNameWidget(nameController: nameController),
                      SizedBox(height: 22 * fem),
                      ProfileEmailWidget(emailController: emailController),
                      SizedBox(height: 22 * fem),
                      ProfileYearOfStudyWidget(),
                    ]),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomActionButtonsWidget(
        fem: fem,
        primaryCallBack: () {},
        primaryText: 'Save',
        secondaryCallBack: () {},
        secondaryText: 'Back',
        isDualActions: true,
      ),
    );
  }

  Stack buildTop(double topPos, BuildContext context, double fem) {
    // Widget buildTop(double topPos, BuildContext context) {

    //  return buildEditIcon(context);
    return Stack(
      clipBehavior: Clip.none,
      fit: StackFit.loose,
      alignment: Alignment.center,
      children: <Widget>[
        buildCoverImage(),
        Positioned(top: (topPos / 2) - 10, child: chooseImageSection()),
        Positioned(
            top: topPos, child: IgnorePointer(child: buildProfileImage())),
        /* Positioned(
            top: coverHeight,
            child: GestureDetector(
                onTap: () {
                  print('ProfileEditIconWidget tapped');
                },
                child: ProfileEditIconWidget())),*/
        // buildEditIcon(context, fem),
        /* Container(
          margin: EdgeInsets.only(top: coverHeight-20),
          child: buildEditIcon(context,fem)
        ),*/
        //   Positioned(top: coverHeight + 20, child: buildEditIcon(context,fem)),
      ],
    );
  }

  //Container buildEditIcon(BuildContext context) {

  Widget buildCoverImage() {
    return ImageDisplayWidget(
      imagePath: 'common/profileHeader.png',
      height: coverHeight,
      width: double.infinity,
      semanticLabelText: 'cover pic',
      isCoverImage: true,
    );
  }

  Widget buildProfileImage() => CircleAvatar(
      radius: profileHeight / 2,
      backgroundColor: Colors.white,
      child: CircleAvatar(
        backgroundImage: const AssetImage('assets/images/profilePic.png'),
        radius: (profileHeight / 2) - 3,
      ));

  Widget chooseImageSection() => Container(
      padding: const EdgeInsets.fromLTRB(20, 6, 20, 6),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white),
        borderRadius: BorderRadius.circular(40),
      ),
      child: const Text(
        'Choose image',
        style: TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          height: 0,
        ),
      ));
}

class ProfileYearOfStudyWidget extends StatelessWidget {
  ProfileYearOfStudyWidget({
    super.key,
  });

  final List<String> years = ['Year 3', 'Year 4', 'Year 5', 'Year 6'];

  // Helper method to convert standard number to year format
  String? _convertStandardToYear(dynamic standard) {
    if (standard == null) return null;

    int standardNum;
    if (standard is String) {
      standardNum = int.tryParse(standard) ?? 0;
    } else if (standard is int) {
      standardNum = standard;
    } else {
      return null;
    }

    final yearFormat = 'Year $standardNum';
    return years.contains(yearFormat) ? yearFormat : null;
  }

  // Helper method to convert year format to standard number
  int _convertYearToStandard(String year) {
    return int.parse(year.replaceAll('Year ', ''));
  }

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return BlocListener<EditProfileBloc, EditProfileState>(
      listener: (context, editProfileState) {
        // Show success popup when year is successfully updated
        if (editProfileState is EditProfileUpdateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(editProfileState.message),
                ],
              ),
              backgroundColor: kPrimaryColor,
              duration: const Duration(seconds: 3),
            ),
          );
        }

        // Show error popup if update fails
        if (editProfileState is EditProfileError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('Error: ${editProfileState.errorMessage}'),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: BlocBuilder<AppBloc, AppState>(
        builder: (context, appState) {
          return BlocBuilder<EditProfileBloc, EditProfileState>(
            builder: (context, editProfileState) {
              // Get current year from different state sources
              String? currentYear;

              // Priority 1: From EditProfileBloc if it has the yearOfStudy
              if (editProfileState is EditProfileLoaded) {
                currentYear = editProfileState.yearOfStudy;
              }
              // Priority 2: Convert from app state standard
              else if (appState.status == AppStatus.authenticated) {
                final standard = appState.userProfile.currentProfile.standard;
                currentYear = _convertStandardToYear(standard);
              }

              print('Current year determined: $currentYear');
              print('Available years: $years');
              print(
                  'App state standard: ${appState.status == AppStatus.authenticated ? appState.userProfile.currentProfile.standard : 'Not authenticated'}');

              // Show loading indicator if update is in progress
              final isUpdating = editProfileState is EditProfileUpdating;

              return Container(
                padding:
                    EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 0 * fem),
                height: 65 * fem,
                child: Stack(
                  children: [
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Year of study',
                        labelStyle: const TextStyle(
                          color: kPrimaryColor,
                          fontSize: 12,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          height: 0,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      hint: const Text("Select year of study"),
                      value: currentYear != null && years.contains(currentYear)
                          ? currentYear
                          : null,
                      iconEnabledColor: kSecondaryColor,
                      items: years.map((String year) {
                        return DropdownMenuItem<String>(
                          value: year,
                          child: Text(
                            year,
                            style: const TextStyle(
                              color: kPrimaryColor,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null &&
                            appState.status == AppStatus.authenticated) {
                          final user = appState.userProfile;

                          // Validate email before proceeding
                          if (user.email.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                    'User email not found. Please log in again.'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          // Convert year format to standard number
                          final newStandard = _convertYearToStandard(newValue);

                          print(
                              'Updating year from $currentYear to $newValue (standard: $newStandard) for user: ${user.email}');

                          context.read<EditProfileBloc>().add(
                                UpdateYearOfStudyEvent(
                                  newYear: newValue,
                                  userEmail: user.email,
                                ),
                              );
                        }
                      },
                    ),
                    // Show loading indicator overlay when updating
                    if (isUpdating)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    kPrimaryColor),
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class ProfileEmailWidget extends StatelessWidget {
  const ProfileEmailWidget({
    super.key,
    required this.emailController,
  });

  final TextEditingController emailController;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return BlocBuilder<AppBloc, AppState>(
      builder: (context, appState) {
        if (appState.status == AppStatus.authenticated) {
          emailController.text = appState.userProfile.email;
        }

        return Container(
          padding: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 0 * fem),
          child: TextFormField(
            enabled: false, // Make field non-editable
            keyboardType: TextInputType.name,
            controller: emailController,
            decoration: InputDecoration(
                filled: true,
                fillColor: Colors
                    .grey[100], // Light grey background for disabled field
                disabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: kSecondaryColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: kSecondaryColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                labelText: 'Email',
                labelStyle: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w400,
                  height: 0,
                )),
            style: const TextStyle(
              color: Colors.grey, // Grey text for disabled field
            ),
            validator: ValidationBuilder().email().maxLength(200).build(),
          ),
        );
      },
    );
  }
}

class ProfileNameWidget extends StatelessWidget {
  const ProfileNameWidget({
    super.key,
    required this.nameController,
  });

  final TextEditingController nameController;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return BlocBuilder<AppBloc, AppState>(
      builder: (context, appState) {
        // Populate name controller with user data
        if (appState.status == AppStatus.authenticated) {
          nameController.text = appState.userProfile.currentProfile.name;
        }

        return Container(
          padding: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 0 * fem),
          child: TextField(
            enabled: false, // Make field non-editable
            keyboardType: TextInputType.name,
            controller: nameController,
            decoration: InputDecoration(
                filled: true,
                fillColor: Colors
                    .grey[100], // Light grey background for disabled field
                disabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: kSecondaryColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: kSecondaryColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                labelText: 'Name',
                labelStyle: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w400,
                  height: 0,
                )),
            style: const TextStyle(
              color: Colors.grey, // Grey text for disabled field
            ),
          ),
        );
      },
    );
  }
}

class ProfileEditIconWidget extends StatelessWidget {
  ProfileEditIconWidget({
    super.key,
  });

  final List avatarList = [
    'avatar_1',
    'avatar_2',
    'avatar_3',
    'avatar_4',
    'avatar_5',
    'avatar_6',
  ];

  // final BuildContext context;
  // final double fem;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("Edit button is clicked");
        showModalBottomSheet<void>(
          showDragHandle: true,
          backgroundColor: Colors.white,
          elevation: 0.0,

          // isScrollControlled: true,
          context: context,
          builder: (BuildContext context) {
            return Container(
              decoration: const ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
              ),
              child: ListView(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.only(left: 25),
                        child: const Text(
                          'Select avatar',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xFF121212),
                            fontSize: 20,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                            height: 0,
                          ),
                        ),
                      ),
                      IconButton(
                          onPressed: () {
                            print("Delete icon pressed");

                            showModalBottomSheet<void>(
                              showDragHandle: true,
                              backgroundColor: Colors.white,
                              elevation: 0.0,

                              // isScrollControlled: true,
                              context: context,
                              builder: (BuildContext context) {
                                return Container(
                                  decoration: const ShapeDecoration(
                                    color: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(24),
                                        topRight: Radius.circular(24),
                                      ),
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      const Text(
                                        'Delete Profile picture',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: Color(0xFF121212),
                                          fontSize: 20,
                                          fontFamily: 'Poppins',
                                          fontWeight: FontWeight.w600,
                                          height: 0,
                                        ),
                                      ),
                                      const SizedBox(height: 28),
                                      const Expanded(
                                        child: Text(
                                          "Do you want to delete the profile picture?",
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: Color(0xFF050505),
                                            fontSize: 14,
                                            fontFamily: 'Poppins',
                                            fontWeight: FontWeight.w400,
                                            height: 0,
                                          ),
                                        ),
                                      ),
                                      Container(
                                        height: 70 * fem,
                                        margin:
                                            const EdgeInsets.only(bottom: 10),
                                        decoration: const ShapeDecoration(
                                          color: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(24),
                                              topRight: Radius.circular(24),
                                            ),
                                          ),
                                          shadows: [
                                            BoxShadow(
                                              color: Color(0x28000000),
                                              blurRadius: 24,
                                              offset: Offset(4, 0),
                                              spreadRadius: 0,
                                            )
                                          ],
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                //  backgroundColor: Colors.white,
                                                //  shadowColor: Colors.white,// Background color
                                                backgroundColor: Colors.white,
                                                elevation: 0.0,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(26),
                                                  //side: BorderSide.none
                                                ),
                                                fixedSize: Size(
                                                    161 * fem,
                                                    double
                                                        .infinity), // Width and height
                                              ),
                                              onPressed: () {
                                                Navigator.pop(context);
                                                Navigator.pop(context);
                                                //  Navigator.popUntil(context, (route) => !Navigator.of(context).canPop());
                                              },
                                              child: const Text('Back',
                                                  style: TextStyle(
                                                    color: kPrimaryColor,
                                                    fontSize: 14,
                                                    fontFamily: 'Poppins',
                                                    fontWeight: FontWeight.w500,
                                                    height: 0,
                                                  )),
                                            ),
                                            ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: const Color(
                                                    0xFF50409A), // Background color
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(26),
                                                ),
                                                fixedSize: Size(
                                                    161 * fem,
                                                    double
                                                        .infinity), // Width and height
                                              ),
                                              onPressed: () {
                                                // Action
                                              },
                                              child: const Text('Delete',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 14,
                                                    fontFamily: 'Poppins',
                                                    fontWeight: FontWeight.w500,
                                                    height: 0,
                                                  )),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                          icon: const Icon(Icons.delete)),
                    ],
                  ),
                  const SizedBox(height: 20),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3, // Set the number of columns
                      childAspectRatio: 1,
                    ),
                    itemCount: avatarList.length,
                    itemBuilder: (BuildContext context, int index) {
                      print("AW:Inside itemBuilder");
                      return Container(
                        width: 76,
                        height: 76,
                        padding: const EdgeInsets.only(bottom: 25),
                        /*decoration: BoxDecoration(
                          color: Color(0xFFD9D9D9),
                          borderRadius: BorderRadius.circular(50)
                        ),*/
                        decoration: BoxDecoration(
                          //color: Color(0xFFD9D9D9),
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: SvgPicture.asset(
                          'assets/images/Profile/avatars/${avatarList[index]}.svg',
                          height: 76,
                          width: 76,
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
      child: Container(
        width: 36,
        height: 36,
        // padding: EdgeInsets.only(top: coverHeight),
        decoration: const ShapeDecoration(
          color: Colors.white,
          shape: OvalBorder(),
          shadows: [
            BoxShadow(
              color: Color(0x28000000),
              blurRadius: 8,
              offset: Offset(0, 0),
              spreadRadius: 0,
            )
          ],
        ),
        child: Image.asset(
          "assets/images/edit_profileimage.png",
        ),
      ),
    );
  }
}

class EditProfileTablet extends StatelessWidget {
  const EditProfileTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return const EditProfile();
  }
}

class EditProfileDesktop extends StatelessWidget {
  EditProfileDesktop({super.key});

  final double coverHeight = 136;
  final double profileHeight = 88;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    final topPos = coverHeight - profileHeight / 2;

    return SingleChildScrollView(
        child: Container(
      padding: const EdgeInsets.all(40),
      // padding: EdgeInsets.fromLTRB(40 * fem, 40 * fem, 40 * fem, 40 * fem),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: kAllBgColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: kProfileHeaderBarColor,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          height: coverHeight,
                          padding: EdgeInsets.zero,
                          margin:
                              EdgeInsets.only(bottom: (profileHeight / 2) + 5),
                          child: buildTop(topPos, context, fem),
                        ),
                        Container(
                          alignment: Alignment.center,
                          margin: const EdgeInsets.all(0),
                          child: ProfileEditIconWidget(),
                        ),
                        SizedBox(
                          height: 24 * fem,
                        ),
                        ProfileNameWidget(nameController: nameController),
                        SizedBox(height: 22 * fem),
                        ProfileEmailWidget(emailController: emailController),
                        SizedBox(height: 22 * fem),
                        ProfileYearOfStudyWidget(),
                      ]),
                ),
              ],
            ),
          ),
          SizedBox(
            width: 20 * fem,
          ),
          const Expanded(
            child: Column(children: [WebDailyTestBanner()]),
          )
        ],
      ),
    ));
  }

  Stack buildTop(double topPos, BuildContext context, double fem) {
    // Widget buildTop(double topPos, BuildContext context) {

    //  return buildEditIcon(context);
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: <Widget>[
        buildCoverImage(),
        //commenting the choose image option to keep the header image intact
        //  Positioned(top: (topPos / 2) - 10, child: chooseImageSection()),
        Positioned(top: topPos, child: buildProfileImage()),
        // buildEditIcon(context, fem),
        /* Container(
          margin: EdgeInsets.only(top: coverHeight-20),
          child: buildEditIcon(context,fem)
        ),*/
        //   Positioned(top: coverHeight + 20, child: buildEditIcon(context,fem)),
      ],
    );
  }

  Widget buildCoverImage() {
    return Image.asset(
      'assets/images/profileHeader.png',
      width: double.infinity,
      height: coverHeight,
      fit: BoxFit.cover,
    );
/*
    return ImageDisplayWidget(
      imagePath: 'common/profileHeader.png',
      height: coverHeight,
      width: double.infinity,
      semanticLabelText: 'cover pic',
      isCoverImage: true,
    );*/
  }

  Widget buildProfileImage() => CircleAvatar(
      radius: profileHeight / 2,
      backgroundColor: Colors.white,
      child: CircleAvatar(
        backgroundImage: const AssetImage('assets/images/profilePic.png'),
        radius: (profileHeight / 2) - 3,
      ));

  Widget chooseImageSection() => Container(
      padding: const EdgeInsets.fromLTRB(20, 6, 20, 6),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white),
        borderRadius: BorderRadius.circular(40),
      ),
      child: const Text(
        'Choose image',
        style: TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          height: 0,
        ),
      ));
}
