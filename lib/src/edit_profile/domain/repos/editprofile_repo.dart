import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_profile.dart';

abstract class EditProfileRepository {
  Future<Either<Failure, UserProfile>> fetchUserProfile({
    required String userEmail,
  });

  Future<Either<Failure, bool>> updateYearOfStudy({
    required String userEmail,
    required String newYear,
  });

  Future<Either<Failure, int>> getLevelForStandard({
    required int standard,
  });
}