import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_profile.dart';
import '../repos/editprofile_repo.dart';

class FetchProfile {
  final EditProfileRepository repository;

  FetchProfile({required this.repository});

  Future<Either<Failure, UserProfile>> call(FetchProfileParams params) async {
    return await repository.fetchUserProfile(
      userEmail: params.userEmail,
    );
  }
}

class FetchProfileParams {
  final String userEmail;

  FetchProfileParams({
    required this.userEmail,
  });
}
