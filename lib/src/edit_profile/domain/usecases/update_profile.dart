import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repos/editprofile_repo.dart';

class UpdateProfile {
  final EditProfileRepository repository;

  UpdateProfile({required this.repository});

  Future<Either<Failure, bool>> call(UpdateProfileParams params) async {
    return await repository.updateYearOfStudy(
      userEmail: params.userEmail,
      newYear: params.newYear,
    );
  }
}

class UpdateProfileParams {
  final String userEmail;
  final String newYear;

  UpdateProfileParams({
    required this.userEmail,
    required this.newYear,
  });
}