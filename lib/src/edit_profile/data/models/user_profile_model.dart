import '../../domain/entities/user_profile.dart';

class UserProfileModel extends UserProfile {
  UserProfileModel({
    required super.name,
    required super.email,
    required super.yearOfStudy,
    required super.standard,
    required super.level,
  });

  factory UserProfileModel.fromFirestore(Map<String, dynamic> data) {
    return UserProfileModel(
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      yearOfStudy: _getYearFromStandard(data['standard'] ?? 3),
      standard: data['standard'] ?? 3,
      level: data['level'] ?? 1,
    );
  }

  /// Creates a default profile for a user with the given email
  factory UserProfileModel.createDefault({
    required String email,
    String name = 'User',
    int standard = 3,
    int level = 1,
  }) {
    return UserProfileModel(
      name: name,
      email: email,
      yearOfStudy: _getYearFromStandard(standard),
      standard: standard,
      level: level,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'email': email,
      'standard': standard,
      'level': level,
    };
  }

  static String _getYearFromStandard(int standard) {
    switch (standard) {
      case 3:
        return 'Year 3';
      case 4:
        return 'Year 4';
      case 5:
        return 'Year 5';
      case 6:
        return 'Year 6';
      default:
        return 'Year 3';
    }
  }

  static int getStandardFromYear(String year) {
    switch (year) {
      case 'Year 3':
        return 3;
      case 'Year 4':
        return 4;
      case 'Year 5':
        return 5;
      case 'Year 6':
        return 6;
      default:
        return 3;
    }
  }
}