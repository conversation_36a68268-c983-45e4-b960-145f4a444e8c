import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/user_profile.dart';
import '../../domain/repos/editprofile_repo.dart';
import '../datasources/editprofile_remote_data_source.dart';
import '../models/user_profile_model.dart';

class EditProfileRepositoryImpl implements EditProfileRepository {
  final EditProfileRemoteDataSource remoteDataSource;

  EditProfileRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, UserProfile>> fetchUserProfile({
    required String userEmail,
  }) async {
    try {
      final profile = await remoteDataSource.fetchUserProfile(
        userEmail: userEmail,
      );
      return Right(profile);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), statusCode: '500'));
    }
  }

  @override
  Future<Either<Failure, bool>> updateYearOfStudy({
    required String userEmail,
    required String newYear,
  }) async {
    try {
      if (userEmail.isEmpty) {
        return const Left(ServerFailure(
          message: 'User email cannot be empty',
          statusCode: '400',
        ));
      }

      if (newYear.isEmpty) {
        return const Left(ServerFailure(
          message: 'Year of study cannot be empty',
          statusCode: '400',
        ));
      }

      final newStandard = UserProfileModel.getStandardFromYear(newYear);

      final level = await remoteDataSource.getLevelForStandard(
        standard: newStandard,
      );

      final result = await remoteDataSource.updateUserProfile(
        userEmail: userEmail,
        newStandard: newStandard,
        newLevel: level,
      );

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), statusCode: '500'));
    }
  }

  @override
  Future<Either<Failure, int>> getLevelForStandard({
    required int standard,
  }) async {
    try {
      final level = await remoteDataSource.getLevelForStandard(
        standard: standard,
      );
      return Right(level);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), statusCode: '500'));
    }
  }
}