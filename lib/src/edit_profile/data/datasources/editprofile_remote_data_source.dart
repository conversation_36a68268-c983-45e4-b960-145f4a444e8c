import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../../core/errors/exceptions.dart';
import '../models/user_profile_model.dart';

abstract class EditProfileRemoteDataSource {
  Future<UserProfileModel> fetchUserProfile({
    required String userEmail,
  });

  Future<bool> updateUserProfile({
    required String userEmail,
    required int newStandard,
    required int newLevel,
  });

  Future<int> getLevelForStandard({required int standard});
}

class EditProfileRemoteDataSourceImpl implements EditProfileRemoteDataSource {
  final FirebaseFirestore firestore;

  EditProfileRemoteDataSourceImpl({required this.firestore});

  @override
  Future<UserProfileModel> fetchUserProfile({
    required String userEmail,
  }) async {
    try {
      if (userEmail.isEmpty) {
        throw const ServerException(
          message: 'User email cannot be empty',
          statusCode: '400',
        );
      }

      final profilesQuery = await firestore
          .collection('users')
          .doc(userEmail)
          .collection('profiles')
          .limit(1)
          .get();

      if (profilesQuery.docs.isEmpty) {
        // Create default profile if none exists
        return await _createDefaultProfile(userEmail);
      }

      final profileDoc = profilesQuery.docs.first;
      final data = profileDoc.data();

      return UserProfileModel.fromFirestore(data);
    } on FirebaseException catch (e) {
      throw ServerException(message: e.message ?? 'Firebase error', statusCode: e.code);
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<bool> updateUserProfile({
    required String userEmail,
    required int newStandard,
    required int newLevel,
  }) async {
    try {
      if (userEmail.isEmpty) {
        throw const ServerException(
          message: 'User email cannot be empty',
          statusCode: '400',
        );
      }

      final profilesQuery = await firestore
          .collection('users')
          .doc(userEmail)
          .collection('profiles')
          .limit(1)
          .get();

      DocumentReference profileRef;

      if (profilesQuery.docs.isEmpty) {
        // Create default profile first if none exists
        await _createDefaultProfile(userEmail);

        // Get the newly created profile
        final newProfilesQuery = await firestore
            .collection('users')
            .doc(userEmail)
            .collection('profiles')
            .limit(1)
            .get();

        profileRef = newProfilesQuery.docs.first.reference;
      } else {
        profileRef = profilesQuery.docs.first.reference;
      }

      await profileRef.update({
        'standard': newStandard,
        'level': newLevel,
      });

      return true;
    } on FirebaseException catch (e) {
      throw ServerException(message: e.message ?? 'Firebase error', statusCode: e.code);
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<int> getLevelForStandard({required int standard}) async {
    try {
      final doc = await firestore
          .collection('standards')
          .doc(standard.toString())
          .get();

      if (!doc.exists) {
        // Create default standard document if it doesn't exist
        await firestore
            .collection('standards')
            .doc(standard.toString())
            .set({'level': 1});
        return 1;
      }

      final data = doc.data();
      return data?['level'] ?? 1;
    } on FirebaseException catch (e) {
      throw ServerException(message: e.message ?? 'Firebase error', statusCode: e.code);
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  /// Creates a default profile for a user if none exists
  Future<UserProfileModel> _createDefaultProfile(String userEmail) async {
    try {
      // Ensure user document exists
      final userDoc = await firestore.collection('users').doc(userEmail).get();
      if (!userDoc.exists) {
        await firestore.collection('users').doc(userEmail).set({'defaultValue': ''});
      }

      // Get level for default standard (3)
      const defaultStandard = 3;
      final level = await getLevelForStandard(standard: defaultStandard);

      // Create default profile
      final defaultProfile = UserProfileModel.createDefault(
        email: userEmail,
        standard: defaultStandard,
        level: level,
      );

      // Save to Firestore
      await firestore
          .collection('users')
          .doc(userEmail)
          .collection('profiles')
          .add(defaultProfile.toFirestore());

      return defaultProfile;
    } on FirebaseException catch (e) {
      throw ServerException(message: e.message ?? 'Firebase error', statusCode: e.code);
    } catch (e) {
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }
}