import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/home/<USER>/blocs/home/<USER>';
import 'package:skillapp/src/home/<USER>/widgets/dailyPractice_gridItem.dart';

class DailyPraticeGridView extends StatefulWidget {
  const DailyPraticeGridView({super.key});

  @override
  State<DailyPraticeGridView> createState() => _DailyPraticeGridViewState();
}

class _DailyPraticeGridViewState extends State<DailyPraticeGridView> {
  @override
  void initState() {
    super.initState();
    context.read<HomeBloc>().add(FetchSubjectsEvent());
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 0 * fem, 16 * fem),
          child: Text(
            'Daily practice',
            style: SafeGoogleFont(
              'Poppins',
              fontSize: 16 * ffem,
              fontWeight: FontWeight.w500,
              height: 1.5 * ffem / fem,
              color: const Color(0xff121212),
            ),
          ),
        ),
        BlocBuilder<HomeBloc, HomeState>(
          //buildWhen: (prev, current) => current.subjects?.isNotEmpty ?? false,
          builder: (context, state) {
            return Builder(builder: (context) {
              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2, // Set the number of columns
                  childAspectRatio: 2.6,
                ),
                itemCount: state.subjects.length,
                itemBuilder: (BuildContext context, int index) {
                  return DailyPracticeGridItem(
                    image: state.subjects[index].image,
                    name: state.subjects[index].description,
                    subjectId: state.subjects[index].id,
                  );
                },
              );
            });
          },
        ),
      ],
    );
  }
}
