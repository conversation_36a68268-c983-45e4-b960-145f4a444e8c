import 'package:flutter/widgets.dart';
import 'package:skillapp/core/configs/configs.dart';

// ignore: camel_case_types
class TestScroll extends StatelessWidget {
  const TestScroll({super.key});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.fromLTRB(17 * fem, 0 * fem, 0 * fem, 16 * fem),
          child: Text(
            'Test',
            style: SafeGoogleFont(
              'Poppins',
              fontSize: 16 * ffem,
              fontWeight: FontWeight.w500,
              height: 1.5 * ffem / fem,
              color: const Color(0xff121212),
            ),
          ),
        ),
        Container(
          // autogroup5q6r8fw (4mb8DeXshTapS7J6FE5Q6R)
          margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 15 * fem, 6 * fem),
          width: double.infinity,
          height: 140 * fem,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin:
                    EdgeInsets.fromLTRB(0 * fem, 0 * fem, 20 * fem, 0 * fem),
                padding:
                    EdgeInsets.fromLTRB(8 * fem, 12 * fem, 8 * fem, 8 * fem),
                width: 162 * fem,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: const Color(0xffd8ddff),
                  borderRadius: BorderRadius.circular(16 * fem),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      // sciencei8M (2:47)
                      margin: EdgeInsets.fromLTRB(
                          0 * fem, 0 * fem, 90 * fem, 0 * fem),
                      child: Text(
                        'Science',
                        style: SafeGoogleFont(
                          'Poppins',
                          fontSize: 12 * ffem,
                          fontWeight: FontWeight.w500,
                          height: 1.5 * ffem / fem,
                          color: const Color(0xff3c489b),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 7 * fem,
                    ),
                    Container(
                      // autogroupsgxwCZK (4mb8kiUSYd4Sk6tHbBSGXw)

                      margin: EdgeInsets.fromLTRB(
                          0 * fem, 0 * fem, 4 * fem, 0 * fem),
                      child: Stack(
                        children: [
                          Image.asset(
                            'assets/images/dashboard/Ellipse_63.png',
                            width: 56 * fem,
                            height: 56 * fem,
                          ),
                          Positioned(
                            top: 16 *fem,
                            left: 16 *fem,
                            right: 16 *fem,
                            bottom: 16 *fem,
                            child: Image.asset(
                              'assets/images/dashboard/Flask.png',
                              width: 24 *fem,
                              height: 24 *fem,
                            ),
                          ),
                        ],
                      ),

                      /*  child: Image.asset(
                        'assets/images/dashboard/auto-group-sgxw.png',
                        width: 56 * fem,
                        height: 56 * fem,
                      ),*/
                    ),
                    SizedBox(
                      height: 7 * fem,
                    ),
                    Container(
                      // group69tBF (2:49)
                      width: double.infinity,
                      height: 32 * fem,
                      decoration: BoxDecoration(
                        color: const Color(0xff50409a),
                        borderRadius: BorderRadius.circular(16 * fem),
                      ),
                      child: Center(
                        child: Text(
                          'Start',
                          textAlign: TextAlign.center,
                          style: SafeGoogleFont(
                            'Poppins',
                            fontSize: 14 * ffem,
                            fontWeight: FontWeight.w500,
                            height: 1.5 * ffem / fem,
                            color: const Color(0xffffffff),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                // autogroupnxyfM4q (4mb8yHwpPuxXLSd3zLnXyf)
                padding:
                    EdgeInsets.fromLTRB(8 * fem, 12 * fem, 8 * fem, 8 * fem),
                width: 162 * fem,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: const Color(0xffffd7d7),
                  borderRadius: BorderRadius.circular(16 * fem),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      // history2Ay (2:48)
                      margin: EdgeInsets.fromLTRB(
                          0 * fem, 0 * fem, 96 * fem, 0 * fem),
                      child: Text(
                        'History',
                        style: SafeGoogleFont(
                          'Poppins',
                          fontSize: 12 * ffem,
                          fontWeight: FontWeight.w500,
                          height: 1.5 * ffem / fem,
                          color: const Color(0xff994141),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 7 * fem,
                    ),
                    SizedBox(
                      // autogroupj3x1JeH (4mb9784753nh7FUimVJ3x1)
                      width: 56 * fem,
                      height: 56 * fem,
                      child: Stack(
                       children: [
                      Image.asset(
                        'assets/images/dashboard/Ellipse_66.png',
                        width: 56 * fem,
                        height: 56 * fem,
                      ),
                      Positioned(
                        top: 16 *fem,
                            left: 16 *fem,
                            right: 16 *fem,
                            bottom: 16 *fem,
                            child: Image.asset(
                              'assets/images/dashboard/Group_91.png',
                              width: 24 *fem,
                              height: 24 *fem,
                            ), 
                      )

                       ],
                      ), 
                     
                    ),
                    SizedBox(
                      height: 7 * fem,
                    ),
                    Container(
                      // group93QBX (2:44)
                      width: double.infinity,
                      height: 32 * fem,
                      decoration: BoxDecoration(
                        color: const Color(0xff50409a),
                        borderRadius: BorderRadius.circular(16 * fem),
                      ),
                      child: Center(
                        child: Text(
                          'Start',
                          textAlign: TextAlign.center,
                          style: SafeGoogleFont(
                            'Poppins',
                            fontSize: 14 * ffem,
                            fontWeight: FontWeight.w500,
                            height: 1.5 * ffem / fem,
                            color: const Color(0xffffffff),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
