import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';

class UserNameWidget extends StatefulWidget {
  const UserNameWidget({super.key});

  @override
  State<UserNameWidget> createState() => _UserNameWidgetState();
}

class _UserNameWidgetState extends State<UserNameWidget> {
  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 0 * fem, 16 * fem),
      constraints: BoxConstraints(
        maxWidth: 209 * fem,
      ),
      child: BlocBuilder<AppBloc, AppState>(builder: (context, state) {
        return Builder(builder: (context) {
          return RichText(
            text: TextSpan(
              style: SafeGoogleFont(
                'Poppins',
                fontSize: 20 * ffem,
                fontWeight: FontWeight.w400,
                height: 1.5 * ffem / fem,
                color: const Color(0xff121212),
              ),
              children: [
                const TextSpan(
                  text: 'Hello\n',
                ),
                TextSpan(
                  text: state
                      .userProfile.currentProfile.name, //'Benjamin Fernandez',
                  style: SafeGoogleFont(
                    'Poppins',
                    fontSize: 20 * ffem,
                    fontWeight: FontWeight.w600,
                    height: 1.5 * ffem / fem,
                    color: const Color(0xff121212),
                  ),
                ),
              ],
            ),
          );
        });
      }),
      /* child: RichText(
        text: TextSpan(
          style: SafeGoogleFont(
            'Poppins',
            fontSize: 20 * ffem,
            fontWeight: FontWeight.w400,
            height: 1.5 * ffem / fem,
            color: const Color(0xff121212),
          ),
          children: [
            const TextSpan(
              text: 'Hello\n',
            ),
            TextSpan(
              text: 'Benjamin Fernandez',
              style: SafeGoogleFont(
                'Poppins',
                fontSize: 20 * ffem,
                fontWeight: FontWeight.w600,
                height: 1.5 * ffem / fem,
                color: const Color(0xff121212),
              ),
            ),
          ],
        ),
      ), */
    );
  }
}
