import 'package:flutter/material.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';

class LearningImageIconWidget extends StatelessWidget {
  const LearningImageIconWidget({super.key});

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    double hfem = ScreenUtil.getHfem(context);

    return Container(
      margin: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 16 * fem, 0 * fem),
      padding: EdgeInsets.fromLTRB(4 * fem, 4 * fem, 4 * fem, 4 * fem),
      width: 56 * fem,
      height: 56 * fem,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(56 * fem),
        gradient: const LinearGradient(
          begin: Alignment(0.896, 0.667),
          end: Alignment(-1.125, 0.875),
          colors: <Color>[Color(0xbaffffff), Color(0xbaffffff)],
          stops: <double>[0, 1],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x3f000000),
            offset: Offset(0 * fem, 0 * fem),
            blurRadius: 2 * fem,
          ),
        ],
      ),
      child: Center(
        // ellipse11Z8R (2:15)
        child: SizedBox(
          width: double.infinity,
          height: 40 * fem,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20 * fem),
              image: const DecorationImage(
                image: AssetImage(
                  'assets/images/dashboard/ellipse-11-bg.png',
                  //'assets/images/dashboard/batman_face.png',
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
