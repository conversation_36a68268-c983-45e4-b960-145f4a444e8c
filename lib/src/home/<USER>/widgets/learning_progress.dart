import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';

class LearningProgressWidget extends StatefulWidget {
  const LearningProgressWidget({super.key});

  @override
  State<LearningProgressWidget> createState() => _LearningProgressState();
}

class _LearningProgressState extends State<LearningProgressWidget> {
  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 16 * fem),
      padding: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 20 * fem, 16 * fem),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32 * fem),
        gradient: const LinearGradient(
          begin: Alignment(-0.81, -0.524),
          end: Alignment(1.359, 1.482),
          colors: <Color>[Color(0xff50409a), Color(0xff964ec2)],
          stops: <double>[0, 1],
        ),
      ),
      child: ClipRect(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 11 * fem, 12 * fem),
              width: double.infinity,
              height: 64 * fem,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.fromLTRB(
                        0 * fem, 16 * fem, 61 * fem, 0 * fem),
                    height: double.infinity,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        LearningImageIconWidget(fem),
                        LearningBadgeNameWidget(fem, ffem),
                      ],
                    ),
                  ),
                  LearnigBadgeWidget(fem),
                ],
              ),
            ),
            Container(
              // yourlearnings5uP (2:10)
              margin: EdgeInsets.fromLTRB(4 * fem, 0 * fem, 0 * fem, 2 * fem),
              child: Text(
                'Your Learnings',
                style: SafeGoogleFont(
                  'Poppins',
                  fontSize: 12 * ffem,
                  fontWeight: FontWeight.w500,
                  height: 1.5 * ffem / fem,
                  color: const Color(0xffffffff),
                ),
              ),
            ),
            Container(
              // loremipsumdolorsitametconsecte (2:11)
              margin: EdgeInsets.fromLTRB(4 * fem, 0 * fem, 0 * fem, 6 * fem),
              constraints: BoxConstraints(
                maxWidth: 259 * fem,
              ),
              child: Text(
                'Lorem ipsum dolor sit amet, consectetur adipiscing \nelit, sed dotempor incididunt ut labo',
                style: SafeGoogleFont(
                  'Poppins',
                  fontSize: 10 * ffem,
                  fontWeight: FontWeight.w400,
                  height: 1.5 * ffem / fem,
                  color: const Color(0xffd5d5d5),
                ),
              ),
            ),
            LearningProgressBarWidget(fem, ffem),
          ],
        ),
      ),
    );
  }

  Container LearningProgressBarWidget(double fem, double ffem) {
    return Container(
            // autogroupmv7pr2y (4mb6jMgKi4DbkPz5qXMv7P)
            margin: EdgeInsets.fromLTRB(4 * fem, 0 * fem, 0 * fem, 0 * fem),
            width: double.infinity,
            height: 18 * fem,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  // autogroupm4dbNX7 (4mb6q2BZ1G426UGGcXM4Db)
                  margin: EdgeInsets.fromLTRB(
                      0 * fem, 5 * fem, 12 * fem, 5 * fem),
                  width: 266 * fem,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: const Color(0xff2c1e59),
                    borderRadius: BorderRadius.circular(6 * fem),
                  ),
                  child: Align(
                    // rectangle396CD (2:37)
                    alignment: Alignment.centerLeft,
                    child: SizedBox(
                      width: 172 * fem,
                      height: 8 * fem,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6 * fem),
                          color: const Color(0xff39e94b),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x3f000000),
                              offset: Offset(0 * fem, 0 * fem),
                              blurRadius: 3 * fem,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Text(
                  // b8y (2:12)
                  '60%',
                  style: SafeGoogleFont(
                    'Poppins',
                    fontSize: 12 * ffem,
                    fontWeight: FontWeight.w500,
                    height: 1.5 * ffem / fem,
                    color: const Color(0xffffffff),
                  ),
                ),
              ],
            ),
          );
  }

  Container LearnigBadgeWidget(double fem) {
    return Container(
                  // autogroupl9iujh7 (4mb6MHUmWTBhWzV8CLL9iu)
                  padding: EdgeInsets.fromLTRB(
                      10.5 * fem, 15.5 * fem, 9.5 * fem, 13.5 * fem),
                  height: double.infinity,
                  decoration: BoxDecoration(
                    border: const Border(),
                    gradient: const LinearGradient(
                      begin: Alignment(0, -1),
                      end: Alignment(0, 1),
                      colors: <Color>[Color(0x8cffffff), Color(0x8cffffff)],
                      stops: <double>[0, 1],
                    ),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(8 * fem),
                      bottomLeft: Radius.circular(8 * fem),
                    ),
                  ),
                  child: Center(
                    // awardstarfill0wght400grad0opsz (2:39)
                    child: SizedBox(
                      width: 35 * fem,
                      height: 35 * fem,
                      child: Image.asset(
                        'assets/images/dashboard/awardstarfill0wght400grad0opsz24-1.png',
                        width: 35 * fem,
                        height: 35 * fem,
                      ),
                    ),
                  ),
                );
  }

  Container LearningBadgeNameWidget(double fem, double ffem) {
    return Container(
                        // knightwarriorFG9 (2:9)
                        margin: EdgeInsets.fromLTRB(
                            0 * fem, 0 * fem, 0 * fem, 11 * fem),
                        child: Text(
                          'Knight warrior',
                          style: SafeGoogleFont(
                            'Poppins',
                            fontSize: 16 * ffem,
                            fontWeight: FontWeight.w600,
                            height: 1.5 * ffem / fem,
                            color: const Color(0xffffffff),
                          ),
                        ),
                      );
  }

  Container LearningImageIconWidget(double fem) {
    return Container(
                      margin: EdgeInsets.fromLTRB(
                            0 * fem, 0 * fem, 16 * fem, 0 * fem),
                        padding: EdgeInsets.fromLTRB(
                            4 * fem, 4 * fem, 4 * fem, 4 * fem),
                        width: 48 * fem,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24 * fem),
                          gradient: const LinearGradient(
                            begin: Alignment(0.896, 0.667),
                            end: Alignment(-1.125, 0.875),
                            colors: <Color>[
                              Color(0xbaffffff),
                              Color(0xbaffffff)
                            ],
                            stops: <double>[0, 1],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0x3f000000),
                              offset: Offset(0 * fem, 0 * fem),
                              blurRadius: 2 * fem,
                            ),
                          ],
                        ),
                        child: Center(
                          // ellipse11Z8R (2:15)
                          child: SizedBox(
                            width: double.infinity,
                            height: 40 * fem,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20 * fem),
                                image: const DecorationImage(
                                  image: AssetImage(
                                    'assets/images/f/ellipse-11-bg.png',
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
  }
}
