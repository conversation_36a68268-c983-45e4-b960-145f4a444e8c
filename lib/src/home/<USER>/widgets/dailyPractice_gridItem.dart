import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';

class DailyPracticeGridItem extends StatelessWidget {
  final String image;
  final String name;
  final String subjectId;

  const DailyPracticeGridItem(
      {super.key,
      required this.image,
      required this.name,
      required this.subjectId});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    return GestureDetector(
      onTap: () {
        // Handle button press event here
        context.push('/practice/${Uri.encodeComponent(subjectId)}');
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 15 * fem, 0 * fem),
            padding: EdgeInsets.fromLTRB(6 * fem, 6 * fem, 12 * fem, 6 * fem),
            height: 52 * fem,
            decoration: BoxDecoration(
              color: const Color(0x1650409a),
              borderRadius: BorderRadius.circular(8 * fem),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin:
                      EdgeInsets.fromLTRB(0 * fem, 0 * fem, 8 * fem, 0 * fem),
                  padding:
                      EdgeInsets.fromLTRB(4 * fem, 4 * fem, 4 * fem, 4 * fem),
                  decoration: BoxDecoration(
                    color: getBoxDecColor(name),
                    borderRadius: BorderRadius.circular(4 * fem),
                  ),
                  child: Center(
                    child: SizedBox(
                      width: 28 * fem,
                      height: 28 * fem,
                      child: Image.asset(
                        image,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    child: Text(
                      name,
                      style: SafeGoogleFont(
                        'Poppins',
                        fontSize: 12 * ffem,
                        fontWeight: FontWeight.w400,
                        height: 1.5 * ffem / fem,
                        color: const Color(0xff121212),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
