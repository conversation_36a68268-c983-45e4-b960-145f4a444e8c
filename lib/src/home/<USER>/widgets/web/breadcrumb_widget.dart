import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';

class BreadcrumbItem {
  final String label;
  final String route;
  final bool isActive;

  BreadcrumbItem({
    required this.label,
    required this.route,
    this.isActive = false,
  });
}

class BreadcrumbWidget extends StatelessWidget {
  final List<BreadcrumbItem> items;

  const BreadcrumbWidget({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    // If there's only one item (Home), show it with a special style
    if (items.length == 1 && items[0].label == 'Home' && items[0].isActive) {
      return Row(
        children: [
          Text(
            items[0].label,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        // Back button
        if (items.length > 1)
          IconButton(
            icon: const Icon(Icons.arrow_back, size: 20),
            onPressed: () {
              // Navigate to the parent route (second-to-last item)
              final parentItem = items[items.length - 2];
              context.go(parentItem.route);
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            splashRadius: 20,
            color: kPrimaryColor,
          ),

        // Breadcrumb items
        ...List.generate(items.length, (i) {
          final item = items[i];

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Add separator between items if not the first item
              if (i > 0) ...[
                const SizedBox(width: 8),
                const Text(
                  '/',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 8),
              ],

              // Breadcrumb item
              if (item.isActive)
                // Current page (not clickable)
                Text(
                  item.label,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                )
              else
                // Clickable link
                InkWell(
                  onTap: () => context.go(item.route),
                  child: Text(
                    item.label,
                    style: const TextStyle(
                      color: kPrimaryColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
            ],
          );
        }),
      ],
    );
  }
}
