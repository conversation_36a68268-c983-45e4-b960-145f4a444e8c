import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/breadcrumb_widget.dart';

class WebHeaderWidget extends StatefulWidget {
  final String headerTitle;
  final List<BreadcrumbItem>? breadcrumbItems;

  const WebHeaderWidget({
    super.key,
    required this.headerTitle,
    this.breadcrumbItems,
  });

  @override
  State<StatefulWidget> createState() => _WebHeaderWidgetState();
}

class _WebHeaderWidgetState extends State<WebHeaderWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AppBloc, AppState>(
      bloc: sl<AppBloc>(),
      listener: (context, state) {
        if (state.status == AppStatus.unauthenticated) {
          context.go('/');
        }
      },
      child: Container(
        width: double.infinity,
        height: 80,
        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color(0x0A000000),
              blurRadius: 24,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Left side: Breadcrumb or Title
            widget.breadcrumbItems != null && widget.breadcrumbItems!.isNotEmpty
                ? BreadcrumbWidget(items: widget.breadcrumbItems!)
                : Text(widget.headerTitle, style: kHeaderTextTs),

            // Right side: Notifications and Logout
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.accessibility_new_outlined),
                  tooltip: 'test new feature',
                  onPressed: () {
                    print("ltest new feature");
                    context.push("/testscreen");
                  },
                ),
                const Icon(Icons.notifications_none_outlined),
                const SizedBox(
                  width: 32,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.fromLTRB(24, 12, 24, 12),
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                      side: const BorderSide(width: 1, color: kPrimaryColor),
                    ),
                  ),
                  onPressed: () {
                    sl<AppBloc>().add(AppLogoutRequested());
                  },
                  child: const Text('Logout', style: kHeaderButtonTs),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
