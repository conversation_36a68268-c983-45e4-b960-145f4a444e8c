import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:skillapp/core/configs/configs.dart';

class WebDailyPracticeGridItem extends StatelessWidget {
  final String image;
  final String name;
  final String subjectId;

  const WebDailyPracticeGridItem(
      {super.key,
      required this.image,
      required this.name,
      required this.subjectId});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Handle button press event here
        context.push('/practice/${Uri.encodeComponent(subjectId)}');
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            // margin: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 40 * fem, 0 * fem),
            padding: const EdgeInsets.fromLTRB(6, 6, 12, 6),
            height: 68, // Fixed height
            decoration: BoxDecoration(
              color: const Color(0x1650409a),
              // borderRadius: BorderRadius.circular(12 * fem),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: getBoxDecColor(name),
                    //  borderRadius: BorderRadius.circular(4 * fem),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(
                    child: SizedBox(
                      //  width: 44 * fem,
                      width: 44,
                      height: 44,
                      child: Image.asset(
                        image,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    name,
                    style: SafeGoogleFont(
                      'Poppins',
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      height: 1.5,
                      color: const Color(0xff121212),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
