import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/home/<USER>/blocs/home/<USER>';

import 'package:skillapp/src/home/<USER>/widgets/web/web_daily_pratice_griditem.dart';

class WebDailyPraticeGridview extends StatefulWidget {
  const WebDailyPraticeGridview({super.key});

  @override
  State<StatefulWidget> createState() => _WebDailyPraticeGridviewState();
}

class _WebDailyPraticeGridviewState extends State<WebDailyPraticeGridview> {
  @override
  void initState() {
    super.initState();
    // Load subjects when widget initializes
    context.read<HomeBloc>().add(FetchSubjectsEvent());
  }

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return SizedBox(
      height: 340, // Fixed height to match LearningInfo widget

      // padding: EdgeInsets.all(24 * fem),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(0 * fem, 0, 0, 16 * fem),
            child: const Text('Daily practice',
                style: kWebSubjectGridViewHeadingTs),
          ),
          const SizedBox(
            height: 40,
          ),
          Expanded(
            child: BlocBuilder<HomeBloc, HomeState>(
              //buildWhen: (prev, current) => current.subjects?.isNotEmpty ?? false,
              builder: (context, state) {
                // Build grid with available subjects
                return Builder(builder: (context) {
                  // Using a more predictable layout approach with fixed height
                  return GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2, // Set the number of columns
                      // Using a fixed height approach instead of aspect ratio
                      mainAxisExtent: 68, // Fixed height for each item
                      crossAxisSpacing: 20,
                      mainAxisSpacing: 60,
                    ),
                    itemCount: state.subjects.length,
                    itemBuilder: (BuildContext context, int index) {
                      return WebDailyPracticeGridItem(
                        image: state.subjects[index].image,
                        name: state.subjects[index].description,
                        subjectId: state.subjects[index].id,
                      );
                    },
                  );
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
