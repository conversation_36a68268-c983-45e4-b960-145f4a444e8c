import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'dart:math' as math;

class WebDailyGoalProgress extends StatelessWidget {
  const WebDailyGoalProgress({
    super.key,
    required this.completedPercent,
    required this.percentToShow,
  });

  final double completedPercent;
  final int percentToShow;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return Container(
      //  height: MediaQuery.of(context).size.height * 0.075,
      height: 75,
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(16 * fem, 0, 16 * fem, 0),
      decoration: ShapeDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Colors.white.withOpacity(0.25),
            Colors.white.withOpacity(0.1),
          ],
          stops: const [0.0211, 1.0981],
        ),
        shape: RoundedRectangleBorder(
          side: const BorderSide(width: 1, color: Colors.white),
          borderRadius: BorderRadius.circular(20 * fem),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(16 * fem, 10, 16 * fem, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Daily goals',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 0,
                  ),
                ),
                const SizedBox(
                  // height: 10 * hfem,
                  height: 10,
                ),
                LinearPercentIndicator(
                  padding: EdgeInsets.all(0 * fem),
                  // width: 140 * fem,
                  // width: MediaQuery.of(context).size.width * 0.18,
                  width:
                      math.min(MediaQuery.of(context).size.width * 0.17, 400),
                  //   lineHeight: 10 * hfem,
                  lineHeight: 10,
                  percent: completedPercent,
                  barRadius: Radius.circular(16 * fem),
                  backgroundColor: kPrimaryColor,
                  progressColor: kAppBarProgressColor,
                  animation: true,
                  animationDuration: 1000,
                  trailing: Padding(
                    padding:
                        EdgeInsets.fromLTRB(8 * fem, 0 * fem, 0 * fem, 0 * fem),
                    child: Text(
                      '$percentToShow%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 0,
                      ),
                    ),
                  ),
                  linearStrokeCap: LinearStrokeCap.butt,
                  curve: Curves.easeInCirc,
                ),
              ],
            ),
          ),
          /* SizedBox(
           // width: 25 * fem,

          ),*/
          GestureDetector(
            onTap: () {
              print('completed pressed');
            },
            child: Container(
              //  padding:EdgeInsets.fromLTRB(19 * fem, 5 * hfem, 19 * fem, 5 * hfem),
              padding: EdgeInsets.fromLTRB(19 * fem, 5, 19 * fem, 5),

              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  side: const BorderSide(width: 1, color: Color(0xFF50409A)),
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text('Complete',
                  style: TextStyle(
                    color: Color(0xFF50409A),
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    height: 0,
                  )),
            ),
          )
        ],
      ),
    );
  }
}
