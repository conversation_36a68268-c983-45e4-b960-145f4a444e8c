import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';

class EnhancedNavigationRail extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onDestinationSelected;

  const EnhancedNavigationRail({
    super.key,
    required this.selectedIndex,
    required this.onDestinationSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Logo at the top
          _buildLogoSection(),

          // User profile section
          _buildUserProfileSection(context),

          // Divider below user profile
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.0),
            child: Divider(height: 1, thickness: 1),
          ),

          // Navigation items
          const SizedBox(height: 16),
          _buildNavigationItem(0, 'Home', Icons.home, Icons.home_filled),
          /* _buildNavigationItem(
              1, 'Learning', Icons.menu_book_outlined, Icons.menu_book),
          _buildNavigationItem(
              2, 'Leaderboard', Icons.leaderboard_outlined, Icons.leaderboard),*/
          _buildNavigationItem(
              3, 'Past Tests', Icons.assignment_outlined, Icons.assignment),
          _buildNavigationItem(4, 'Subscription',
              Icons.card_membership_outlined, Icons.card_membership),
          _buildNavigationItem(
              5, 'Profile & Settings', Icons.settings_outlined, Icons.settings),
          //  _buildNavigationItem(6, 'Quiz Demo', Icons.quiz_outlined, Icons.quiz),
        ],
      ),
    );
  }

  Widget _buildLogoSection() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 20, 24, 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 60,
                height: 50,
                child: SvgPicture.asset(
                  'assets/images/logo/logo.svg',
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                "Selective Ninja",
                style: TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: kPrimaryColor,
                ),
              ),
            ],
          ),
        ),
        // Divider below logo section
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 0),
          child: Divider(height: 1, thickness: 1),
        ),
      ],
    );
  }

  Widget _buildUserProfileSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: BlocBuilder<AppBloc, AppState>(
        builder: (context, state) {
          return Column(
            children: [
              // User avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 3),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const CircleAvatar(
                  backgroundImage: AssetImage('assets/images/profilePic.png'),
                  radius: 40,
                ),
              ),
              const SizedBox(height: 12),
              // User name
              Text(
                state.userProfile.currentProfile.name.isNotEmpty
                    ? state.userProfile.currentProfile.name
                    : "",
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: kPrimaryTextColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNavigationItem(
      int index, String label, IconData icon, IconData selectedIcon) {
    final bool isSelected = selectedIndex == index;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: InkWell(
        onTap: () => onDestinationSelected(index),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: double.infinity, // Full width
          height: 56, // Fixed height for the navigation item
          decoration: BoxDecoration(
            color: isSelected ? kPrimaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Icon(
                isSelected ? selectedIcon : icon,
                color: isSelected ? Colors.white : kNavigationMenuTextColor,
                size: 24,
              ),
              const SizedBox(width: 16),
              Text(
                label,
                style: isSelected
                    ? const TextStyle(
                        fontFamily: 'Poppins',
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      )
                    : knavigationMenuTextNotSelectedTs,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
