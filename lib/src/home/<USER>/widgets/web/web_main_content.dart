import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class WebMainContent extends StatefulWidget {
  const WebMainContent({
    super.key,
    required this.child,
    //required int selectedIndex, required Widget child,
  });

  final Widget child; // Add this line

  @override
  State<StatefulWidget> createState() => _WebMainContentState();
}

class _WebMainContentState extends State<WebMainContent> {
  @override
  Widget build(BuildContext context) {
    // return WebDashboardWidget(widget: widget);
    return widget.child;
  }
}
