import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skillapp/core/configs/configs.dart';

class WebLogoDisplay extends StatelessWidget {
  const WebLogoDisplay({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 28, 24, 28),
      child: Row(
        children: [
          SvgPicture.asset(
            'assets/images/logo/logo.svg',
          ),
          const SizedBox(
            width: 16,
          ),
          const Expanded(
            child: Text(
              "Selective Ninja",
              style: kNavigationbarLogoTs,
              overflow: TextOverflow.fade,
            ),
          ),
        ],
      ),
    );
  }
}
