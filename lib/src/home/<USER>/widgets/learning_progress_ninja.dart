
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/home/<USER>/blocs/dashboard_summary/dashboard_summary_bloc.dart';
import 'package:skillapp/src/home/<USER>/widgets/daily_goal_progress.dart';
import 'package:skillapp/src/streaks/presentation/providers/current_day_streak_status_provider.dart';

class LearningProgressNinjaWidget extends StatefulWidget {
  const LearningProgressNinjaWidget({super.key});

  @override
  State<LearningProgressNinjaWidget> createState() =>
      _LearningProgressNinjaState();
}

class _LearningProgressNinjaState extends State<LearningProgressNinjaWidget> {
  @override
  void initState() {
    super.initState();
    context.read<DashboardSummaryBloc>().add(GetDashboardSummary());
    print("Learning progress widget called");
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 16 * fem),
          padding: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 20 * fem, 16 * fem),
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20 * fem),
            gradient: const LinearGradient(
              begin: Alignment(0.00, -1.00),
              end: Alignment(0, 1),
              colors: [Color(0xFF50409A), Color(0xFF08002E)],
              // stops: <double>[0, 1],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin:
                    EdgeInsets.fromLTRB(0 * fem, 0 * fem, 11 * fem, 12 * fem),
                width: double.infinity,
                height: 64 * fem,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(
                          0 * fem, 16 * fem, 61 * fem, 0 * fem),
                      height: double.infinity,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          LearningImageIconWidget(fem),
                          LearningBadgeNameWidget(fem, ffem),
                        ],
                      ),
                    ),
                    // LearnigBadgeWidget(fem),
                  ],
                ),
              ),
              Container(
                padding:
                    EdgeInsets.fromLTRB(2 * fem, 2 * fem, 120 * fem, 4 * fem),
                child: BlocBuilder<DashboardSummaryBloc, DashboardSummaryState>(
                    builder: (context, state) {
                  if (state is DashboardSummaryLoaded) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Streak',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w400,
                                height: 0,
                              ),
                            ),
                            SizedBox(height: 6 * fem),
                            Text(
                              state.dashboardSummary.streakDays,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                                height: 0,
                              ),
                            )
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Text(
                                  'XP',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w400,
                                    height: 0,
                                  ),
                                ),
                                SizedBox(
                                  width: 4 * fem,
                                ),
                                Image.asset(
                                    'assets/images/dashboard/xp_logo.png')
                              ],
                            ),
                            SizedBox(height: 6 * fem),
                            Text(
                              state.dashboardSummary.xpPoints.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                                height: 0,
                              ),
                            )
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Text(
                                  'Coins',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w400,
                                    height: 0,
                                  ),
                                ),
                                SizedBox(
                                  width: 4 * fem,
                                ),
                                Image.asset(
                                    'assets/images/dashboard/ninjacoin_logo.png'),
                              ],
                            ),
                            SizedBox(height: 6 * fem),
                            Text(
                              state.dashboardSummary.skillPoints.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                                height: 0,
                              ),
                            )
                          ],
                        )
                      ],
                    );
                  } else {
                    //else condition
                    return Container();
                  }
                }),
              ),
              SizedBox(
                height: 50 * fem,
              ),
            ],
          ),
        ),
        Positioned(
            right: 30,
            bottom: 16,
            child: Image.asset(
              'assets/images/dashboard/blueNinja.png',
            )),
        Positioned(
          bottom: 16,
          right: 16,
          left: 16,
          // top: 10,
          child: Consumer<CurrentDayStreakStatusProvider>(
            builder: (_, provider, __) {
              if (provider.currentDayStreakStatus != null) {
                int targetCount =
                    provider.currentDayStreakStatus?.targetCount ?? 0;
                int completedCount =
                    provider.currentDayStreakStatus?.completedCount ?? 0;

                double completedPercent = (completedCount / targetCount);
                print("Completed percentage is $completedPercent");
                int percentToShow = (completedPercent * 100).toInt();

                if (percentToShow >= 100) {
                  return Container(
                    height: 55 * fem,
                    padding: EdgeInsets.fromLTRB(
                        16 * fem, 0 * fem, 0 * fem, 0 * fem),
                    child: Row(
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const Text(
                            'Daily goals',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                          SizedBox(
                            width: 8 * fem,
                          ),
                          Image.asset(
                              'assets/images/dashboard/completed_tick.png'),
                        ]),
                  );
                } else {
                  return DailyGoalProgress(
                      fem: fem,
                      completedPercent: completedPercent,
                      percentToShow: percentToShow);
                }
              } else {
                return DailyGoalProgress(
                    fem: fem, completedPercent: 0, percentToShow: 0);
              }
            },
          ),
        ),
      ],
    );
  }

  Container LearningProgressBarWidget(double fem, double ffem) {
    return Container(
      // autogroupmv7pr2y (4mb6jMgKi4DbkPz5qXMv7P)
      margin: EdgeInsets.fromLTRB(4 * fem, 0 * fem, 0 * fem, 0 * fem),
      width: double.infinity,
      height: 18 * fem,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            // autogroupm4dbNX7 (4mb6q2BZ1G426UGGcXM4Db)
            margin: EdgeInsets.fromLTRB(0 * fem, 5 * fem, 12 * fem, 5 * fem),
            width: 266 * fem,
            height: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xff2c1e59),
              borderRadius: BorderRadius.circular(6 * fem),
            ),
            child: Align(
              // rectangle396CD (2:37)
              alignment: Alignment.centerLeft,
              child: SizedBox(
                width: 172 * fem,
                height: 8 * fem,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6 * fem),
                    color: const Color(0xff39e94b),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x3f000000),
                        offset: Offset(0 * fem, 0 * fem),
                        blurRadius: 3 * fem,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Text(
            // b8y (2:12)
            '60%',
            style: SafeGoogleFont(
              'Poppins',
              fontSize: 12 * ffem,
              fontWeight: FontWeight.w500,
              height: 1.5 * ffem / fem,
              color: const Color(0xffffffff),
            ),
          ),
        ],
      ),
    );
  }

  Container LearnigBadgeWidget(double fem) {
    return Container(
      // autogroupl9iujh7 (4mb6MHUmWTBhWzV8CLL9iu)
      // padding: EdgeInsets.fromLTRB(10.5 * fem, 15.5 * fem, 9.5 * fem, 13.5 * fem),
      padding: EdgeInsets.fromLTRB(10.5 * fem, 0 * fem, 9.5 * fem, 0 * fem),
      height: double.infinity,
      /* decoration: BoxDecoration(
        border: const Border(),
        gradient: const LinearGradient(
          begin: Alignment(0, -1),
          end: Alignment(0, 1),
          colors: <Color>[Color(0x8cffffff), Color(0x8cffffff)],
          stops: <double>[0, 1],
        ),
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(8 * fem),
          bottomLeft: Radius.circular(8 * fem),
        ),
      ),*/
      child: Center(
        // awardstarfill0wght400grad0opsz (2:39)
        child: SizedBox(
          //  width: 35 * fem,
          // height: 35 * fem,
          child: Image.asset(
            'assets/images/dashboard/blueNinja.png',
            //  width: 35 * fem,
            //  height: 35 * fem,
          ),

          /* child: Image.asset(
            'assets/images/dashboard/awardstarfill0wght400grad0opsz24-1.png',
            width: 35 * fem,
            height: 35 * fem,
          ),*/
        ),
      ),
    );
  }

  Container LearningBadgeNameWidget(double fem, double ffem) {
    return Container(
      // knightwarriorFG9 (2:9)
      margin: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 0 * fem, 11 * fem),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Knight warrior',
            style: SafeGoogleFont(
              'Poppins',
              fontSize: 16 * ffem,
              fontWeight: FontWeight.w600,
              height: 1.5 * ffem / fem,
              color: const Color(0xffffffff),
            ),
          ),
          const Text(
            'Level 1',
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
              height: 0,
            ),
          ),
        ],
      ),
    );
  }

  Container LearningImageIconWidget(double fem) {
    return Container(
      margin: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 16 * fem, 0 * fem),
      padding: EdgeInsets.fromLTRB(4 * fem, 4 * fem, 4 * fem, 4 * fem),
      width: 48 * fem,
      height: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24 * fem),
        gradient: const LinearGradient(
          begin: Alignment(0.896, 0.667),
          end: Alignment(-1.125, 0.875),
          colors: <Color>[Color(0xbaffffff), Color(0xbaffffff)],
          stops: <double>[0, 1],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0x3f000000),
            offset: Offset(0 * fem, 0 * fem),
            blurRadius: 2 * fem,
          ),
        ],
      ),
      child: Center(
        // ellipse11Z8R (2:15)
        child: SizedBox(
          width: double.infinity,
          height: 40 * fem,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20 * fem),
              image: const DecorationImage(
                image: AssetImage(
                  'assets/images/dashboard/ellipse-11-bg.png',
                  //'assets/images/dashboard/batman_face.png',
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
