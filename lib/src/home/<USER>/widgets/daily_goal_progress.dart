import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:skillapp/core/configs/configs.dart';

class DailyGoalProgress extends StatelessWidget {
  const DailyGoalProgress({
    super.key,
    required this.fem,
    required this.completedPercent,
    required this.percentToShow,
  });

  final double fem;
  final double completedPercent;
  final int percentToShow;

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 55 * fem,
      height: MediaQuery.of(context).size.height * 0.075,
      padding: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 0 * fem, 0 * fem),
      decoration: ShapeDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Colors.white.withOpacity(0.25),
            Colors.white.withOpacity(0.1),
          ],
          stops: const [0.0211, 1.0981],
        ),
        shape: RoundedRectangleBorder(
          side: const BorderSide(width: 1, color: Colors.white),
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Row(
        // crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(0 * fem, 10 * fem, 0 * fem, 10 * fem),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Daily goals',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 0,
                  ),
                ),
                SizedBox(
                  height: 3 * fem,
                ),
                LinearPercentIndicator(
                  padding: EdgeInsets.all(0 * fem),
                  width: 140 * fem,
                  lineHeight: 8 * fem,
                  percent: completedPercent,
                  barRadius: Radius.circular(16 * fem),
                  backgroundColor: kPrimaryColor,
                  progressColor: kAppBarProgressColor,
                  animation: true,
                  animationDuration: 1000,
                  trailing: Padding(
                    padding:
                        EdgeInsets.fromLTRB(8 * fem, 0 * fem, 0 * fem, 0 * fem),
                    child: Text(
                      '$percentToShow%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 0,
                      ),
                    ),
                  ),
                  linearStrokeCap: LinearStrokeCap.butt,
                  curve: Curves.easeInCirc,
                ),
              ],
            ),
          ),
          SizedBox(
            width: 25 * fem,
          ),
          GestureDetector(
            onTap: () {
              print('completed pressed');
            },
            child: Container(
              padding:
                  EdgeInsets.fromLTRB(19 * fem, 5 * fem, 19 * fem, 5 * fem),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  side: const BorderSide(width: 1, color: Color(0xFF50409A)),
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text('Complete',
                  style: TextStyle(
                    color: Color(0xFF50409A),
                    fontSize: 12,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    height: 0,
                  )),
            ),
          )
        ],
      ),
    );
  }
}
