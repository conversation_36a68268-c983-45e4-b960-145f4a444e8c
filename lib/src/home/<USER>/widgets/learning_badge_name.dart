import 'package:flutter/material.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/themes/custom_text_styles.dart';

class LearningBadgeNameWidget extends StatelessWidget {
  const LearningBadgeNameWidget({
    super.key,
    //  required this.fem,
    //  required this.ffem,
  });

  // final double fem;
  // final double ffem;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    double hfem = ScreenUtil.getHfem(context);

    return Container(
      // knightwarriorFG9 (2:9)
      margin: EdgeInsets.fromLTRB(0 * fem, 0 * fem, 0 * fem, 11 * fem),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Knight warrior',
            style: SafeGoogleFont(
              'Poppins',
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: const Color(0xffffffff),
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          const Text(
            'Level 1',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0,
            ),
          ),
        ],
      ),
    );
  }
}
