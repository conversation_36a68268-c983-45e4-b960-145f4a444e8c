import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/home/<USER>/repos/subjects_repo.dart';

class FetchSubjects extends FutureUsecaseWithoutParams<List<Subject>> {
  const FetchSubjects(
      {required SubjectsRepo subjectsRepo, required CacheContext cacheContext})
      : _repo = subjectsRepo,
        _cacheContext = cacheContext;

  final SubjectsRepo _repo;
  final CacheContext _cacheContext;

  @override
  ResultFuture<List<Subject>> call() async {
    //Check if data is present in cache!!

    if (!_cacheContext.sujectCacheExists()) {
      final result = await _repo.getSubjects();
      if (result is Failure) {
        return result;
      } else {
        _cacheContext
            .populateSubjectCache(result.getOrElse(() => List.empty()));
      }
    }
    return Right(_cacheContext.getSubjectList());
  }
}
