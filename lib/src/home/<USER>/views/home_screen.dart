import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/widgets/app_drawer.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';
import 'package:skillapp/src/home/<USER>/widgets/learning_progress_ninja.dart';
import 'package:skillapp/src/home/<USER>/widgets/user_name.dart';
import 'package:skillapp/src/home/<USER>/widgets/dailypractice_gridview.dart';
import 'package:skillapp/src/home/<USER>/widgets/test_section_scroll.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/web_dashboard.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  factory HomeScreen.routeBuilder(_, __) {
    return const HomeScreen();
  }

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return const AdaptiveLayout(
        mobileBody: HomeScreenMobile(),
        tabletBody: HomeScreenTablet(),
        desktopBody: HomeScreenDesktop());
  }
}

class HomeScreenMobile extends StatelessWidget {
  const HomeScreenMobile({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          UIParameters.appBarTitleText,
          style: kDashboardAppbarTs,
        ),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.logout_outlined),
            tooltip: 'notifications',
            onPressed: () {
              print("logout pressed");
              context.read<AppBloc>().add(AppLogoutRequested());
              context.push("/logout");
            },
          ),
          IconButton(
            icon: const Icon(Icons.new_label),
            tooltip: 'Upload Questions',
            onPressed: () {
              print("Upload Questions pressed");
              context.push("/uploadquestions");
            },
          ),
          IconButton(
            icon: const Icon(Icons.accessibility_new_outlined),
            tooltip: 'test new feature',
            onPressed: () {
              print("ltest new feature");
              context.push("/testscreen");
            },
          ),
        ],
      ),
      drawer: const DashboardDrawer(),
      body: const SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UserNameWidget(),
            LearningProgressNinjaWidget(),
            DailyPraticeGridView(),
            TestScroll(),
          ],
        ),
      ),
    );
  }
}

class HomeScreenTablet extends StatelessWidget {
  const HomeScreenTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.red,
      child: const Center(
        child: Text("Tablet View"),
      ),
    );
  }
}

class HomeScreenDesktop extends StatefulWidget {
  const HomeScreenDesktop({super.key});

  @override
  State<HomeScreenDesktop> createState() => _HomeScreenDesktopState();
}

class _HomeScreenDesktopState extends State<HomeScreenDesktop> {
  // No need for state variables or methods as we're just providing the TestsBloc

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: sl<TestsBloc>(),
      child: const WebDashboardWidget(),
    );
  }
}
