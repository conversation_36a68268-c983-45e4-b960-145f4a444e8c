import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/home/<USER>/models/subjects_model.dart';

abstract class SubjectsRemoteDataSource {
  const SubjectsRemoteDataSource();

  Future<List<SubjectModel>> getSubjects();
}

class SubjectsRemoteDataSourceImpl implements SubjectsRemoteDataSource {
  final FirebaseFirestore _firestore;

  SubjectsRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  @override
  Future<List<SubjectModel>> getSubjects() async {
    try {
      final CollectionReference subjectsCollection =
          _firestore.collection('subjects');
      QuerySnapshot querySnapshot = await subjectsCollection.get();
      List<SubjectModel> subjects = querySnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return SubjectModel(
          id: doc.id,
          description: data['description'] as String,
          image: data['image'] as String,
        );
      }).toList();

      /*Reference code for shared preferences
      //persist subjects list to shared preferences
      await _prefs.setStringList(
          'subjects', subjects.map((e) => e.toJson()).toList());
      //read the subjects data from shared preferences and assign to list
      subjects = _prefs
          .getStringList('subjects')!
          .map((e) => SubjectModel.fromJson(e))
          .toList();
      reference code ends! */
      print("AW:getSubjects_before getQuestions call:subject= $subjects");
      //populateQuestionBank();
      print("AW:getSubjects after getQuestions call");
      return subjects;
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrint(s.toString());
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }
}
