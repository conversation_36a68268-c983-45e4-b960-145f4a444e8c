import 'dart:convert';

import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/common/entities/subjects.dart';

class SubjectModel extends Subject {
  const SubjectModel({
    required super.id,
    required super.description,
    required super.image,
  });

  factory SubjectModel.fromMap(DataMap map) {
    return SubjectModel(
      id: map['id'] as String,
      description: map['description'] as String,
      image: map['image'] as String,
    );
  }

  factory SubjectModel.fromJson(String source) =>
      SubjectModel.fromMap(json.decode(source) as DataMap);

  @override
  List<Object?> get props => [id, description];
}

class QuestionBundleModel {
  final String id;
  final bool isCompleted;
  final Set<String> attemptedQuestions;

  QuestionBundleModel(
      {required this.id,
      this.isCompleted = false,
      this.attemptedQuestions = const <String>{}});
}

class SubjectsDefaultModel {
  var subjects = [
    const SubjectModel(
        id: "maths",
        description: "MathsD",
        image: "assets/images/dashboard/calculator.png"),
    const SubjectModel(
        id: "reading",
        description: "ReadingD",
        image: "assets/images/dashboard/ios-books-application.png"),
    const SubjectModel(
        id: "thinking",
        description: "Thinking skillsD",
        image: "assets/images/dashboard/light-bulb.png"),
    const SubjectModel(
        id: "writing",
        description: "WritingD",
        image: "assets/images/dashboard/pencil.png")
  ];
}
