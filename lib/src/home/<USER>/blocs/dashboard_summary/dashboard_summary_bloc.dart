import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';
import 'package:skillapp/src/experience_score/domain/usecases/fetch_experience_score.dart';
import 'package:skillapp/src/home/<USER>/entities/dashboard_summary.dart';
import 'package:skillapp/src/skillpoints/domain/usecases/fetch_total_skill_points.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_day_count.dart';

part 'dashboard_summary_event.dart';
part 'dashboard_summary_state.dart';

class DashboardSummaryBloc
    extends Bloc<DashboardSummaryEvent, DashboardSummaryState> {
  final FetchTotalSkillpoints _fetchTotalSkillpoints;
  final FetchCurrentStreakDayCount _fetchCurrentStreakDayCount;
  final FetchExperienceScore _fetchExperienceScore;

  DashboardSummaryBloc({
    required FetchTotalSkillpoints fetchTotalSkillpoints,
    required FetchCurrentStreakDayCount fetchCurrentStreakDayCount,
    required FetchExperienceScore fetchExperienceScore,
  })  : _fetchTotalSkillpoints = fetchTotalSkillpoints,
        _fetchCurrentStreakDayCount = fetchCurrentStreakDayCount,
        _fetchExperienceScore = fetchExperienceScore,
        super(DashboardSummaryInitial()) {
    on<GetDashboardSummary>(_getDashboardSummary);
    on<ResetDashboardSummary>(_resetDashboardSummary);
  }

  Future<void> _resetDashboardSummary(
      ResetDashboardSummary event, Emitter<DashboardSummaryState> emit) async {
    print("Inside _resetDashboardSummary");
    emit(DashboardSummaryLoading());
  }

  Future<void> _getDashboardSummary(
      GetDashboardSummary event, Emitter<DashboardSummaryState> emit) async {
    print("Inside _getDashboardSummary");
    emit(DashboardSummaryLoading());

    //final pointsResultFuture = _fetchTotalSkillpoints();
    //final streakResultFuture = _fetchCurrentStreakSummary();
    final experienceScoreResultFuture = _fetchExperienceScore();
    int skillpoints = 0;
    int experienceScorePoints = 0;

    final pointsResult = await _fetchTotalSkillpoints();
    //final streakResult = await _fetchCurrentStreakSummary();
    final experienceScoreResult = await experienceScoreResultFuture;

    if (pointsResult is Right) {
      skillpoints = pointsResult.getOrElse(() => 0);
    }

    final streakDaysResult = await _fetchCurrentStreakDayCount();

    int streakDays = -1;
    if (streakDaysResult is Right) {
      streakDays = streakDaysResult.getOrElse(() => -1);
    }

    if (experienceScoreResult is Right) {
      final experienceScore =
          experienceScoreResult.getOrElse(() => const ExperienceScore(0));
      experienceScorePoints = experienceScore.score;
    }

    print('SkillPoints - $skillpoints');
    print('ExperienceScore - $experienceScorePoints');
    print('StreakDays - $streakDays');

    emit(DashboardSummaryLoaded(
      DashboardSummary(
        skillPoints: skillpoints,
        xpPoints: experienceScorePoints,
        streakDays: streakDays != -1 ? '$streakDays days' : '0 days',
      ),
    ));
  }

  @override
  Future<void> close() {
    print("DashboardSummaryBloc is being closed.");
    return super.close();
  }
}
