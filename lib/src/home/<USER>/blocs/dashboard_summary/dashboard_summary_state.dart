part of 'dashboard_summary_bloc.dart';

sealed class DashboardSummaryState extends Equatable {
  const DashboardSummaryState();

  @override
  List<Object> get props => [];
}

final class DashboardSummaryInitial extends DashboardSummaryState {}

final class DashboardSummaryLoading extends DashboardSummaryState {}

final class DashboardSummaryLoaded extends DashboardSummaryState {
  final DashboardSummary dashboardSummary;

  const DashboardSummaryLoaded(this.dashboardSummary);

  @override
  List<Object> get props => [dashboardSummary];
}

class DashboardSummaryError extends DashboardSummaryState {
  final String message;

  const DashboardSummaryError(this.message);

  @override
  List<Object> get props => [message];
}
