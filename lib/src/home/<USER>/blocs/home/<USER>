import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/src/home/<USER>/usecases/fetch_subjects.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc({required FetchSubjects fetchSubjects})
      : _fetchSubjects = fetchSubjects,
        super(HomeDefault()) {
    on<FetchSubjectsEvent>(_fetchSubjectsList);
  }

  final FetchSubjects _fetchSubjects;

  Future<void> _fetchSubjectsList(
    FetchSubjectsEvent event,
    Emitter<HomeState> emit,
  ) async {
    final result = await _fetchSubjects();

    result.fold(
        (failure) => {
              emit(
                HomeDataState(
                  subjects: List.empty(),
                ),
              )
            },
        (res) => {
              emit(
                HomeDataState(
                  subjects: res,
                ),
              )
            });
  }
}
