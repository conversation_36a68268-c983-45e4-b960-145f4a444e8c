// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  late final List<Subject> subjects;
  //Other details will come here.

  @override
  bool? get stringify => true;

  @override
  List<Object> get props => [];
}

class HomeDefault extends HomeState {
  HomeDefault() {
    subjects = [
      const Subject(
          id: "maths",
          description: "Maths",
          image: "assets/images/dashboard/calculator.png"),
      const Subject(
          id: "reading",
          description: "Reading",
          image: "assets/images/dashboard/ios-books-application.png"),
      const Subject(
          id: "thinking",
          description: "Thinking skills",
          image: "assets/images/dashboard/light-bulb.png"),
      const Subject(
          id: "writing",
          description: "Writing",
          image: "assets/images/dashboard/pencil.png")
    ];
  }
}

class HomeDataState extends HomeState {
  HomeDataState({
    required subjects,
  }) {
    this.subjects = subjects;
  }
}
