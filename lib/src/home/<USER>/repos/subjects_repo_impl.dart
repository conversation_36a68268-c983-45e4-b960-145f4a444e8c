import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/home/<USER>/datasources/subjects_remote_data_source.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/src/home/<USER>/repos/subjects_repo.dart';

class SubjectsRepoImpl extends SubjectsRepo {
  SubjectsRepoImpl(this._subjectsRemoteDataSource);

  final SubjectsRemoteDataSource _subjectsRemoteDataSource;

  @override
  ResultFuture<List<Subject>> getSubjects() async {
    try {
      final result = await _subjectsRemoteDataSource.getSubjects();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }
}
