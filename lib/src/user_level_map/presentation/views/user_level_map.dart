import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/user_level_map/domain/entities/level.dart';

class UserLevelMapWidget extends StatefulWidget {
  const UserLevelMapWidget({super.key});

  factory UserLevelMapWidget.routeBuilder(_, __) {
    return const UserLevelMapWidget();
  }

  @override
  State<StatefulWidget> createState() => _UserLevelMapWidgetState();
}

class _UserLevelMapWidgetState extends State<UserLevelMapWidget> {
  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    double deviceWidth = MediaQuery.of(context).size.width;
    double deviceHeight = MediaQuery.of(context).size.height;
    List<Level> userLevelList = [
      const Level('LEVEL1', 0.35, 0.73),
      const Level('LEVEL2', 0.62, 0.66),
      const Level('LEVEL3', 0.46, 0.55),
      const Level('LEVEL4', 0.20, 0.48),
      const Level('LEVEL5', 0.41, 0.425),
    ];

    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
            image: AssetImage('assets/images/userLevelMap/userlevel1.png'),
            fit: BoxFit.fill),
      ),
      child: Stack(
        children: [
          Positioned(
              left: deviceWidth * 0.13,
              top: deviceHeight * 0.45,
              //left: 50 * fem,
              //top: 356 * fem,
              child:
                  Image.asset('assets/images/userLevelMap/userlevelpath1.png')),
          Positioned(
            left: deviceWidth * 0.35,
            top: deviceHeight * 0.73,

            //   left: 130 * fem,
            //  top: 590 * fem,
            child: Container(
              width: 72 * fem,
              height: 72 * fem,
              decoration: const ShapeDecoration(
                color: Color(0xFFD9D9D9),
                shape: OvalBorder(),
                shadows: [
                  BoxShadow(
                    color: Color(0xC4FFFFFF),
                    blurRadius: 24,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: CircularPercentIndicator(
                radius: 32.0,
                lineWidth: 4.0,
                percent: 0.60,
                center: Image.asset(
                  'assets/images/userLevelMap/userlevelflag1.png',
                  height: 29 * fem,
                  width: 26 * fem,
                ),
                backgroundColor: const Color(0xFFD9D9D9),
                progressColor: kAppBarProgressColor,
              ),
            ),
          ),
          Positioned(
              left: deviceWidth * 0.62,
              top: deviceHeight * 0.66,

              // left: 220 * fem,
              //  top: 528 * fem,
              child: Container(
                width: 52 * fem,
                height: 52 * fem,
                decoration: const ShapeDecoration(
                  color: Color(0xFFADADAD),
                  shape: OvalBorder(),
                ),
                child: Image.asset(
                    'assets/images/userLevelMap/userlevellocked1.png'),
              )),
          Positioned(
              left: deviceWidth * 0.46,
              top: deviceHeight * 0.55,

              //  left: 160 * fem,
              //  top: 455 * fem,
              child: Container(
                width: 52 * fem,
                height: 52 * fem,
                decoration: const ShapeDecoration(
                  color: Color(0xFFADADAD),
                  shape: OvalBorder(),
                ),
                child: Image.asset(
                    'assets/images/userLevelMap/userlevellocked1.png'),
              )),
          Positioned(
              left: deviceWidth * 0.20,
              top: deviceHeight * 0.48,
              // left: 75 * fem,
              // top: 385 * fem,
              child: Container(
                width: 52 * fem,
                height: 52 * fem,
                decoration: const ShapeDecoration(
                  color: Color(0xFFADADAD),
                  shape: OvalBorder(),
                ),
                child: Image.asset(
                    'assets/images/userLevelMap/userlevellocked1.png'),
              )),
          Positioned(
              left: deviceWidth * 0.41,
              top: deviceHeight * 0.425,

              //  left: 145 * fem,
              //  top: 335 * fem,
              child: Container(
                width: 52 * fem,
                height: 52 * fem,
                decoration: const ShapeDecoration(
                  color: Color(0xFFADADAD),
                  shape: OvalBorder(),
                ),
                child: Image.asset(
                    'assets/images/userLevelMap/userlevellocked1.png'),
              )),
          Positioned(
            left: deviceWidth * 0.60,
            top: deviceHeight * 0.755,

            // left: 220 * fem,
            // top: 610 * fem,
            child: Container(
              padding:
                  EdgeInsets.fromLTRB(15 * fem, 5 * fem, 15 * fem, 5 * fem),
              decoration: ShapeDecoration(
                color: const Color(0xFFFF7BBF),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(9)),
              ),
              child: GestureDetector(
                child: const Text(
                  'Start',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
                onTap: () {
                  print("Start button in level map is clicked");
                },
              ),
            ),
          ),
          Positioned(
            bottom: deviceHeight * 0.05,
            left: deviceWidth * 0.28,
            child: GestureDetector(
              onTap: () => context.go('/home'),
              child: Container(
                padding:
                    EdgeInsets.fromLTRB(40 * fem, 15 * fem, 40 * fem, 15 * fem),
                alignment: Alignment.center,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(26),
                  ),
                  color: kPrimaryColor,
                ),
                child: const Text('Continue journey',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                      height: 0,
                      decoration: TextDecoration.none,
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
