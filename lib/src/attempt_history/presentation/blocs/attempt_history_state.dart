part of 'attempt_history_bloc.dart';

sealed class AttemptHistoryState extends Equatable {
  final AttemptHistory attemptHistory;

  const AttemptHistoryState({required this.attemptHistory});

  @override
  List<Object> get props => [attemptHistory];
}

//add a new state with initial value as empty
class AttemptHistoryEmptyState extends AttemptHistoryState {
  const AttemptHistoryEmptyState()
      : super(attemptHistory: const AttemptHistory.empty());
}

//add a new state with initial value as loading
class AttemptHistoryLoadingState extends AttemptHistoryState {
  const AttemptHistoryLoadingState()
      : super(attemptHistory: const AttemptHistory.empty());
}

//add a new state with initial value as loaded
class AttemptHistoryLoadedState extends AttemptHistoryState {
  const AttemptHistoryLoadedState(AttemptHistory attemptHistoryData)
      : super(attemptHistory: attemptHistoryData);
}

//add a new state with initial value as error
class AttemptHistoryErrorState extends AttemptHistoryState {
  final String errorMessage;

  const AttemptHistoryErrorState(
      this.errorMessage, AttemptHistory? attemptHistory)
      : super(
          attemptHistory: attemptHistory ?? const AttemptHistory.empty(),
        );
}
