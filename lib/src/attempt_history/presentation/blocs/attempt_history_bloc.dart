import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/src/attempt_history/domain/entities/attempt_history.dart';
import 'package:skillapp/src/attempt_history/domain/usecases/fetch_attempt_history.dart';

part 'attempt_history_event.dart';
part 'attempt_history_state.dart';

class AttemptHistoryBloc
    extends Bloc<AttemptHistoryInitial, AttemptHistoryState> {
  final FetchAttemptHistory _fetchAttemptHistory;

  AttemptHistoryBloc({required FetchAttemptHistory fetchAttemptHistory})
      : _fetchAttemptHistory = fetchAttemptHistory,
        super(const AttemptHistoryEmptyState()) {
    on<FetchAttemptHistoryEvent>(_fetchAttemptHistoryEventHandler);
  }

  FutureOr<void> _fetchAttemptHistoryEventHandler(
      FetchAttemptHistoryEvent event, Emitter<AttemptHistoryState> emit) async {
    final result = await _fetchAttemptHistory(
        FetchAttemptHistoryParams(event.lastRecordTimestamp));

    result.fold(
        (failure) => emit(
              AttemptHistoryErrorState(
                  "Unable to fetch history data", state.attemptHistory),
            ), (success) {
      if (success.attemptHistoryList.isEmpty) {
        emit(
          AttemptHistoryErrorState("No data found", state.attemptHistory),
        );
      } else {
        emit(AttemptHistoryLoadedState(success));
      }
    });
  }

  /*Future<FutureOr<void>> _fetchAttemptHistoryOld(
      FetchAttemptHistoryEvent event, Emitter<AttemptHistoryState> emit) async {
    print("AW:Inside _fetchAttemptHistory");
    emit(
      const AttemptHistoryLoadingState(),
    );

    try {
      AttemptHistoryModel attemptHistoryData =
          await repository.fetchAttemptHistory(event.lastRecordTimestamp);
      print("AW:_fetchAttemptHistory:attemptHistoryData=$attemptHistoryData");
      if (attemptHistoryData.attemptHistoryList.isEmpty) {
        emit(
          AttemptHistoryErrorState("No data found", state.attemptHistory),
        );
      } else {
        emit(AttemptHistoryLoadedState(
            convertAttemptHistoryToModel(attemptHistoryData)));
      }
    } catch (e, s) {
      //print stacktrace here

      print("Exception: $e ");
      print(s);
      emit(AttemptHistoryErrorState(
          "Error while fetching next set data", state.attemptHistory));
    }
  }*/
}
