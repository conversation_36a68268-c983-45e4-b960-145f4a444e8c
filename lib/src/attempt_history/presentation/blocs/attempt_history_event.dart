// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'attempt_history_bloc.dart';

sealed class AttemptHistoryInitial extends Equatable {
  const AttemptHistoryInitial();
  @override
  List<Object> get props => [];
}

class FetchAttemptHistoryEvent extends AttemptHistoryInitial {
  final int lastRecordTimestamp;
  const FetchAttemptHistoryEvent({
    required this.lastRecordTimestamp,
  });

  @override
  String toString() =>
      'FetchAttemptHistoryEvent (lastRecordTimestamp: $lastRecordTimestamp)';
}
