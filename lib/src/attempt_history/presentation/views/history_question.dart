import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/question.dart';

import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/common/widgets/web/web_answer_options_bloc_widget.dart';
import 'package:skillapp/core/common/widgets/web/web_dailytest_banner.dart';
import 'package:skillapp/core/common/widgets/web/web_question_bloc_widget.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/flag_question/presentation/widgets/web_history_pratice_footer.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:skillapp/src/practice/presentation/widgets/practice_floating_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/practice_footer.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_hint_and_solution_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_more_actions_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_practice_footer.dart';
import 'package:skillapp/src/practice/presentation/widgets/web/web_pratice_floating_widget.dart';

class SingleQuestionFlow extends StatefulWidget {
  final String bundleId;
  final String questionId;
  final String subject;

  const SingleQuestionFlow({
    required this.bundleId,
    required this.questionId,
    required this.subject,
    super.key,
  });

  factory SingleQuestionFlow.routeBuilder(BuildContext context,
      GoRouterState state, String bundleId, String questionId, String subject) {
    return SingleQuestionFlow(
        bundleId: bundleId, questionId: questionId, subject: subject);
  }

  @override
  State<SingleQuestionFlow> createState() => _SingleQuestionFlowState();
}

class _SingleQuestionFlowState extends State<SingleQuestionFlow> {
  @override
  void initState() {
    super.initState();
    print("AW: Before initState in SingleQuestionFlow");
    // contect.read<PracticeBloc>().add(Flow(delegate: delegate))
    context.read<PracticeBloc>().add(
          FetchSingleQuestionEvent(
              widget.questionId, widget.bundleId, widget.subject),
        );
    print("AW: After initState in SingleQuestionFlow");
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayout(
        mobileBody: SingleQuestionFlowMobile(
          subject: widget.subject,
        ),
        tabletBody: SingleQuestionFlowTablet(),
        desktopBody: SingleQuestionFlowDesktop(
          subject: widget.subject,
        ));
  }
}

class SingleQuestionFlowDesktop extends StatelessWidget {
  final String subject;
  const SingleQuestionFlowDesktop({super.key, required this.subject});

  @override
  Widget build(BuildContext context) {
    double fem = MediaQuery.of(context).size.width / 375;
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
    //bool questionLoaded = false;
    return BlocListener<PracticeBloc, PracticeState>(
      listener: (context, state) {
        if (state.isError) {
          //TODO-Handle error here
        }
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: kWebMainContentBgColor,
        body: Padding(
          padding: const EdgeInsets.all(40),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const Expanded(
                flex: 2,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      WebQuestionBlocBuilder(),
                      SizedBox(height: 40),
                      WebAnswerOptionsBlocBuilder(),
                      SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 40 * fem),
              /*  const Expanded(
                flex: 1,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      //  MoreActionsWidget(),
                      // SizedBox(height: 40),
                      // WebDailyTestBanner(),
                    ],
                  ),
                ),
              ),*/
            ],
          ),
        ),
        endDrawer: BlocBuilder<PracticeBloc, PracticeState>(
          builder: (context, state) {
            bool showExplanation = false;
            var correctAnswer = '';

            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              showExplanation = true;
            }

            QuestionAndAnswers question = state.questionAndAnswer;
            List<OptionEntry> options = question.answerOptions.entries;
            for (int i = 0; i < options.length; i++) {
              if (options[i].id == question.correctAnswer) {
                correctAnswer = options[i].displayId;
              }
            }

            return WebHintAndSolutionDrawer(
                showExplanation: showExplanation,
                correctAnswer: correctAnswer,
                question: question,
                scaffoldKey: scaffoldKey);
          },
        ),
        floatingActionButton:
            BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          return Builder(builder: (context) {
            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.failure ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    flex: 2,
                    child: Padding(
                        padding: EdgeInsets.fromLTRB(1 * fem, 0, 10 * fem, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            WebHintAndSolutionWidget(
                                state: state, scaffoldKey: scaffoldKey),
                          ],
                        )),
                  ),
                  Expanded(flex: 1, child: Container()),
                ],
              );
            } else {
              return Container();
            }
          });
        }),
        bottomNavigationBar: BottomAppBar(
          height: 130,
          notchMargin: 0,
          padding: const EdgeInsets.all(0),
          color: kWebMainContentBgColor,
          //  color: Colors.yellow,
          child: Padding(
            padding: const EdgeInsets.only(left: 40, right: 40),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Container(
                    height: 90,
                    decoration: ShapeDecoration(
                      color: kOnSurfaceTextColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      shadows: const [
                        BoxShadow(
                          color: Color(0x14000000),
                          blurRadius: 32,
                          offset: Offset(0, 0),
                          spreadRadius: 0,
                        )
                      ],
                    ),
                    //  child: const WebPraticeScreenFooter(),
                    child: const WebHistoryPraticeScreenFooter(),
                  ),
                ),
                Expanded(flex: 1, child: Container()),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SingleQuestionFlowTablet extends StatelessWidget {
  const SingleQuestionFlowTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: const Text('SingleQuestionFlowTablet'),
    );
  }
}

class SingleQuestionFlowMobile extends StatelessWidget {
  final String subject;

  const SingleQuestionFlowMobile({super.key, required this.subject});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    String appBarTitle = subject;
    //bool questionLoaded = false;
    return BlocListener<PracticeBloc, PracticeState>(
      listener: (context, state) {
        if (state.isError) {
          //TODO-Handle error here
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0XFFF5F5F5),
        appBar: AppBarTemplate(
          appBarTitle: "Saved Question : $appBarTitle",
          actionType: 'NA',
        ),
        body: const SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: QuestionAnswerWidget(),
        ),
        floatingActionButton:
            BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          return Builder(builder: (context) {
            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.failure ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              return HintAndSolutionWidget(state: state);
            } else {
              return Container();
            }
          });
        }),
        persistentFooterButtons: const [
          PraticeScreenFooter(),
        ],
      ),
    );
  }
}
