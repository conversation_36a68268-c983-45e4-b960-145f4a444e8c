import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/ui/adaptive/layout/adaptivelayout_widget.dart';
import 'package:skillapp/src/attempt_history/domain/entities/attempt_history.dart';
import 'package:skillapp/src/attempt_history/presentation/blocs/attempt_history_bloc.dart';

class HistoryQuestionList extends StatefulWidget {
  const HistoryQuestionList({super.key});

  factory HistoryQuestionList.routeBuilder(_, __) {
    return const HistoryQuestionList();
  }

  @override
  State<HistoryQuestionList> createState() => _HistoryQuestionListState();
}

class _HistoryQuestionListState extends State<HistoryQuestionList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    context
        .read<AttemptHistoryBloc>()
        .add(const FetchAttemptHistoryEvent(lastRecordTimestamp: 0));
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    print("Scrolling dude");
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // widget.yourBloc.add(LoadMoreDataEvent());
      print("Maximum pos reached");
    }
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayout(
        mobileBody:
            HistoryQuestionsListMobile(scrollController: _scrollController),
        tabletBody: const HistoryQuestionsListTablet(),
        desktopBody: const HistoryQuestionsListDesktop());
  }

/*

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Scaffold(
      backgroundColor: kProfileSavedQuestionBgColor,
      appBar: const AppBarTemplate(
          appBarTitle: 'Attempted Questions', actionType: 'NA'),
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: BlocBuilder<AttemptHistoryBloc, AttemptHistoryState>(
          builder: (context, state) {
            print("state = $state");
            if (state is AttemptHistoryLoadedState) {
              if (state.attemptHistory.attemptHistoryList.isEmpty) {
                return const Center(child: Text('No questions attempted'));
              } else {
                return displayHistoryQuestionsWidget(
                    fem, state.attemptHistory.attemptHistoryMap);
              }
            } else {
              return const Center(child: Text('No Questions loaded'));
            }
          },
        ),
      ),
    );
  }

  Container displayHistoryQuestionsWidget(
      double fem, Map<String, List<AttemptHistoryEntry>> historyMap) {
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 4 * fem),
      child: ListView.builder(
        itemCount: historyMap.length,
        shrinkWrap: true,
        controller: _scrollController,
        itemBuilder: (context, index) {
          final String heading = historyMap.keys.elementAt(index);
          final List<AttemptHistoryEntry>? questions = historyMap[heading];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading
              Container(
                padding: EdgeInsets.all(8 * fem),
                /* decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8 * fem),
              ),*/
                child: Text(
                  heading,
                  style: TextStyle(
                    fontSize: 16 * fem,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // List of Questions
              Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16 * fem)),
                child: Column(
                  children: questions!.map((question) {
                    return Container(
                      margin: EdgeInsets.fromLTRB(16 * fem, 0, 16 * fem, 0),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: kProfileMenuSeparationLine,
                            width: 1.0,
                          ),
                        ),
                      ),
                      child: ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: Container(
                          padding: EdgeInsets.fromLTRB(
                              4 * fem, 4 * fem, 4 * fem, 4 * fem),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4 * fem),
                          ),
                          child: IconTheme(
                            data: const IconThemeData(size: 10),
                            child: ColorFiltered(
                              colorFilter: ColorFilter.mode(
                                  getFlagColor('easy'), BlendMode.srcIn),
                              child: SvgPicture.asset(
                                'assets/images/practice/flagSelected.svg',
                              ),
                            ),
                          ),
                        ),
                        title: Text(
                          question.shortDescription,
                          style: kPracticeAppBarMenuItemTs,
                        ),
                        trailing: const IconTheme(
                          data: IconThemeData(size: 12),
                          child: Icon(Icons.arrow_forward_ios),
                        ),
                        onTap: () {
                          // Handle tap action
                          String bundleId = question.bundleId;
                          String questionId = question.questionId;
                          String subjectId = question.subjectId;

                          context.push(
                              '/singleQuestionFlow/${Uri.encodeComponent(bundleId)}/${Uri.encodeComponent(questionId)}/${Uri.encodeComponent(subjectId)}');
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  */
}

class HistoryQuestionsListDesktop extends StatelessWidget {
  const HistoryQuestionsListDesktop({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return SingleChildScrollView(
        child: Container(
            padding: const EdgeInsets.all(40),
            // this decoration will override the global backgroundcolor
            // decoration: BoxDecoration(color: Colors.white),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // const SizedBox(height: 20),
                      Container(
                        //    padding: EdgeInsets.fromLTRB(20, 0, 0, 0),
                        child: const Text(
                          'Attempted questions',
                          style: kWebSavedQuestionHeaderTs,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Container(
                        //padding: const EdgeInsets.symmetric(vertical: 20),
                        child: BlocBuilder<AttemptHistoryBloc,
                            AttemptHistoryState>(
                          builder: (context, state) {
                            print("state = $state");
                            if (state is AttemptHistoryLoadedState) {
                              if (state
                                  .attemptHistory.attemptHistoryList.isEmpty) {
                                return const Center(
                                    child: Text('No questions attempted'));
                              } else {
                                return displayHistoryQuestionsWidget(fem,
                                    state.attemptHistory.attemptHistoryMap);
                              }
                            } else {
                              return const Center(
                                  child: Text('No Questions loaded'));
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Container(
                    // color: Colors.red,
                    child: const Text(''),
                  ),
                ),
              ],
            )));

    /* return Scaffold(
      backgroundColor: kProfileSavedQuestionBgColor,
      appBar: const AppBarTemplate(
          appBarTitle: 'Attempted Questions', actionType: 'NA'),
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: BlocBuilder<AttemptHistoryBloc, AttemptHistoryState>(
          builder: (context, state) {
            print("state = $state");
            if (state is AttemptHistoryLoadedState) {
              if (state.attemptHistory.attemptHistoryList.isEmpty) {
                return const Center(child: Text('No questions attempted'));
              } else {
                return displayHistoryQuestionsWidget(
                    fem, state.attemptHistory.attemptHistoryMap);
              }
            } else {
              return const Center(child: Text('No Questions loaded'));
            }
          },
        ),
      ),
    );*/
  }

  Container displayHistoryQuestionsWidget(
      double fem, Map<String, List<AttemptHistoryEntry>> historyMap) {
    return Container(
      //   margin: const EdgeInsets.fromLTRB(16, 0, 16, 4),
      child: ListView.builder(
        itemCount: historyMap.length,
        shrinkWrap: true,
        //   controller: scrollController,
        itemBuilder: (context, index) {
          final String heading = historyMap.keys.elementAt(index);
          final List<AttemptHistoryEntry>? questions = historyMap[heading];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading
              Container(
                padding: const EdgeInsets.all(8),
                child: Text(
                  heading,
                  style: kWebAttemptedQuestionSubHeaderTs,
                ),
              ),
              // List of Questions
              const SizedBox(height: 10),
              Container(
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x1E000000),
                      blurRadius: 24,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Column(
                  //  children: questions!.map((question) {
                  children: questions!.asMap().entries.map((entry) {
                    int questionIndex = entry.key;
                    AttemptHistoryEntry question = entry.value;
                    return Container(
                      margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                      decoration: BoxDecoration(
                        border: questionIndex == questions.length - 1
                            ? null
                            : const Border(
                                bottom: BorderSide(
                                  color: kProfileMenuSeparationLine,
                                  width: 1.0,
                                ),
                              ),
                      ),
                      child: ListTile(
                        contentPadding:
                            const EdgeInsets.only(left: 32, right: 32),
                        /* leading: Container(
                          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4 * fem),
                          ),
                          child: Container(
                            child: Text(''),
                          ),
                        ),*/
                        title: Text(
                          question.shortDescription,
                          style: kWebAttemptedQuestionDescriptionTs,
                        ),
                        trailing: const IconTheme(
                          data: IconThemeData(size: 12),
                          child: Icon(Icons.arrow_forward_ios),
                        ),
                        onTap: () {
                          // Handle tap action
                          String bundleId = question.bundleId;
                          String questionId = question.questionId;
                          String subjectId = question.subjectId;

                          context.push(
                              '/singleQuestionFlow/${Uri.encodeComponent(bundleId)}/${Uri.encodeComponent(questionId)}/${Uri.encodeComponent(subjectId)}');
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 20),
            ],
          );
        },
      ),
    );
  }
}

class HistoryQuestionsListTablet extends StatelessWidget {
  const HistoryQuestionsListTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return const Text("This is tablet view");
  }
}

class HistoryQuestionsListMobile extends StatelessWidget {
  final ScrollController scrollController;

  const HistoryQuestionsListMobile({
    super.key,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Scaffold(
      backgroundColor: kProfileSavedQuestionBgColor,
      appBar: const AppBarTemplate(
          appBarTitle: 'Attempted Questions', actionType: 'NA'),
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: BlocBuilder<AttemptHistoryBloc, AttemptHistoryState>(
          builder: (context, state) {
            print("state = $state");
            if (state is AttemptHistoryLoadedState) {
              if (state.attemptHistory.attemptHistoryList.isEmpty) {
                return const Center(child: Text('No questions attempted'));
              } else {
                return displayHistoryQuestionsWidget(
                    fem, state.attemptHistory.attemptHistoryMap);
              }
            } else {
              return const Center(child: Text('No Questions loaded'));
            }
          },
        ),
      ),
    );
  }

  Container displayHistoryQuestionsWidget(
      double fem, Map<String, List<AttemptHistoryEntry>> historyMap) {
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 4 * fem),
      child: ListView.builder(
        itemCount: historyMap.length,
        shrinkWrap: true,
        controller: scrollController,
        itemBuilder: (context, index) {
          final String heading = historyMap.keys.elementAt(index);
          final List<AttemptHistoryEntry>? questions = historyMap[heading];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading
              Container(
                padding: EdgeInsets.all(8 * fem),
                /* decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8 * fem),
              ),*/
                child: Text(
                  heading,
                  style: TextStyle(
                    fontSize: 16 * fem,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // List of Questions
              Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16 * fem)),
                child: Column(
                  children: questions!.map((question) {
                    return Container(
                      margin: EdgeInsets.fromLTRB(16 * fem, 0, 16 * fem, 0),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: kProfileMenuSeparationLine,
                            width: 1.0,
                          ),
                        ),
                      ),
                      child: ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: Container(
                          padding: EdgeInsets.fromLTRB(
                              4 * fem, 4 * fem, 4 * fem, 4 * fem),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4 * fem),
                          ),
                          child: IconTheme(
                            data: const IconThemeData(size: 10),
                            child: ColorFiltered(
                              colorFilter: ColorFilter.mode(
                                  getFlagColor('easy'), BlendMode.srcIn),
                              child: SvgPicture.asset(
                                'assets/images/practice/flagSelected.svg',
                              ),
                            ),
                          ),
                        ),
                        title: Text(
                          question.shortDescription,
                          style: kPracticeAppBarMenuItemTs,
                        ),
                        trailing: const IconTheme(
                          data: IconThemeData(size: 12),
                          child: Icon(Icons.arrow_forward_ios),
                        ),
                        onTap: () {
                          // Handle tap action
                          String bundleId = question.bundleId;
                          String questionId = question.questionId;
                          String subjectId = question.subjectId;

                          context.push(
                              '/singleQuestionFlow/${Uri.encodeComponent(bundleId)}/${Uri.encodeComponent(questionId)}/${Uri.encodeComponent(subjectId)}');
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
