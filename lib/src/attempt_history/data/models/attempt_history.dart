import 'package:skillapp/src/attempt_history/domain/entities/attempt_history.dart';

class AttemptHistoryModel extends AttemptHistory {
  const AttemptHistoryModel({
    required super.attemptHistoryMap,
    required super.attemptHistoryList,
    required super.startAfterTimeStamp,
    required super.isLastPage,
  });

  const AttemptHistoryModel.empty() : super.empty();

  //add toString method
  @override
  String toString() {
    return "AttemptHistoryData[startAfterTimeStamp=$startAfterTimeStamp,isLastPage=$isLastPage,attemptHistoryMap=$attemptHistoryMap]";
  }
}
/*
class AttemptHistoryEntryModel {
  final String uniqueId;
  final String questionId;
  final String subjectId;
  final String shortDescription;
  final String bundleId;
  final List<AttemptHistoryEntryStatusModel> attemptStatusList;

  AttemptHistoryEntryModel({
    required this.uniqueId,
    required this.questionId,
    required this.subjectId,
    required this.shortDescription,
    required this.bundleId,
    required this.attemptStatusList,
  });

  @override
  String toString() {
    return "Entry[$uniqueId, $questionId, $shortDescription, $attemptStatusList]";
  }
}

class AttemptHistoryEntryStatusModel {
  final AttemptStatus attemptStatus;
  final Timestamp timestamp;

  AttemptHistoryEntryStatusModel({
    required this.attemptStatus,
    required this.timestamp,
  });


  @override
  String toString() {
    return "Status[$attemptStatus]";
  }
}*/
