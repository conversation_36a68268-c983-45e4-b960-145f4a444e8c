import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/attempt_history/data/datasources/attempt_history_remote_datasource.dart';
import 'package:skillapp/src/attempt_history/domain/entities/attempt_history.dart';
import 'package:skillapp/src/attempt_history/domain/repos/attempt_history_repo.dart';

class AttemptHistoryRepoImpl implements AttemptHistoryRepo {
  final AttemptHistoryRemoteDataSource _attemptHistoryRemoteDataSource;
  final CacheContext _cacheContext;

  AttemptHistoryRepoImpl({
    required AttemptHistoryRemoteDataSource attemptHistoryRemoteDataSource,
    required CacheContext cacheContext,
  })  : _attemptHistoryRemoteDataSource = attemptHistoryRemoteDataSource,
        _cacheContext = cacheContext;

  @override
  ResultFuture<AttemptHistory> getAttemptHistory(
      int lastRecordTimestamp) async {
    try {
      final result = await _attemptHistoryRemoteDataSource.getAttemptHistory(
          lastRecordTimestamp, _cacheContext.profileId);

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }
}
