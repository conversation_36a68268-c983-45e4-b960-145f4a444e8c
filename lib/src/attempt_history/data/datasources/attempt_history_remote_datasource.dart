import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/utils/repository_constants.dart';
import 'package:skillapp/src/attempt_history/data/models/attempt_history.dart';
import 'package:skillapp/src/attempt_history/domain/entities/attempt_history.dart';

abstract class AttemptHistoryRemoteDataSource {
  Future<AttemptHistoryModel> getAttemptHistory(
      int lastRecordTimestamp, String profileId);
}

class AttemptHistoryRemoteDataSourceImpl
    implements AttemptHistoryRemoteDataSource {
  final FirebaseFirestore _firestore;

  AttemptHistoryRemoteDataSourceImpl({
    required FirebaseFirestore firestore,
  }) : _firestore = firestore;

  @override
  Future<AttemptHistoryModel> getAttemptHistory(
      int lastRecordTimestamp, String profileId) async {
    print(
        "AW:Inside fetchAttemptHistory lastRecordTimestamp=$lastRecordTimestamp");

    CollectionReference attemptedQuestionsCollection =
        _firestore.collection('attempted_questions/$profileId/questions');

    //fetch only the last 50 entries from querySnapshot when it is ordered by timestamp field
    QuerySnapshot querySnapshot;
    if (lastRecordTimestamp != 0) {
      /*DocumentSnapshot startDoc =
          await attemptedQuestionsCollection.doc(startDocumentId).get();
      //how to get timestamp field from startDoc
      if (startDoc.data() != null) {
        print(startDoc.data()?['timestamp']);
      }*/
      Timestamp lastTimestamp =
          Timestamp.fromMillisecondsSinceEpoch(lastRecordTimestamp);
      print('lastTimestamp: $lastTimestamp');
      querySnapshot = await attemptedQuestionsCollection
          .orderBy('timeStamp', descending: true)
          .startAfter([lastTimestamp])
          .limit(RepositoryConstants.defaultPaginationFetchSize)
          .get();
    } else {
      querySnapshot = await attemptedQuestionsCollection
          .orderBy('timeStamp', descending: true)
          .limit(RepositoryConstants.defaultPaginationFetchSize)
          .get();
    }
    List<AttemptHistoryEntry> attemptHistoryList;
    int startAfterTimeStamp = 0;
    if (querySnapshot.docs.isNotEmpty) {
      attemptHistoryList = querySnapshot.docs.map(
        (doc) {
          Map attemptHistoryMap = doc.data() as Map<String, dynamic>;
          //print("document fetched: $attemptHistoryMap");
          //how to check if doc contain key?

          return AttemptHistoryEntry(
            uniqueId: doc.id,
            questionId: attemptHistoryMap['questionId'],
            subjectId: attemptHistoryMap['subjectId'],
            bundleId: attemptHistoryMap['bundleId'],
            shortDescription: attemptHistoryMap['shortDescription'] ??
                'Not available', //TODO-Remove once shortDescription is added for all questions
            attemptStatusList: [
              AttemptHistoryEntryStatus(
                attemptStatus:
                    parseAttemptStatus(attemptHistoryMap['attemptStatus']),
                timestamp: attemptHistoryMap['timeStamp'],
              )
            ],
          );
        },
      ).toList();
      //print('last docyument: ${querySnapshot.docs.last}');
      //get the timestamp of the last document

      var tempMap = querySnapshot.docs.last.data() as Map<String, dynamic>;
      //print('temp=$tempMap');
      startAfterTimeStamp = tempMap['timeStamp'].millisecondsSinceEpoch;
      print('startAfterTimeStamp: $startAfterTimeStamp');
      //['timestamp'].todate().millisecondsSinceEpoch;
    } else {
      attemptHistoryList = [];
    }
    //print("AW:attemptHistoryList= $attemptHistoryList");
    //convert attemptHistoryList to a map using attemptStatus.list[0].timestamp as key

    Map<DateTime, List<AttemptHistoryEntry>> attemptHistoryMap =
        groupBy(attemptHistoryList, getDateFromAttemptHistoryEntry);

    Map<String, List<AttemptHistoryEntry>> groupedData = {};
    //print("AW:attemptHistoryMap");
    //print attemptHistoryMap to see if it is grouped by date
    attemptHistoryMap.forEach((key, value) {
      //print("JB1:Check:HistoryMap= $key, value= $value");
    });
    attemptHistoryMap.forEach((attemptedDate, value) {
      //print("JB1:$attemptedDate, value= $value");
      groupBy(value, (p0) => p0.questionId).forEach((questionId, value) {
        AttemptHistoryEntry entry = value.first;
        //print("JB1:first entry= $entry");
        for (var element in value) {
          // print("JB1:element= $element");
          // print("JB1:element != value.first= ${element != value.first}");
          if (element != value.first) {
            // print(
            //     "JB1:element.attemptStatusList= ${element.attemptStatusList}");
            entry.attemptStatusList.addAll(element.attemptStatusList);
          }
        }
        String dateKey = lookUpKeyMapping(attemptedDate);
        groupedData.containsKey(dateKey)
            ? groupedData[dateKey]!.add(entry)
            : groupedData[dateKey] = [entry];
        //print("JB1:groupedData:Updated entry= $groupedData");
      });
    });

    //print("AW:groupedData= $groupedData");

    groupedData.forEach((key, value) {
      //print("JB1:Check:GroupedData= $key, value= $value");
    });

    AttemptHistoryModel data = AttemptHistoryModel(
      attemptHistoryMap: groupedData,
      attemptHistoryList: attemptHistoryList,
      startAfterTimeStamp: startAfterTimeStamp,
      isLastPage: attemptHistoryList.length <
          RepositoryConstants.defaultPaginationFetchSize,
    );
    print("Final AttemptHistoryData: $data");
    return data;
  }

  var getDateFromAttemptHistoryEntry = (AttemptHistoryEntry entry) {
    Timestamp timestamp = entry.attemptStatusList[0].timestamp;
    DateTime dateTime = timestamp.toDate();
    // Remove time component
    DateTime date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return date;
  };

  String lookUpKeyMapping(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);

    final inputDate = DateTime(date.year, date.month, date.day);

    if (inputDate == today) {
      return 'Today';
    } else if (inputDate == yesterday) {
      return 'Yesterday';
    } else {
      return formatDate(date);
    }
  }

  String formatDate(DateTime date) {
    return DateFormat('d MMM yyyy').format(date);
  }
}
