import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/attempt_history/domain/entities/attempt_history.dart';
import 'package:skillapp/src/attempt_history/domain/repos/attempt_history_repo.dart';

class FetchAttemptHistory
    implements
        FutureUsecaseWithParams<AttemptHistory, FetchAttemptHistoryParams> {
  final AttemptHistoryRepo _repository;

  FetchAttemptHistory({required AttemptHistoryRepo repository})
      : _repository = repository;

  @override
  ResultFuture<AttemptHistory> call(FetchAttemptHistoryParams params) =>
      _repository.getAttemptHistory(params.lastRecordTimestamp);
}

class FetchAttemptHistoryParams extends Equatable {
  final int lastRecordTimestamp;

  const FetchAttemptHistoryParams(this.lastRecordTimestamp);

  @override
  List<Object?> get props => [lastRecordTimestamp];
}
