import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:skillapp/core/enums/enum_master.dart';

class AttemptHistory {
  final List<AttemptHistoryEntry> attemptHistoryList;
  final int startAfterTimeStamp;
  final bool isLastPage;
  final Map<String, List<AttemptHistoryEntry>> attemptHistoryMap;

  const AttemptHistory({
    required this.attemptHistoryMap,
    required this.attemptHistoryList,
    required this.startAfterTimeStamp,
    required this.isLastPage,
  });

  const AttemptHistory.empty()
      : attemptHistoryMap = const {},
        attemptHistoryList = const [],
        startAfterTimeStamp = 0,
        isLastPage = false;

  //add toString method
  @override
  String toString() {
    return "AttemptHistoryData[startAfterTimeStamp=$startAfterTimeStamp,isLastPage=$isLastPage,attemptHistoryMap=$attemptHistoryMap]";
  }
}

class AttemptHistoryEntry {
  final String uniqueId;
  final String questionId;
  final String subjectId;
  final String shortDescription;
  final String bundleId;
  final List<AttemptHistoryEntryStatus> attemptStatusList;

  AttemptHistoryEntry({
    required this.uniqueId,
    required this.questionId,
    required this.subjectId,
    required this.shortDescription,
    required this.bundleId,
    required this.attemptStatusList,
  });

  /*@override
  String toString() {
    return "AttemptHistoryEntry[uniqueId=$uniqueId, questionId=$questionId, subjectId=$subjectId, shortDescription=$shortDescription, bundleId=$bundleId, attemptStatusList=$attemptStatusList]";
  }*/

  @override
  String toString() {
    return "Entry[$uniqueId, $questionId, $shortDescription, $attemptStatusList]";
  }
}

class AttemptHistoryEntryStatus {
  final AttemptStatus attemptStatus;
  final Timestamp timestamp;

  AttemptHistoryEntryStatus({
    required this.attemptStatus,
    required this.timestamp,
  });

  //generate tostring
  /*@override
  String toString() {
    return "AttemptHistoryEntryStatus[attemptStatus=$attemptStatus, timestamp=$timestamp]";
  }*/
  @override
  String toString() {
    return "Status[$attemptStatus]";
  }
}
