import 'package:dartz/dartz.dart';
import '../../data/models/test_configuration_model.dart';
import '../../data/repos/test_configuration_repository.dart';
import 'package:skillapp/core/errors/failures.dart';

class FetchTestConfiguration {
  final TestConfigurationRepository repository;

  FetchTestConfiguration({required this.repository});

  Future<Either<Failure, TestConfiguration>> call(String testId) async {
    try {
      final testConfiguration = await repository.getTestConfiguration(testId);
      return Right(testConfiguration);
    } catch (e) {
      return const Left(ServerFailure(
          message: "Unable to fetch test configuration", statusCode: 500));
    }
  }
}
