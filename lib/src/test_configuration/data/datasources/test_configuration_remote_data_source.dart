import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:skillapp/src/test_configuration/data/models/test_configuration_model.dart';

abstract class TestConfigurationRemoteDataSource {
  Future<TestConfiguration> fetchTestConfiguration(String testId);
}

class TestConfigurationRemoteDataSourceImpl
    implements TestConfigurationRemoteDataSource {
  final FirebaseFirestore _firestore;

  TestConfigurationRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  @override
  Future<TestConfiguration> fetchTestConfiguration(String testId) async {
    DocumentSnapshot<Map<String, dynamic>> doc =
        await _firestore.collection('tests').doc(testId).get();

    if (doc.exists) {
      return TestConfiguration.fromMap(doc.data()!);
    } else {
      throw Exception('Test configuration not found');
    }
  }
}
