class TestConfiguration {
  final String type;
  final List<String> subjects;
  final int maxAllowedTime;
  final int breakTimeAfter;
  final String subjectId;

  TestConfiguration({
    required this.type,
    required this.subjects,
    required this.maxAllowedTime,
    required this.breakTimeAfter,
    required this.subjectId,
  });

  factory TestConfiguration.fromMap(Map<String, dynamic> data) {
    return TestConfiguration(
      type: data['type'] as String,
      subjects: List<String>.from(data['subjects']),
      maxAllowedTime: data['max_allowed_time'] as int,
      breakTimeAfter: data['break_time_after'] as int,
      subjectId: data['subject_id'] as String,
    );
  }
}
