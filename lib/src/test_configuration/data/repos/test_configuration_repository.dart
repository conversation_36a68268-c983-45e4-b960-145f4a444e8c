import 'package:skillapp/src/test_configuration/data/models/test_configuration_model.dart';

import '../datasources/test_configuration_remote_data_source.dart';

abstract class TestConfigurationRepository {
  Future<TestConfiguration> getTestConfiguration(String testId);
}

class TestConfigurationRepositoryImpl implements TestConfigurationRepository {
  final TestConfigurationRemoteDataSource remoteDataSource;

  TestConfigurationRepositoryImpl({required this.remoteDataSource});

  @override
  Future<TestConfiguration> getTestConfiguration(String testId) async {
    return await remoteDataSource.fetchTestConfiguration(testId);
  }
}
