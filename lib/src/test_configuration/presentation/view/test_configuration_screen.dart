import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/src/test_configuration/domain/usecases/fetch_test_configuration.dart';
import 'package:skillapp/src/test_configuration/presentation/cubit/test_configuration_cubit.dart';

class TestConfigurationScreen extends StatelessWidget {
  final String testId;

  const TestConfigurationScreen({super.key, required this.testId});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TestConfigurationCubit(
        fetchTestConfiguration: context.read<FetchTestConfiguration>(),
      )..loadTestConfiguration(testId),
      child: Scaffold(
        appBar: AppBar(title: const Text('Test Configuration')),
        body: BlocBuilder<TestConfigurationCubit, TestConfigurationState>(
          builder: (context, state) {
            if (state is TestConfigurationLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is TestConfigurationLoaded) {
              return Center(
                  child: Text('Test Type: ${state.testConfiguration.type}'));
            } else if (state is TestConfigurationError) {
              return Center(child: Text(state.message));
            } else {
              return const Center(child: Text('Please wait...'));
            }
          },
        ),
      ),
    );
  }
}
