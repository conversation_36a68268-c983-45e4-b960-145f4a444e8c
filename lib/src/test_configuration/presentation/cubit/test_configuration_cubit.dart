import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/fetch_test_configuration.dart';
import '../../data/models/test_configuration_model.dart';

class TestConfigurationCubit extends Cubit<TestConfigurationState> {
  final FetchTestConfiguration fetchTestConfiguration;

  TestConfigurationCubit({required this.fetchTestConfiguration})
      : super(TestConfigurationInitial());

  Future<void> loadTestConfiguration(String testId) async {
    emit(TestConfigurationLoading());
    final result = await fetchTestConfiguration(testId);
    result.fold(
      (failure) =>
          emit(TestConfigurationError('Failed to load test configuration')),
      (testConfiguration) => emit(TestConfigurationLoaded(testConfiguration)),
    );
  }
}

abstract class TestConfigurationState {}

class TestConfigurationInitial extends TestConfigurationState {}

class TestConfigurationLoading extends TestConfigurationState {}

class TestConfigurationLoaded extends TestConfigurationState {
  final TestConfiguration testConfiguration;

  TestConfigurationLoaded(this.testConfiguration);
}

class TestConfigurationError extends TestConfigurationState {
  final String message;

  TestConfigurationError(this.message);
}
