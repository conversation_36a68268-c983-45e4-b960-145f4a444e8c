import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
//import 'dart:html' as html;
import '../../../../core/error/exceptions.dart';

class StripePaymentService {
  Future<String> createPaymentMethod(CardFieldInputDetails card) async {
    // This method is only used on mobile platforms
    if (kIsWeb) {
      throw PaymentException(
          message: 'Payment method creation not supported on web');
    }

    try {
      final paymentMethod = await Stripe.instance.createPaymentMethod(
        params: const PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(),
        ),
      );
      return paymentMethod.id;
    } catch (e) {
      throw PaymentException(
          message: 'Failed to create payment method: ${e.toString()}');
    }
  }

  Future<void> confirmPayment(String clientSecret) async {
    // This method is only used on mobile platforms
    if (kIsWeb) {
      throw PaymentException(
          message: 'Payment confirmation not supported on web');
    }

    try {
      await Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: const PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(),
        ),
      );
    } catch (e) {
      throw PaymentException(
          message: 'Payment confirmation failed: ${e.toString()}');
    }
  }

  Future<void> initStripe(String publishableKey) async {
    // Only initialize Stripe on mobile platforms
    if (!kIsWeb) {
      Stripe.publishableKey = publishableKey;
      await Stripe.instance.applySettings();
    }
    // For web, we'll use the Stripe Checkout directly via URL
  }

  /// Redirects to Stripe Checkout page for payment
  Future<void> redirectToCheckout({
    required String checkoutSessionUrl,
    required BuildContext context,
  }) async {
    try {
      final Uri url = Uri.parse(checkoutSessionUrl);

      if (kIsWeb) {
        //html.window.location.href = checkoutSessionUrl;
      } else {
        if (await canLaunchUrl(url)) {
          await launchUrl(url, mode: LaunchMode.externalApplication);
        } else {
          throw PaymentException(message: 'Could not launch checkout page');
        }
      }
    } catch (e) {
      throw PaymentException(
          message: 'Failed to redirect to checkout: ${e.toString()}');
    }
  }
}
