import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/subscription_plan_model.dart';
import '../models/subscription_model.dart';
import '../models/payment_method_model.dart';
import '../../../../core/error/exceptions.dart';

abstract class SubscriptionRemoteDataSource {
  Future<List<SubscriptionPlanModel>> getSubscriptionPlans();
  Future<SubscriptionModel> getCurrentSubscription();
  Future<List<PaymentMethodModel>> getPaymentMethods();
  Future<PaymentMethodModel> addPaymentMethod(String paymentMethodId);
  Future<SubscriptionModel> createSubscription(
      String planId, String paymentMethodId);
  Future<SubscriptionModel> cancelSubscription(String subscriptionId);
  Future<String> createPaymentIntent(String planId);
  Future<String> createCheckoutSession(String planId);
}

class SubscriptionRemoteDataSourceImpl implements SubscriptionRemoteDataSource {
  final http.Client client;
  final String baseUrl;

  SubscriptionRemoteDataSourceImpl({
    required this.client,
    required this.baseUrl,
  });

  @override
  Future<List<SubscriptionPlanModel>> getSubscriptionPlans() async {
    try {
      final response = await client.get(
        Uri.parse('$baseUrl/subscription/plans'),
        headers: {'Content-Type': 'application/json'},
      );

      print(
          "JB: getSubscriptionPlans:response.statusCode= ${response.statusCode}");

      if (response.statusCode == 200) {
        final List<dynamic> plansJson = json.decode(response.body);
        return plansJson
            .map((json) => SubscriptionPlanModel.fromJson(json))
            .toList();
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<SubscriptionModel> getCurrentSubscription() async {
    try {
      final response = await client.get(
        Uri.parse('$baseUrl/subscription/current'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return SubscriptionModel.fromJson(json.decode(response.body));
      } else if (response.statusCode == 404) {
        throw NotFoundException();
      } else {
        throw ServerException();
      }
    } catch (e) {
      if (e is NotFoundException) {
        rethrow;
      }
      throw ServerException();
    }
  }

  @override
  Future<List<PaymentMethodModel>> getPaymentMethods() async {
    try {
      final response = await client.get(
        Uri.parse('$baseUrl/payment-methods'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> methodsJson = json.decode(response.body);
        return methodsJson
            .map((json) => PaymentMethodModel.fromJson(json))
            .toList();
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<PaymentMethodModel> addPaymentMethod(String paymentMethodId) async {
    try {
      final response = await client.post(
        Uri.parse('$baseUrl/payment-methods'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'payment_method_id': paymentMethodId}),
      );

      if (response.statusCode == 201) {
        return PaymentMethodModel.fromJson(json.decode(response.body));
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<SubscriptionModel> createSubscription(
      String planId, String paymentMethodId) async {
    try {
      final response = await client.post(
        Uri.parse('$baseUrl/subscriptions'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'plan_id': planId,
          'payment_method_id': paymentMethodId,
        }),
      );

      if (response.statusCode == 201) {
        return SubscriptionModel.fromJson(json.decode(response.body));
      } else {
        final errorData = json.decode(response.body);
        throw PaymentException(
            message: errorData['error'] ?? 'Failed to create subscription');
      }
    } catch (e) {
      if (e is PaymentException) {
        rethrow;
      }
      throw ServerException();
    }
  }

  @override
  Future<SubscriptionModel> cancelSubscription(String subscriptionId) async {
    try {
      final response = await client.delete(
        Uri.parse('$baseUrl/subscriptions/$subscriptionId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return SubscriptionModel.fromJson(json.decode(response.body));
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<String> createPaymentIntent(String planId) async {
    try {
      final response = await client.post(
        Uri.parse('$baseUrl/payment-intents'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'plan_id': planId}),
      );

      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['client_secret'];
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }
  // this method should call the firebase function call
  /* @override
  Future<String> createCheckoutSession(String planId) async {
    try {
      final response = await client.post(
        Uri.parse('$baseUrl/checkout-sessions'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'plan_id': planId}),
      );

      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['checkout_url'];
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }*/

  @override
  Future<String> createCheckoutSession(String planId) async {
    final url = Uri.parse('https://api.stripe.com/v1/checkout/sessions');
    //  final String apiKey = "Api key from Stripe";

    const String apiKey =
        'sk_test_51RGa7aP1vpo5Sc3oz4R4q4zxHdhNEOm6iK8Pu9c6ovNLxiRjLsQcd2Kf6ob3u7fyPN4pekuNgvgttbpvusceSQTi00Hj7Fijcs';

    try {
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'success_url': 'https://localhost:3000/success',
          'line_items[0][price]': 'price_1RGaDDP1vpo5Sc3ov8bET6h8',
          'line_items[0][quantity]': '1',
          'mode': 'subscription',
          'customer_email': '<EMAIL>'
        },
      );

      if (response.statusCode == 200) {
        final sessionData = json.decode(response.body);
        print('checkout total response data: $sessionData');
        print('Checkout Session created: ${sessionData['id']}');

        return sessionData['url'];
        //  return "https://checkout.stripe.com/pay/${sessionData['id']}";
      } else {
        print('Error creating Checkout Session: ${response.body}');
        throw ServerException();
      }
    } catch (e) {
      print('Exception occurred: $e');
      throw ServerException();
    }
  }
}
