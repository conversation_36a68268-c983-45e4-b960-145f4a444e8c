import 'package:dartz/dartz.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/entities/subscription.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/repositories/subscription_repository.dart';
import '../datasources/subscription_remote_data_source.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/network_info.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  SubscriptionRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans() async {
    if (await networkInfo.isConnected) {
      try {
        final remotePlans = await remoteDataSource.getSubscriptionPlans();
        return Right(remotePlans);
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Subscription>> getCurrentSubscription() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteSubscription =
            await remoteDataSource.getCurrentSubscription();
        return Right(remoteSubscription);
      } on NotFoundException {
        return const Left(NotFoundFailure());
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteMethods = await remoteDataSource.getPaymentMethods();
        return Right(remoteMethods);
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentMethod>> addPaymentMethod(
      String paymentMethodId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteMethod =
            await remoteDataSource.addPaymentMethod(paymentMethodId);
        return Right(remoteMethod);
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Subscription>> createSubscription(
      String planId, String paymentMethodId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteSubscription =
            await remoteDataSource.createSubscription(planId, paymentMethodId);
        return Right(remoteSubscription);
      } on PaymentException catch (e) {
        return Left(PaymentFailure(message: e.message));
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Subscription>> cancelSubscription(
      String subscriptionId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteSubscription =
            await remoteDataSource.cancelSubscription(subscriptionId);
        return Right(remoteSubscription);
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String>> createPaymentIntent(String planId) async {
    if (await networkInfo.isConnected) {
      try {
        final clientSecret = await remoteDataSource.createPaymentIntent(planId);
        return Right(clientSecret);
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String>> createCheckoutSession(String planId) async {
    if (await networkInfo.isConnected) {
      try {
        final checkoutUrl =
            await remoteDataSource.createCheckoutSession(planId);
        return Right(checkoutUrl);
      } on ServerException {
        return const Left(ServerFailure());
      }
    } else {
      return const Left(NetworkFailure());
    }
  }
}
