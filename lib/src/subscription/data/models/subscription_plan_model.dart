import '../../domain/entities/subscription_plan.dart';

class SubscriptionPlanModel extends SubscriptionPlan {
  SubscriptionPlanModel({
    required super.id,
    required super.name,
    required super.price,
    required super.interval,
    required super.features,
    super.isActive,
  });

  factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanModel(
      id: json['id'],
      name: json['name'],
      price: (json['price'] as num).toDouble(),
      interval: json['interval'],
      features: List<String>.from(json['features']),
      isActive: json['is_active'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'interval': interval,
      'features': features,
      'is_active': isActive,
    };
  }
}
