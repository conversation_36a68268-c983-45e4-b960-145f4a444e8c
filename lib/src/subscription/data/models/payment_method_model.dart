import '../../domain/entities/payment_method.dart';

class PaymentMethodModel extends PaymentMethod {
  PaymentMethodModel({
    required super.id,
    required super.type,
    required super.last4,
    required super.brand,
    required super.expiryMonth,
    required super.expiryYear,
  });

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'],
      type: json['type'],
      last4: json['card']['last4'],
      brand: json['card']['brand'],
      expiryMonth: json['card']['exp_month'],
      expiryYear: json['card']['exp_year'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'card': {
        'last4': last4,
        'brand': brand,
        'exp_month': expiryMonth,
        'exp_year': expiryYear,
      },
    };
  }
}
