import 'package:dartz/dartz.dart';
import '../entities/subscription_plan.dart';
import '../entities/subscription.dart';
import '../entities/payment_method.dart';
import '../../../../core/error/failures.dart';

abstract class SubscriptionRepository {
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans();
  Future<Either<Failure, Subscription>> getCurrentSubscription();
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods();
  Future<Either<Failure, PaymentMethod>> addPaymentMethod(
      String paymentMethodId);
  Future<Either<Failure, Subscription>> createSubscription(
      String planId, String paymentMethodId);
  Future<Either<Failure, Subscription>> cancelSubscription(
      String subscriptionId);
  Future<Either<Failure, String>> createPaymentIntent(String planId);
  Future<Either<Failure, String>> createCheckoutSession(String planId);
}
