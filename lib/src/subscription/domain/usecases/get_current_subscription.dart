import 'package:dartz/dartz.dart';
import '../repositories/subscription_repository.dart';
import '../entities/subscription.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class GetCurrentSubscription implements UseCase<Subscription, NoParams> {
  final SubscriptionRepository repository;

  GetCurrentSubscription(this.repository);

  @override
  Future<Either<Failure, Subscription>> call(NoParams params) {
    return repository.getCurrentSubscription();
  }
}
