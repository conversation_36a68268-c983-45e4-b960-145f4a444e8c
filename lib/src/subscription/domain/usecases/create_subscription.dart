import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../repositories/subscription_repository.dart';
import '../entities/subscription.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class CreateSubscriptionParams extends Equatable {
  final String planId;
  final String paymentMethodId;

  const CreateSubscriptionParams({
    required this.planId,
    required this.paymentMethodId,
  });

  @override
  List<Object> get props => [planId, paymentMethodId];
}

class CreateSubscription implements UseCase<Subscription, CreateSubscriptionParams> {
  final SubscriptionRepository repository;

  CreateSubscription(this.repository);

  @override
  Future<Either<Failure, Subscription>> call(CreateSubscriptionParams params) {
    return repository.createSubscription(params.planId, params.paymentMethodId);
  }
}
