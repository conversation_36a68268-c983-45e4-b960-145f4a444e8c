import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../repositories/subscription_repository.dart';
import '../entities/subscription.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class CancelSubscriptionParams extends Equatable {
  final String subscriptionId;

  const CancelSubscriptionParams({required this.subscriptionId});

  @override
  List<Object> get props => [subscriptionId];
}

class CancelSubscription implements UseCase<Subscription, CancelSubscriptionParams> {
  final SubscriptionRepository repository;

  CancelSubscription(this.repository);

  @override
  Future<Either<Failure, Subscription>> call(CancelSubscriptionParams params) {
    return repository.cancelSubscription(params.subscriptionId);
  }
}
