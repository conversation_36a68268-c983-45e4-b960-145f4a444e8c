import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../repositories/subscription_repository.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class CreatePaymentIntentParams extends Equatable {
  final String planId;

  const CreatePaymentIntentParams({required this.planId});

  @override
  List<Object> get props => [planId];
}

class CreatePaymentIntent implements UseCase<String, CreatePaymentIntentParams> {
  final SubscriptionRepository repository;

  CreatePaymentIntent(this.repository);

  @override
  Future<Either<Failure, String>> call(CreatePaymentIntentParams params) {
    return repository.createPaymentIntent(params.planId);
  }
}
