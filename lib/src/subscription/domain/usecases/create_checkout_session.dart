import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../repositories/subscription_repository.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class CreateCheckoutSessionParams extends Equatable {
  final String planId;

  const CreateCheckoutSessionParams({required this.planId});

  @override
  List<Object> get props => [planId];
}

class CreateCheckoutSession implements UseCase<String, CreateCheckoutSessionParams> {
  final SubscriptionRepository repository;

  CreateCheckoutSession(this.repository);

  @override
  Future<Either<Failure, String>> call(CreateCheckoutSessionParams params) {
    return repository.createCheckoutSession(params.planId);
  }
}
