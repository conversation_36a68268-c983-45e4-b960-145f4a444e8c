import 'package:dartz/dartz.dart';
import '../repositories/subscription_repository.dart';
import '../entities/subscription_plan.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class GetSubscriptionPlans implements UseCase<List<SubscriptionPlan>, NoParams> {
  final SubscriptionRepository repository;

  GetSubscriptionPlans(this.repository);

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> call(NoParams params) {
    return repository.getSubscriptionPlans();
  }
}
