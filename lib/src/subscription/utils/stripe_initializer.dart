import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class StripeInitializer {
  static Future<void> initialize() async {
    // Replace with your Stripe publishable key
    const publishableKey = 'pk_test_your_publishable_key';

    // Only initialize Stripe on mobile platforms
    if (!kIsWeb) {
      Stripe.publishableKey = publishableKey;
      await Stripe.instance.applySettings();
    }
    // For web, we'll use the Stripe Checkout directly via URL
  }
}
