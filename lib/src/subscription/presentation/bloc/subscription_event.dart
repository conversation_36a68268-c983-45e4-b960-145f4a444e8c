import 'package:equatable/equatable.dart';

abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object> get props => [];
}

class LoadSubscriptionPlans extends SubscriptionEvent {}

class LoadCurrentSubscription extends SubscriptionEvent {}

class LoadPaymentMethods extends SubscriptionEvent {}

class AddPaymentMethod extends SubscriptionEvent {
  final String paymentMethodId;

  const AddPaymentMethod(this.paymentMethodId);

  @override
  List<Object> get props => [paymentMethodId];
}

class CreateSubscriptionEvent extends SubscriptionEvent {
  final String planId;
  final String paymentMethodId;

  const CreateSubscriptionEvent({
    required this.planId,
    required this.paymentMethodId,
  });

  @override
  List<Object> get props => [planId, paymentMethodId];
}

class CancelSubscriptionEvent extends SubscriptionEvent {
  final String subscriptionId;

  const CancelSubscriptionEvent(this.subscriptionId);

  @override
  List<Object> get props => [subscriptionId];
}

class CreatePaymentIntentEvent extends SubscriptionEvent {
  final String planId;

  const CreatePaymentIntentEvent(this.planId);

  @override
  List<Object> get props => [planId];
}

class CreateCheckoutSessionEvent extends SubscriptionEvent {
  final String planId;

  const CreateCheckoutSessionEvent(this.planId);

  @override
  List<Object> get props => [planId];
}
