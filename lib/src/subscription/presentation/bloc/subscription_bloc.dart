import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_subscription_plans.dart';
import '../../domain/usecases/get_current_subscription.dart';
import '../../domain/usecases/create_subscription.dart';
import '../../domain/usecases/cancel_subscription.dart';
import '../../domain/usecases/create_payment_intent.dart';
import '../../domain/usecases/create_checkout_session.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/error/failures.dart';
import 'subscription_event.dart';
import 'subscription_state.dart';

class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final GetSubscriptionPlans getSubscriptionPlans;
  final GetCurrentSubscription getCurrentSubscription;
  final CreateSubscription createSubscription;
  final CancelSubscription cancelSubscription;
  final CreatePaymentIntent createPaymentIntent;
  final CreateCheckoutSession createCheckoutSession;

  SubscriptionBloc({
    required this.getSubscriptionPlans,
    required this.getCurrentSubscription,
    required this.createSubscription,
    required this.cancelSubscription,
    required this.createPaymentIntent,
    required this.createCheckoutSession,
  }) : super(SubscriptionInitial()) {
    on<LoadSubscriptionPlans>(_onLoadSubscriptionPlans);
    on<LoadCurrentSubscription>(_onLoadCurrentSubscription);
    on<CreateSubscriptionEvent>(_onCreateSubscription);
    on<CancelSubscriptionEvent>(_onCancelSubscription);
    on<CreatePaymentIntentEvent>(_onCreatePaymentIntent);
    on<CreateCheckoutSessionEvent>(_onCreateCheckoutSession);
  }

  Future<void> _onLoadSubscriptionPlans(
    LoadSubscriptionPlans event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SubscriptionPlansLoading());
    final result = await getSubscriptionPlans(NoParams());
    result.fold(
      (failure) => emit(
          const SubscriptionPlansError('Failed to load subscription plans')),
      (plans) => emit(SubscriptionPlansLoaded(plans)),
    );
  }

  Future<void> _onLoadCurrentSubscription(
    LoadCurrentSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CurrentSubscriptionLoading());
    final result = await getCurrentSubscription(NoParams());
    result.fold(
      (failure) {
        if (failure is NotFoundFailure) {
          emit(NoActiveSubscription());
        } else {
          emit(const CurrentSubscriptionError(
              'Failed to load current subscription'));
        }
      },
      (subscription) => emit(CurrentSubscriptionLoaded(subscription)),
    );
  }

  Future<void> _onCreateSubscription(
    CreateSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CreatingSubscription());
    final result = await createSubscription(
      CreateSubscriptionParams(
        planId: event.planId,
        paymentMethodId: event.paymentMethodId,
      ),
    );
    result.fold(
      (failure) {
        if (failure is PaymentFailure) {
          emit(CreateSubscriptionError(failure.message));
        } else {
          emit(const CreateSubscriptionError('Failed to create subscription'));
        }
      },
      (subscription) => emit(SubscriptionCreated(subscription)),
    );
  }

  Future<void> _onCancelSubscription(
    CancelSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CancellingSubscription());
    final result = await cancelSubscription(
      CancelSubscriptionParams(subscriptionId: event.subscriptionId),
    );
    result.fold(
      (failure) =>
          emit(const CancelSubscriptionError('Failed to cancel subscription')),
      (subscription) => emit(SubscriptionCancelled(subscription)),
    );
  }

  Future<void> _onCreatePaymentIntent(
    CreatePaymentIntentEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CreatingPaymentIntent());
    final result = await createPaymentIntent(
      CreatePaymentIntentParams(planId: event.planId),
    );
    result.fold(
      (failure) => emit(
          const CreatePaymentIntentError('Failed to create payment intent')),
      (clientSecret) => emit(PaymentIntentCreated(clientSecret)),
    );
  }

  Future<void> _onCreateCheckoutSession(
    CreateCheckoutSessionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CreatingCheckoutSession());
    final result = await createCheckoutSession(
      CreateCheckoutSessionParams(planId: event.planId),
    );
    result.fold(
      (failure) => emit(const CreateCheckoutSessionError(
          'Failed to create checkout session')),
      (checkoutUrl) => emit(CheckoutSessionCreated(checkoutUrl)),
    );
  }
}
