import 'package:equatable/equatable.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/entities/subscription.dart';
import '../../domain/entities/payment_method.dart';

abstract class SubscriptionState extends Equatable {
  const SubscriptionState();

  @override
  List<Object?> get props => [];
}

class SubscriptionInitial extends SubscriptionState {}

class SubscriptionPlansLoading extends SubscriptionState {}

class SubscriptionPlansLoaded extends SubscriptionState {
  final List<SubscriptionPlan> plans;

  const SubscriptionPlansLoaded(this.plans);

  @override
  List<Object> get props => [plans];
}

class SubscriptionPlansError extends SubscriptionState {
  final String message;

  const SubscriptionPlansError(this.message);

  @override
  List<Object> get props => [message];
}

class CurrentSubscriptionLoading extends SubscriptionState {}

class CurrentSubscriptionLoaded extends SubscriptionState {
  final Subscription subscription;

  const CurrentSubscriptionLoaded(this.subscription);

  @override
  List<Object> get props => [subscription];
}

class NoActiveSubscription extends SubscriptionState {}

class CurrentSubscriptionError extends SubscriptionState {
  final String message;

  const CurrentSubscriptionError(this.message);

  @override
  List<Object> get props => [message];
}

class PaymentMethodsLoading extends SubscriptionState {}

class PaymentMethodsLoaded extends SubscriptionState {
  final List<PaymentMethod> paymentMethods;

  const PaymentMethodsLoaded(this.paymentMethods);

  @override
  List<Object> get props => [paymentMethods];
}

class PaymentMethodsError extends SubscriptionState {
  final String message;

  const PaymentMethodsError(this.message);

  @override
  List<Object> get props => [message];
}

class AddingPaymentMethod extends SubscriptionState {}

class PaymentMethodAdded extends SubscriptionState {
  final PaymentMethod paymentMethod;

  const PaymentMethodAdded(this.paymentMethod);

  @override
  List<Object> get props => [paymentMethod];
}

class AddPaymentMethodError extends SubscriptionState {
  final String message;

  const AddPaymentMethodError(this.message);

  @override
  List<Object> get props => [message];
}

class CreatingSubscription extends SubscriptionState {}

class SubscriptionCreated extends SubscriptionState {
  final Subscription subscription;

  const SubscriptionCreated(this.subscription);

  @override
  List<Object> get props => [subscription];
}

class CreateSubscriptionError extends SubscriptionState {
  final String message;

  const CreateSubscriptionError(this.message);

  @override
  List<Object> get props => [message];
}

class CancellingSubscription extends SubscriptionState {}

class SubscriptionCancelled extends SubscriptionState {
  final Subscription subscription;

  const SubscriptionCancelled(this.subscription);

  @override
  List<Object> get props => [subscription];
}

class CancelSubscriptionError extends SubscriptionState {
  final String message;

  const CancelSubscriptionError(this.message);

  @override
  List<Object> get props => [message];
}

class CreatingPaymentIntent extends SubscriptionState {}

class PaymentIntentCreated extends SubscriptionState {
  final String clientSecret;

  const PaymentIntentCreated(this.clientSecret);

  @override
  List<Object> get props => [clientSecret];
}

class CreatePaymentIntentError extends SubscriptionState {
  final String message;

  const CreatePaymentIntentError(this.message);

  @override
  List<Object> get props => [message];
}

class CreatingCheckoutSession extends SubscriptionState {}

class CheckoutSessionCreated extends SubscriptionState {
  final String checkoutUrl;

  const CheckoutSessionCreated(this.checkoutUrl);

  @override
  List<Object> get props => [checkoutUrl];
}

class CreateCheckoutSessionError extends SubscriptionState {
  final String message;

  const CreateCheckoutSessionError(this.message);

  @override
  List<Object> get props => [message];
}
