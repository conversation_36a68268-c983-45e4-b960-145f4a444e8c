import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../bloc/subscription_bloc.dart';
import '../bloc/subscription_event.dart';
import '../bloc/subscription_state.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../data/services/stripe_payment_service.dart';

class SubscriptionPlanPage extends StatefulWidget {
  const SubscriptionPlanPage({super.key});

  // Factory constructor for route builder
  factory SubscriptionPlanPage.routeBuilder(_, __) {
    return const SubscriptionPlanPage();
  }

  @override
  State<SubscriptionPlanPage> createState() => _SubscriptionPlanPageState();
}

class _SubscriptionPlanPageState extends State<SubscriptionPlanPage> {
  @override
  void initState() {
    super.initState();
    // Load subscription plans when the page is initialized
    context.read<SubscriptionBloc>().add(LoadSubscriptionPlans());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SubscriptionBloc, SubscriptionState>(
      builder: (context, state) {
        /*  if (state is SubscriptionPlansLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is SubscriptionPlansError) {
          return Center(child: Text('Error: ${state.message}'));
        } else if (state is SubscriptionPlansLoaded) {*/
        // return _buildContent(context, state.plans);
        return _buildContent(context, []);
        /* } else {
          // Initial state or any other state
          return _buildContent(context, []);
        }*/
      },
    );
  }

  Widget _buildContent(BuildContext context, List<SubscriptionPlan> plans) {
    // If we have plans from the API, use them, otherwise use hardcoded plans

    final bool useApiPlans = plans.isNotEmpty;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(40),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Text and features
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section
                const Text(
                  'Choose A Plan',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFD75D9D), // Pink color from screenshot
                  ),
                ),
                const Text(
                  'That Works For You',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFD75D9D), // Pink color from screenshot
                  ),
                ),
                const SizedBox(height: 16),

                // Subheader text
                const SizedBox(
                  width: 500,
                  child: Text(
                    'Unlock your full potential with plans designed to match your learning needs, whether you\'re just getting started or preparing to excel, we\'ve got it covered',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                      height: 1.5,
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Features list
                _buildFeatureItem('Exclusive Access'),
                const SizedBox(height: 16),
                _buildFeatureItem('Performance Insights'),
                const SizedBox(height: 16),
                _buildFeatureItem('24/7 Availability'),
                const SizedBox(height: 16),
                _buildFeatureItem('Adaptive Learning'),
                const SizedBox(height: 16),
                _buildFeatureItem('Early Updates'),
              ],
            ),
          ),

          const SizedBox(width: 40),

          // Right side - Subscription plans
          Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: useApiPlans
                  ? _buildPlansFromApi(plans)
                  : _buildHardcodedPlans(),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildPlansFromApi(List<SubscriptionPlan> plans) {
    return plans.map((plan) {
      final bool isPremium = plan.price > 0;
      return Expanded(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: _buildPlanCard(
            title: plan.name,
            price: isPremium ? '\$${plan.price}' : 'Free',
            subtitle: isPremium ? '/${plan.interval}' : null,
            features: plan.features,
            buttonText: isPremium ? 'Purchase Plan' : 'Continue with Free',
            isPremium: isPremium,
            imagePath:
                'assets/images/subscription/${isPremium ? 'premium' : 'free'}_plan.jpg',
            onButtonPressed: () => _handlePlanSelection(plan),
          ),
        ),
      );
    }).toList();
  }

  List<Widget> _buildHardcodedPlans() {
    return [
      // Free plan
      Expanded(
        child: _buildPlanCard(
          title: 'Free Plan',
          price: 'Free',
          features: [
            'Daily 5 question for each subject',
            'One Daily test',
            'One sectional mock test',
          ],
          buttonText: 'Continue with Free',
          isPremium: false,
          imagePath: 'assets/images/subscription/free_plan.jpg',
          onButtonPressed: () => _handleFreePlanSelection(),
        ),
      ),

      const SizedBox(width: 24),

      // Premium plan
      Expanded(
        child: _buildPlanCard(
          title: 'Monthly Subscription',
          price: '14.99\$',
          subtitle: '/month',
          discountText: '50% Discount',
          features: [
            'Daily 25 question for each subject',
            'One Daily test',
            'One sectional mock test',
            'Monthly Released full Mock Test (Unlimited Retakes)',
          ],
          buttonText: 'Purchase Plan',
          isPremium: true,
          imagePath: 'assets/images/subscription/premium_plan.jpg',
          onButtonPressed: () => _handlePremiumPlanSelection(),
        ),
      ),
    ];
  }

  void _handlePlanSelection(SubscriptionPlan plan) {
    if (plan.price > 0) {
      // Premium plan - use Stripe Checkout
      final stripeService = GetIt.instance<StripePaymentService>();

      // Create a local variable for the subscription
      late final StreamSubscription<SubscriptionState> subscription;

      // Initialize the subscription
      subscription = context.read<SubscriptionBloc>().stream.listen((state) {
        if (!mounted) {
          subscription.cancel();
          return;
        }

        if (state is CheckoutSessionCreated) {
          // Redirect to Stripe Checkout
          stripeService
              .redirectToCheckout(
            checkoutSessionUrl: state.checkoutUrl,
            context: context,
          )
              .catchError((error) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: ${error.toString()}')),
              );
            }
          });
          subscription.cancel();
        } else if (state is CreateCheckoutSessionError) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
          subscription.cancel();
        }
      });

      // Add the event after setting up the listener
      context.read<SubscriptionBloc>().add(
            CreateCheckoutSessionEvent(plan.id),
          );
    } else {
      // Free plan - activate immediately
      _handleFreePlanSelection();
    }
  }

  void _handleFreePlanSelection() {
    // For free plan, just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Free plan activated!')),
    );
  }

  void _handlePremiumPlanSelection() {
    // Use Stripe Checkout for premium plan
    final stripeService = GetIt.instance<StripePaymentService>();

    // Create a local variable for the subscription
    late final StreamSubscription<SubscriptionState> subscription;

    // Initialize the subscription
    subscription = context.read<SubscriptionBloc>().stream.listen((state) {
      if (!mounted) {
        subscription.cancel();
        return;
      }

      if (state is CheckoutSessionCreated) {
        // Redirect to Stripe Checkout
        stripeService
            .redirectToCheckout(
          checkoutSessionUrl: state.checkoutUrl,
          context: context,
        )
            .catchError((error) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${error.toString()}')),
            );
          }
        });
        subscription.cancel();
      } else if (state is CreateCheckoutSessionError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
        subscription.cancel();
      }
    });

    // Add the event after setting up the listener
    context.read<SubscriptionBloc>().add(
          const CreateCheckoutSessionEvent('premium_monthly'),
        );
  }

  Widget _buildPlanCard({
    required String title,
    required String price,
    String? subtitle,
    String? discountText,
    required List<String> features,
    required String buttonText,
    required bool isPremium,
    required String imagePath,
    required VoidCallback onButtonPressed,
  }) {
    return Container(
      width: 350,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Plan image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            child: Image.asset(
              imagePath,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  width: double.infinity,
                  color: isPremium
                      ? Colors.purple.withAlpha(50)
                      : Colors.blue.withAlpha(50),
                  child: Center(
                    child: Text(
                      isPremium ? 'Premium Plan' : 'Free Plan',
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                );
              },
            ),
          ),

          // Plan title badge
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isPremium ? Colors.white : Colors.purple.withAlpha(25),
                borderRadius: BorderRadius.circular(16),
                border: isPremium
                    ? Border.all(color: Colors.purple, width: 1)
                    : null,
              ),
              child: Text(
                title,
                style: TextStyle(
                  color: isPremium ? Colors.purple : Colors.black87,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
          ),

          // Price section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  price,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (subtitle != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 4.0, bottom: 4.0),
                    child: Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                if (discountText != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        discountText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Features list
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: features.map((feature) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.check_circle_outline,
                        color: Colors.black87,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          feature,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),

          // Button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onButtonPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isPremium ? Colors.purple : Colors.white,
                  foregroundColor: isPremium ? Colors.white : Colors.purple,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                    side: BorderSide(
                      color: Colors.purple,
                      width: isPremium ? 0 : 1,
                    ),
                  ),
                ),
                child: Text(
                  buttonText,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isPremium ? Colors.white : Colors.purple,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.green.withAlpha(25),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.check,
            color: Colors.green,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}
