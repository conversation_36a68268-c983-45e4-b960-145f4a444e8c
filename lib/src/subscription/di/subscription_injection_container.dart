import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import '../data/datasources/subscription_remote_data_source.dart';
import '../data/repositories/subscription_repository_impl.dart';
import '../data/services/stripe_payment_service.dart';
import '../domain/repositories/subscription_repository.dart';
import '../domain/usecases/get_subscription_plans.dart';
import '../domain/usecases/get_current_subscription.dart';
import '../domain/usecases/create_subscription.dart';
import '../domain/usecases/cancel_subscription.dart';
import '../domain/usecases/create_payment_intent.dart';
import '../domain/usecases/create_checkout_session.dart';
import '../presentation/bloc/subscription_bloc.dart';
import '../../../core/network/network_info.dart';

final sl = GetIt.instance;

Future<void> initSubscriptionDependencies() async {
  // BLoC
  sl.registerFactory(
    () => SubscriptionBloc(
      getSubscriptionPlans: sl(),
      getCurrentSubscription: sl(),
      createSubscription: sl(),
      cancelSubscription: sl(),
      createPaymentIntent: sl(),
      createCheckoutSession: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetSubscriptionPlans(sl()));
  sl.registerLazySingleton(() => GetCurrentSubscription(sl()));
  sl.registerLazySingleton(() => CreateSubscription(sl()));
  sl.registerLazySingleton(() => CancelSubscription(sl()));
  sl.registerLazySingleton(() => CreatePaymentIntent(sl()));
  sl.registerLazySingleton(() => CreateCheckoutSession(sl()));

  // Repository
  sl.registerLazySingleton<SubscriptionRepository>(
    () => SubscriptionRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<SubscriptionRemoteDataSource>(
    () => SubscriptionRemoteDataSourceImpl(
      client: sl(),
      baseUrl: 'https://api.stripe.com/v1', // Replace with your actual API URL
    ),
  );

  // Services
  sl.registerLazySingleton(() => StripePaymentService());

  // External
  if (!sl.isRegistered<http.Client>()) {
    sl.registerLazySingleton(() => http.Client());
  }

  if (!sl.isRegistered<NetworkInfo>()) {
    sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());
  }
}
