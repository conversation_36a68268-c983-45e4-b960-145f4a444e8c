import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class BackEndTestingSupportcreen extends StatefulWidget {
  const BackEndTestingSupportcreen({super.key});

  factory BackEndTestingSupportcreen.routeBuilder(
      BuildContext context, GoRouterState state) {
    return const BackEndTestingSupportcreen();
  }

  @override
  State<BackEndTestingSupportcreen> createState() =>
      _BackEndTestingSupportcreenState();
}

class _BackEndTestingSupportcreenState
    extends State<BackEndTestingSupportcreen> {
  String someData = "No data";
  String testAttemptId = 'AttemptId';

  @override
  void initState() {
    super.initState();
  }

  Future<void> _doAction() async {
    context
        .read<TestsBloc>()
        .add(const FetchTestDataEvent(testId: 'MATHS_T001'));
  }

  Future<void> _clearState() async {
    context.read<TestsBloc>().add(ClearDataUnitTesting());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Test Your Feature')),
      body: Center(
        child: SingleChildScrollView(
          child: Wrap(
            spacing: 10, // Horizontal spacing between buttons
            runSpacing: 10, // Vertical spacing between rows
            alignment: WrapAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(const FetchNewSectionalTestEvent(subject: 'maths'));
                },
                child: const Text('FetchNewSectionalTestEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(const FetchTodaysDailyTestEvent());
                },
                child: const Text('FetchToday\'s Test'),
              ),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(const FetchTestDataEvent(testId: 'MATHS_T001'));
                },
                child: const Text('FetchTestDataEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(const MarkTestAsStartedEvent(
                        testAttemptId: 'MATHS_T001',
                        testType: TestTypes.daily,
                      ));
                },
                child: const Text('MarkTestAsStartedEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(const FetchNextQuestionEvent(currentOrder: 0));
                },
                child: const Text('FetchNextQuestionEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(const FetchPreviousQuestionEvent(currentOrder: 3));
                },
                child: const Text('FetchPreviousQuestionEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(const AnswerAQuestionEvent(
                        questionId: 'MATHS02',
                        selectedAnswer: 'A',
                      ));
                },
                child: const Text('AnswerAQuestionEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(const FlagAQuestionEvent(questionId: 'MATHS04'));
                },
                child: const Text('FlagAQuestionEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(ViewFlaggedQuestionsEvent());
                },
                child: const Text('ViewFlaggedQuestionsEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(
                      const ViewSelectedQuestionEvent(questionId: 'MATHS02'));
                },
                child: const Text('ViewSelectedQuestionEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(SubmitTestEvent());
                },
                child: const Text('SubmitTestEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(FetchTestAttemptStatusEvent(
                      testAttemptId: testAttemptId,
                      testType: TestTypes.sectional));
                },
                child: const Text('FetchSectionalTestAttemptStatusEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(FetchTestAttemptDetailsEvent(
                      testAttemptId: testAttemptId,
                      testType: TestTypes.sectional));
                },
                child: const Text('FetchSectionalTestAttemptDetailsEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(
                      const FetchTestAttemptHistoryEvent(
                          testType: TestTypes.sectional));
                },
                child: const Text('FetchSectionalTestAttemptHistoryEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(
                      const FetchGroupedTestAttemptHistoryEvent(
                          testType: TestTypes.sectional));
                },
                child:
                    const Text('FetchGroupedSectionalTestAttemptHistoryEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Use a sample test attempt ID - in real usage, this would come from user selection
                  String sampleTestAttemptId =
                      'e81daa68-8629-4d2b-b5b2-73983bd98216';
                  context.read<TestsBloc>().add(FetchTestAttemptAnalysisEvent(
                      testAttemptId: sampleTestAttemptId,
                      testType: TestTypes.sectional));
                },
                child: const Text('FetchTestAttemptAnalysisEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Use a sample test attempt ID - in real usage, this would come from user selection
                  String sampleTestAttemptId =
                      'e81daa68-8629-4d2b-b5b2-73983bd98216';
                  context.read<TestsBloc>().add(FetchTestResultSummaryEvent(
                      testAttemptId: sampleTestAttemptId,
                      testType: TestTypes.sectional));
                },
                child: const Text('FetchTestResultSummaryEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Use a sample test attempt ID - in real usage, this would come from user selection
                  String sampleTestAttemptId =
                      'e81daa68-8629-4d2b-b5b2-73983bd98216';
                  context.read<TestsBloc>().add(
                      FetchTestQuestionsWithAnswerStatusEvent(
                          testAttemptId: sampleTestAttemptId,
                          testType: TestTypes.sectional));
                },
                child: const Text('FetchTestQuestionsWithAnswerStatusEvent'),
              ),
              ElevatedButton(
                onPressed: () {
                  context.read<TestsBloc>().add(ViewTestProgressEvent());
                },
                child: const Text('ViewTestProgressEvent'),
              ),
              ElevatedButton(
                onPressed: _clearState,
                child: const Text('Clear State'),
              ),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<TestsBloc>()
                      .add(CheckIsDailyTestAttemptedEvent());
                },
                child: const Text('Check Is Daily Test Attempted'),
              ),
              BlocBuilder<TestsBloc, TestsStateInitial>(
                builder: (context, state) {
                  if (state is TestsDataState) {
                    testAttemptId = state.testAttemptId;
                    return Text('TestsDataState =$state');
                  } else if (state
                      is FetchGroupedSectionalTestAttemptHistoryState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            'Grouped Test Attempt History (${state.groupedTestAttemptHistory.length} groups):'),
                        ...state.groupedTestAttemptHistory.map(
                          (group) => Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    'Test: ${group.testName} (${group.subject})'),
                                Text('Total Attempts: ${group.totalAttempts}'),
                                Text(
                                    'Latest: ${group.latestAttempt.scorePercentage}% on ${group.latestAttempt.attemptDate}'),
                                if (group.previousAttempts.isNotEmpty)
                                  Text(
                                      'Previous: ${group.previousAttempts.length} attempts'),
                                const Divider(),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  } else if (state is FetchTestAttemptAnalysisState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Test Attempt Analysis:'),
                        Text('Test: ${state.testAttemptAnalysis.testName}'),
                        Text('Subject: ${state.testAttemptAnalysis.subject}'),
                        Text(
                            'Overall Accuracy: ${state.testAttemptAnalysis.overallAccuracy.toStringAsFixed(1)}%'),
                        const SizedBox(height: 16),
                        Text('Module Analysis:'),
                        ...state.testAttemptAnalysis.moduleAnalysis.map(
                          (module) => Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Container(
                              padding: const EdgeInsets.all(12.0),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Question Type: ${module.questionType}',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold)),
                                  Text(
                                      'Total Questions: ${module.totalQuestions}'),
                                  Text('Correct: ${module.correctAnswers}'),
                                  Text('Incorrect: ${module.incorrectAnswers}'),
                                  Text(
                                      'Unattempted: ${module.unattemptedQuestions}'),
                                  Text(
                                      'Accuracy: ${module.accuracy.toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        color: module.accuracy >= 80
                                            ? Colors.green
                                            : module.accuracy >= 60
                                                ? Colors.orange
                                                : Colors.red,
                                        fontWeight: FontWeight.bold,
                                      )),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  } else if (state is FetchTestResultSummaryState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Test Result Summary:',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 16),

                        // Top section with percentage and time
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(16.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text('Total Percentage',
                                        style: TextStyle(color: Colors.grey)),
                                    Text(
                                        '${state.testResultSummary.totalPercentage.toStringAsFixed(1)}%',
                                        style: const TextStyle(
                                            fontSize: 24,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.blue)),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(16.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text('Time Spent',
                                        style: TextStyle(color: Colors.grey)),
                                    Text(state.testResultSummary.timeSpent,
                                        style: const TextStyle(
                                            fontSize: 24,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.blue)),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Test Result section
                        const Text('Test Result',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),

                        Container(
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: const BoxDecoration(
                                      color: Colors.green,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Expanded(
                                      child: Text('Correct Answers')),
                                  Text(
                                      '${state.testResultSummary.correctAnswers}',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold)),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: const BoxDecoration(
                                      color: Colors.orange,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Expanded(
                                      child: Text('Incorrect Answers')),
                                  Text(
                                      '${state.testResultSummary.incorrectAnswers}',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold)),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: const BoxDecoration(
                                      color: Colors.grey,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Expanded(
                                      child: Text('Unanswered answers')),
                                  Text(
                                      '${state.testResultSummary.unansweredAnswers}',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold)),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),
                        Text('Test: ${state.testResultSummary.testName}'),
                        Text('Subject: ${state.testResultSummary.subject}'),
                        Text(
                            'Total Questions: ${state.testResultSummary.totalQuestions}'),
                      ],
                    );
                  } else if (state is FetchTestQuestionsWithAnswerStatusState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Test Questions with Answer Status:',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 16),
                        Text(
                            'Test: ${state.testQuestionsWithAnswerStatus.testName}'),
                        Text(
                            'Subject: ${state.testQuestionsWithAnswerStatus.subject}'),
                        Text(
                            'Total Questions: ${state.testQuestionsWithAnswerStatus.totalQuestions}'),
                        Text(
                            'Correct: ${state.testQuestionsWithAnswerStatus.correctAnswers}'),
                        Text(
                            'Incorrect: ${state.testQuestionsWithAnswerStatus.incorrectAnswers}'),
                        Text(
                            'Unanswered: ${state.testQuestionsWithAnswerStatus.unansweredQuestions}'),
                        const SizedBox(height: 16),
                        const Text('Questions:',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        ...state.testQuestionsWithAnswerStatus.questions.map(
                          (question) => Container(
                            margin: const EdgeInsets.only(bottom: 8.0),
                            padding: const EdgeInsets.all(12.0),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8.0),
                              color: question.answerStatus ==
                                      QuestionAnswerStatus.correct
                                  ? Colors.green.withOpacity(0.1)
                                  : question.answerStatus ==
                                          QuestionAnswerStatus.incorrect
                                      ? Colors.red.withOpacity(0.1)
                                      : Colors.grey.withOpacity(0.1),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text('Q${question.questionOrder}: ',
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    Expanded(
                                        child: Text(question.shortDescription)),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: question.answerStatus ==
                                                QuestionAnswerStatus.correct
                                            ? Colors.green
                                            : question.answerStatus ==
                                                    QuestionAnswerStatus
                                                        .incorrect
                                                ? Colors.red
                                                : Colors.grey,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        question.answerStatus ==
                                                QuestionAnswerStatus.correct
                                            ? 'Correct'
                                            : question.answerStatus ==
                                                    QuestionAnswerStatus
                                                        .incorrect
                                                ? 'Incorrect'
                                                : 'Unanswered',
                                        style: const TextStyle(
                                            color: Colors.white, fontSize: 12),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                    'Question":${question.questionAndAnswers.question}',
                                    style: const TextStyle(fontSize: 12)),
                                Text('Module: ${question.module}',
                                    style: const TextStyle(
                                        fontSize: 12, color: Colors.grey)),
                                if (question.answerStatus !=
                                    QuestionAnswerStatus.unanswered) ...[
                                  const SizedBox(height: 4),
                                  Text('Selected: ${question.selectedAnswer}',
                                      style: const TextStyle(fontSize: 12)),
                                  Text('Correct: ${question.correctAnswer}',
                                      style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold)),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  } else if (state is NewTestFetchedState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Today\'s Daily Test Fetched:',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 16),
                        Text('Test ID: ${state.test.testId}'),
                        Text('Test Name: ${state.test.description}'),
                        Text('Subject: ${state.test.subject}'),
                        Text(
                            'Total Questions: ${state.test.questionWithOrderList.length}'),
                        Text('Test Attempt ID: ${state.testAttemptId}'),
                        const SizedBox(height: 16),
                        const Text('Question IDs:',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        ...state.test.questionWithOrderList.take(5).map(
                              (questionWithOrder) => Container(
                                margin: const EdgeInsets.only(bottom: 4.0),
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(4.0),
                                  color: Colors.blue.withValues(alpha: 0.1),
                                ),
                                child: Row(
                                  children: [
                                    Text('Q${questionWithOrder.order}: ',
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    Expanded(
                                        child:
                                            Text(questionWithOrder.questionId)),
                                  ],
                                ),
                              ),
                            ),
                        if (state.test.questionWithOrderList.length > 5)
                          Text(
                              '... and ${state.test.questionWithOrderList.length - 5} more questions'),
                      ],
                    );
                  } else if (state is TestDataErrorState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Daily Test Error:',
                            style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.red)),
                        const SizedBox(height: 8),
                        Text('Error: ${state.message}'),
                      ],
                    );
                  } else {
                    return Text('TestsStateInitial =$state');
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TestScreen extends StatefulWidget {
  const TestScreen({super.key});

  @override
  State<TestScreen> createState() => _testData();
}

class _testData extends State<TestScreen> {
  String someData = "No data";

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TestsBloc, TestsStateInitial>(
      builder: (context, state) {
        return Text(state.toString());
      },
    );
  }
}
