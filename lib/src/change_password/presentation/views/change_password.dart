import 'package:flutter/material.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/change_password/presentation/widgets/change_password_header.dart';

class ChangePassword extends StatefulWidget {
  const ChangePassword({super.key});

  factory ChangePassword.routeBuilder(_, __) {
    return const ChangePassword();
  }

  @override
  State<StatefulWidget> createState() => __changePasswordState();
}

class __changePasswordState extends State<ChangePassword> {
  var _isOldPassWordObscured;
  var _isNewPasswordObscured;
  var _isConfirmPasswordObscured;

  @override
  void initState() {
    super.initState();

    _isOldPassWordObscured = true;
    _isNewPasswordObscured = true;
    _isConfirmPasswordObscured = true;
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Scaffold(
      backgroundColor: const Color(0xFFF2F2F2),
      appBar: const AppBarTemplate(
          appBarTitle: 'Change Password', actionType: 'NA'),
      body: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.fromLTRB(16 * fem, 20 * fem, 16 * fem, 20 * fem),
          child: Column(
            children: [
              const ChangePasswordHeader(),
              SizedBox(
                height: 20 * fem,
              ),
              Container(
                padding:
                    EdgeInsets.fromLTRB(16 * fem, 20 * fem, 16 * fem, 20 * fem),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    TextFormField(
                      obscureText: _isOldPassWordObscured,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        labelText: 'Old Password',
                        labelStyle: TextStyle(
                          color: kPrimaryColor,
                          fontSize: 12 * fem,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          height: 0,
                        ),
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              _isOldPassWordObscured = !_isOldPassWordObscured;
                            });
                          },
                          icon: _isOldPassWordObscured
                              ? const Icon(Icons.visibility_outlined)
                              : const Icon(Icons.visibility_off_outlined),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20 * fem,
                    ),
                    TextFormField(
                      obscureText: _isNewPasswordObscured,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        labelText: 'New Password',
                        labelStyle: TextStyle(
                          color: kPrimaryColor,
                          fontSize: 12 * fem,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          height: 0,
                        ),
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              _isNewPasswordObscured = !_isNewPasswordObscured;
                            });
                          },
                          icon: _isNewPasswordObscured
                              ? const Icon(Icons.visibility_outlined)
                              : const Icon(Icons.visibility_off_outlined),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20 * fem,
                    ),
                    TextFormField(
                      obscureText: _isConfirmPasswordObscured,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: kSecondaryColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        labelText: 'Confirm Password',
                        labelStyle: TextStyle(
                          color: kPrimaryColor,
                          fontSize: 12 * fem,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          height: 0,
                        ),
                        suffixIcon: IconButton(
                          onPressed: () {
                            setState(() {
                              _isConfirmPasswordObscured =
                                  !_isConfirmPasswordObscured;
                            });
                          },
                          icon: _isConfirmPasswordObscured
                              ? const Icon(Icons.visibility_outlined)
                              : const Icon(Icons.visibility_off_outlined),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 25 * fem,
                    ),
                    const Text(
                      '**Require all devices to sign in with new password',
                      style: TextStyle(
                        color: Color(0xFF575757),
                        fontSize: 14,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                        height: 0,
                      ),
                    ),
                    SizedBox(
                      height: 20 * fem,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomActionButtonsWidget(
        fem: fem,
        primaryCallBack: () {},
        primaryText: 'Save',
        secondaryCallBack: () {},
        secondaryText: 'Back',
        isDualActions: true,
      ),
    );
  }
}
