import 'package:flutter/material.dart';

class ChangePasswordHeader extends StatelessWidget {
  const ChangePasswordHeader({super.key});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return Container(
      padding: EdgeInsets.fromLTRB(14 * fem, 16 * fem, 14 * fem, 16 * fem),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SizedBox(
            height: 38 * fem,
            width: 38 * fem,
            child: Image.asset('assets/images/changePassword/star_shape_1.png'),
          ),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 38 * fem,
            width: 38 * fem,
            child: Image.asset('assets/images/changePassword/star_shape_1.png'),
          ),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 115 * fem,
            width: 115 * fem,
            child: Image.asset('assets/images/changePassword/lockAndKey.png'),
          ),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 38 * fem,
            width: 38 * fem,
            child: Image.asset('assets/images/changePassword/star_shape_1.png'),
          ),
          Sized<PERSON><PERSON>(
            height: 38 * fem,
            width: 38 * fem,
            child: Image.asset('assets/images/changePassword/star_shape_1.png'),
          ),
        ],
      ),
    );
  }
}
