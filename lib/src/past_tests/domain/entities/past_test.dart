import 'package:equatable/equatable.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';

class PastTest extends Equatable {
  final int id;
  final String testName;
  final String testDate;
  final String duration;
  final int questionCount;
  final double scorePercentage;
  final TestResultStatus status;
  final TestTypes testType;

  const PastTest({
    required this.id,
    required this.testName,
    required this.testDate,
    required this.duration,
    required this.questionCount,
    required this.scorePercentage,
    required this.status,
    required this.testType,
  });

  @override
  List<Object?> get props => [
        id,
        testName,
        testDate,
        duration,
        questionCount,
        scorePercentage,
        status,
        testType,
      ];
}
