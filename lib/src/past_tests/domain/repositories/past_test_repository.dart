import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/past_test.dart';
import '../../../test/domain/entitites/test.dart';

abstract class PastTestRepository {
  Future<Either<Failure, List<PastTest>>> getPastTests();
  Future<Either<Failure, List<GroupedSectionalTestAttempts>>>
      getGroupedPastTests();
  Future<Either<Failure, void>> retakeTest(int testId);
}
