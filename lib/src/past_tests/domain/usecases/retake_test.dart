import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/past_test_repository.dart';

class RetakeTest implements UseCase<void, RetakeTestParams> {
  final PastTestRepository repository;

  RetakeTest(this.repository);

  @override
  Future<Either<Failure, void>> call(RetakeTestParams params) {
    return repository.retakeTest(params.testId);
  }
}

class RetakeTestParams extends Equatable {
  final int testId;

  const RetakeTestParams({required this.testId});

  @override
  List<Object?> get props => [testId];
}
