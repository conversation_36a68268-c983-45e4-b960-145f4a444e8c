import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/past_test.dart';
import '../repositories/past_test_repository.dart';

class GetPastTests implements UseCase<List<PastTest>, NoParams> {
  final PastTestRepository repository;

  GetPastTests(this.repository);

  @override
  Future<Either<Failure, List<PastTest>>> call(NoParams params) {
    return repository.getPastTests();
  }
}
