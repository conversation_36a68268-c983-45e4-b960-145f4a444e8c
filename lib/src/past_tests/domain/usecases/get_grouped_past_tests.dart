import 'package:skillapp/src/test/domain/common/tests_enum.dart';

import '../../../../core/common/usecase/usecase.dart';
import '../../../../core/common/utils/typedefs.dart';
import '../../../test/domain/entitites/test.dart';
import '../../../test/domain/usecases/fetch_grouped_sectional_test_attempt_history.dart';

class GetGroupedPastTests extends FutureUsecaseWithParams<
    List<GroupedSectionalTestAttempts>, TestTypes> {
  final FetchGroupedSectionalTestAttemptHistory
      fetchGroupedSectionalTestAttemptHistory;

  GetGroupedPastTests(this.fetchGroupedSectionalTestAttemptHistory);

  @override
  ResultFuture<List<GroupedSectionalTestAttempts>> call(params) {
    return fetchGroupedSectionalTestAttemptHistory.call(params);
  }
}
