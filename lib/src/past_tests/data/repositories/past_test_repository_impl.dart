import 'package:dartz/dartz.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/past_test.dart';
import '../../domain/repositories/past_test_repository.dart';
import '../../../test/domain/entitites/test.dart';
import '../../../test/domain/usecases/fetch_grouped_sectional_test_attempt_history.dart';

class PastTestRepositoryImpl implements PastTestRepository {
  final FetchGroupedSectionalTestAttemptHistory
      fetchGroupedSectionalTestAttemptHistory;

  PastTestRepositoryImpl(
      {required this.fetchGroupedSectionalTestAttemptHistory});
  @override
  Future<Either<Failure, List<PastTest>>> getPastTests() async {
    try {
      // This is mock data for now
      // In a real implementation, this would fetch data from a data source
      await Future.delayed(
          const Duration(seconds: 1)); // Simulate network delay

      return const Right([
        PastTest(
          id: 1,
          testName: 'Selective Ninja Entry Quiz',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 100,
          status: TestResultStatus.completed,
          testType: TestTypes.sectional, // Assuming this is a sectional test
        ),
        PastTest(
          id: 2,
          testName: 'Scholar\'s Pathway',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 25,
          status: TestResultStatus.failed,
          testType: TestTypes.sectional,
        ),
        PastTest(
          id: 3,
          testName: 'Selective Ninja Entry Quiz',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 100,
          status: TestResultStatus.completed,
          testType: TestTypes.sectional,
        ),
        PastTest(
          id: 4,
          testName: 'Scholar\'s Pathway',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 25,
          status: TestResultStatus.failed,
          testType: TestTypes.sectional,
        ),
        PastTest(
          id: 5,
          testName: 'Selective Ninja Entry Quiz',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 100,
          status: TestResultStatus.completed,
          testType: TestTypes.sectional,
        ),
        PastTest(
          id: 6,
          testName: 'Scholar\'s Pathway',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 25,
          status: TestResultStatus.failed,
          testType: TestTypes.sectional,
        ),
        PastTest(
          id: 7,
          testName: 'Selective Ninja Entry Quiz',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 100,
          status: TestResultStatus.completed,
          testType: TestTypes.sectional,
        ),
        PastTest(
          id: 8,
          testName: 'Scholar\'s Pathway',
          testDate: '12/04/2024',
          duration: '15:00 Min',
          questionCount: 10,
          scorePercentage: 25,
          status: TestResultStatus.failed,
          testType: TestTypes.sectional,
        ),
      ]);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<GroupedSectionalTestAttempts>>>
      getGroupedPastTests() async {
    // This method is not used since we're using the usecase directly
    // Return empty list for now
    return const Right([]);
  }

  @override
  Future<Either<Failure, void>> retakeTest(int testId) async {
    try {
      // This is a mock implementation
      // In a real implementation, this would call a data source
      await Future.delayed(
          const Duration(seconds: 1)); // Simulate network delay

      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }
}
