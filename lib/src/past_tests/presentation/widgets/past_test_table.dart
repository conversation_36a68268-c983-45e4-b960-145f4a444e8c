import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/past_tests/domain/entities/past_test.dart';
import 'package:skillapp/src/past_tests/presentation/blocs/past_tests_bloc.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';

class PastTestTable extends StatefulWidget {
  final List<PastTest> tests;

  const PastTestTable({
    super.key,
    required this.tests,
  });

  @override
  State<PastTestTable> createState() => _PastTestTableState();
}

class _PastTestTableState extends State<PastTestTable> {
  // Track which rows are expanded
  final Set<int> _expandedRows = {};

  void _toggleRow(int testId) {
    setState(() {
      if (_expandedRows.contains(testId)) {
        _expandedRows.remove(testId);
      } else {
        _expandedRows.add(testId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                SizedBox(
                    width: 40, child: Text('Sl No', style: kTableHeaderTs)),
                SizedBox(width: 20),
                Expanded(
                  flex: 3,
                  child: Text('Test Name', style: kTableHeaderTs),
                ),
                Expanded(
                  flex: 1,
                  child: Text('Date', style: kTableHeaderTs),
                ),
                Expanded(
                  flex: 1,
                  child: Text('Duration', style: kTableHeaderTs),
                ),
                Expanded(
                  flex: 1,
                  child: Text('Score', style: kTableHeaderTs),
                ),
                Expanded(
                  flex: 1,
                  child: Text('Status', style: kTableHeaderTs),
                ),
                SizedBox(width: 120),
                SizedBox(width: 40),
              ],
            ),
          ),

          // Table rows
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: widget.tests.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final test = widget.tests[index];
              final isExpanded = _expandedRows.contains(test.id);
              return Column(
                children: [
                  _buildTestRow(context, test, index + 1, isExpanded),
                  // Show expanded content if row is expanded
                  if (isExpanded) _buildExpandedContent(context, test),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTestRow(
      BuildContext context, PastTest test, int index, bool isExpanded) {
    print("TEST Data is --> $test");

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      child: Row(
        children: [
          // Index number
          SizedBox(
            width: 40,
            child: Text(
              index.toString(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 20),

          // Test name and question count
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  test.testName,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${test.questionCount} Questions',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Test date
          Expanded(
            flex: 1,
            child: Text(
              test.testDate,
              style: const TextStyle(fontSize: 14),
            ),
          ),

          // Duration
          Expanded(
            flex: 1,
            child: Text(
              test.duration,
              style: const TextStyle(fontSize: 14),
            ),
          ),

          // Score
          Expanded(
            flex: 1,
            child: Text(
              '${test.scorePercentage.toInt()} %',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Status
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(test.status),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                _getStatusText(test.status),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          // Retake button
          SizedBox(
            width: 120,
            child: ElevatedButton(
              onPressed: () {
                context
                    .read<PastTestsBloc>()
                    .add(RetakeTestEvent(test.testType, test.id));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: kPrimaryColor,
                side: const BorderSide(color: kPrimaryColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
              child: const Text('Retake Test'),
            ),
          ),

          // More options button
          SizedBox(
            width: 40,
            child: IconButton(
              icon: Icon(isExpanded
                  ? Icons.keyboard_arrow_up
                  : Icons.keyboard_arrow_down),
              onPressed: () => _toggleRow(test.id),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TestResultStatus status) {
    switch (status) {
      case TestResultStatus.completed:
        return Colors.green;
      case TestResultStatus.failed:
        return Colors.red;
      case TestResultStatus.pending:
        return Colors.orange;
    }
  }

  String _getStatusText(TestResultStatus status) {
    switch (status) {
      case TestResultStatus.completed:
        return 'Completed';
      case TestResultStatus.failed:
        return 'Failed';
      case TestResultStatus.pending:
        return 'Pending';
    }
  }

  Widget _buildExpandedContent(BuildContext context, PastTest test) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 16),
      color: Colors.grey[50],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Test Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: kPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),

          // Test details in a grid layout
          Wrap(
            spacing: 40,
            runSpacing: 16,
            children: [
              _buildDetailItem('Test ID', '#${test.id}'),
              _buildDetailItem('Questions', '${test.questionCount} questions'),
              _buildDetailItem('Date Taken', test.testDate),
              _buildDetailItem('Duration', test.duration),
              _buildDetailItem('Score', '${test.scorePercentage.toInt()}%'),
              _buildDetailItem('Status', _getStatusText(test.status)),
            ],
          ),

          const SizedBox(height: 24),

          // Test History section
          const Text(
            'Test History',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: kPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),

          // Test history items
          _buildTestHistoryItem('12/02/2024', 20, TestResultStatus.failed,
              testId: test.id.toString()),
          const SizedBox(height: 8),
          _buildTestHistoryItem('12/02/2024', 20, TestResultStatus.failed,
              testId: test.id.toString()),

          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  // View detailed results
                },
                icon: const Icon(Icons.assessment, size: 18),
                label: const Text('View Results'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: kPrimaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
              const SizedBox(width: 16),
              OutlinedButton.icon(
                onPressed: () {
                  // Download certificate
                },
                icon: const Icon(Icons.download, size: 18),
                label: const Text('Download Certificate'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: kPrimaryColor,
                  side: const BorderSide(color: kPrimaryColor),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTestHistoryItem(String date, int score, TestResultStatus status,
      {String testId = '1'}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Date
          Expanded(
            flex: 2,
            child: Text(
              date,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Score
          Expanded(
            flex: 1,
            child: Text(
              '$score%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
          ),

          // Status
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: status == TestResultStatus.failed
                    ? const Color.fromRGBO(255, 0, 0, 0.1) // Red with opacity
                    : status == TestResultStatus.completed
                        ? const Color.fromRGBO(
                            0, 128, 0, 0.1) // Green with opacity
                        : const Color.fromRGBO(
                            255, 165, 0, 0.1), // Orange with opacity
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                _getStatusText(status),
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: status == TestResultStatus.failed
                      ? Colors.red
                      : status == TestResultStatus.completed
                          ? Colors.green
                          : Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          // View Summary button
          TextButton.icon(
            onPressed: () {
              // Navigate to test summary page
              // Hardcoded the TestAttempt Id for Testing
              testId = 'f9295f1d-339d-4f46-a92f-0d91f4cc4a81';
              context.go('/test-result/$testId');
              //  context.go('/test-summary/$testId');
            },
            icon: const Icon(Icons.arrow_forward, size: 16),
            label: const Text('View Summary'),
            style: TextButton.styleFrom(
              foregroundColor: kPrimaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return SizedBox(
      width: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
