import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';

class PastTestsSummary extends StatelessWidget {
  final int totalTests;
  final int passedTests;
  final VoidCallback onTakeNewTest;

  const PastTestsSummary({
    super.key,
    required this.totalTests,
    required this.passedTests,
    required this.onTakeNewTest,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Title
          // This needs to be replaced with the Test Type from Database
          const Text(
            '',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),

          // Stats and action button
          Row(
            children: [
              // Total tests
              _buildStatItem(
                icon: Icons.assignment_outlined,
                iconColor: Colors.amber,
                label: 'Total Tests',
                value: totalTests.toString(),
              ),
              const SizedBox(width: 24),

              // Passed tests
              /* _buildStatItem(
                icon: Icons.check_circle_outline,
                iconColor: Colors.pink,
                label: 'Passed',
                value: passedTests.toString(),
              ),
              const SizedBox(width: 24), */

              // Take new test button
              ElevatedButton.icon(
                onPressed: onTakeNewTest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: kPrimaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                icon: const Icon(
                  Icons.bolt,
                  color: Colors.white,
                ),
                label: const Text('Take New Test'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
