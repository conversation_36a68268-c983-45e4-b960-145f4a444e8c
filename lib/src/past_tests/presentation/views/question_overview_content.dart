import 'package:flutter/material.dart';

class QuestionOverviewContent extends StatelessWidget {
  const QuestionOverviewContent({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample question data
    final List<Map<String, dynamic>> questions = [
      {
        'id': 1,
        'question': 'What is the sum of 478 and 326?',
        'result': 'Incorrect',
        'time': '2 min 10 sec',
      },
      {
        'id': 2,
        'question': 'Multiply 34 by 12.',
        'result': 'Correct',
        'time': '1 min 45 sec',
      },
      {
        'id': 3,
        'question': 'What is the difference between 987 and 459?',
        'result': 'Incorrect',
        'time': '2 min 30 sec',
      },
      {
        'id': 4,
        'question': 'Divide 144 by 12.',
        'result': 'Correct',
        'time': '1 min 0 sec',
      },
      {
        'id': 5,
        'question':
            'A rectangle has a length of 15 cm and a width of 8 cm. What is its area?',
        'result': 'Incorrect',
        'time': '3 min 15 sec',
      },
      {
        'id': 6,
        'question':
            'If a box contains 248 apples and 63 apples are taken out, how many apples are left?',
        'result': 'Correct',
        'time': '2 min 5 sec',
      },
      {
        'id': 7,
        'question': 'Convert 3.5 kilometers into meters.',
        'result': 'Correct',
        'time': '2 min 40 sec',
      },
      {
        'id': 8,
        'question': 'What is the perimeter of a square with side length 9 cm?',
        'result': 'Correct',
        'time': '1 min 50 sec',
      },
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    'Sl No',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Text(
                    'Question',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Result',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Time Taken',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                const SizedBox(width: 24), // Space for chevron
              ],
            ),
          ),

          // Question Rows
          ...questions.map((question) => _buildQuestionRow(question)),
        ],
      ),
    );
  }

  Widget _buildQuestionRow(Map<String, dynamic> question) {
    final bool isCorrect = question['result'] == 'Correct';

    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Navigate to question details
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                // Question ID
                Expanded(
                  flex: 1,
                  child: Text(
                    question['id'].toString(),
                    style: const TextStyle(
                      color: Colors.black87,
                    ),
                  ),
                ),

                // Question Text
                Expanded(
                  flex: 6,
                  child: Text(
                    question['question'],
                    style: const TextStyle(
                      color: Colors.black87,
                    ),
                  ),
                ),

                // Result
                Expanded(
                  flex: 2,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: isCorrect ? Colors.green[100] : Colors.red[100],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        question['result'],
                        style: TextStyle(
                          color:
                              isCorrect ? Colors.green[700] : Colors.red[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),

                // Time Taken
                Expanded(
                  flex: 2,
                  child: Text(
                    question['time'],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.black87,
                    ),
                  ),
                ),

                // Chevron
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey[400],
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
