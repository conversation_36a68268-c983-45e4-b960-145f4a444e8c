import 'dart:math';
import 'package:flutter/material.dart';

class RadarChartPainter extends CustomPainter {
  final List<String> categories = [
    'Reading',
    'Maths',
    'Thinking Skills',
    'Writing'
  ];
  final List<double> yourScores = [
    0.85,
    0.80,
    0.55,
    0.70
  ]; // 85%, 80%, 55%, 70%
  final List<double> cohortScores = [
    0.70,
    0.75,
    0.60,
    0.70
  ]; // 70%, 75%, 60%, 70%

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2 * 0.8;

    // Draw radar grid
    _drawRadarGrid(canvas, center, radius);

    // Draw category labels
    _drawCategoryLabels(canvas, center, radius);

    // Draw data
    _drawData(
        canvas, center, radius, yourScores, Colors.purple[200]!, Colors.purple);
    _drawData(
        canvas, center, radius, cohortScores, Colors.green[200]!, Colors.green);
  }

  void _drawRadarGrid(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw concentric circles
    for (int i = 1; i <= 5; i++) {
      final circleRadius = radius * i / 5;
      canvas.drawCircle(center, circleRadius, paint);
    }

    // Draw radial lines
    for (int i = 0; i < categories.length; i++) {
      final angle = 2 * pi * i / categories.length - pi / 2;
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);
      canvas.drawLine(center, Offset(x, y), paint);
    }
  }

  void _drawCategoryLabels(Canvas canvas, Offset center, double radius) {
    const textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 12,
    );

    for (int i = 0; i < categories.length; i++) {
      final angle = 2 * pi * i / categories.length - pi / 2;
      final x = center.dx + (radius + 20) * cos(angle);
      final y = center.dy + (radius + 20) * sin(angle);

      final textSpan = TextSpan(
        text: categories[i],
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Adjust position to center the text
      final textOffset = Offset(
        x - textPainter.width / 2,
        y - textPainter.height / 2,
      );

      textPainter.paint(canvas, textOffset);
    }
  }

  void _drawData(Canvas canvas, Offset center, double radius,
      List<double> scores, Color fillColor, Color borderColor) {
    final fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final path = Path();

    for (int i = 0; i < categories.length; i++) {
      final angle = 2 * pi * i / categories.length - pi / 2;
      final score = scores[i];
      final x = center.dx + radius * score * cos(angle);
      final y = center.dy + radius * score * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
