import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:fl_chart/fl_chart.dart';
import 'radar_chart_painter.dart';
import 'question_overview_content.dart';
import 'question_compare_content.dart';

class TestSummaryPage extends StatefulWidget {
  final String testId;

  const TestSummaryPage({
    super.key,
    required this.testId,
  });

  @override
  State<TestSummaryPage> createState() => _TestSummaryPageState();
}

class _TestSummaryPageState extends State<TestSummaryPage> {
  int _selectedTabIndex = 0;
  final List<String> _tabs = [
    'Summary',
    'Subject-wise Analysis',
    'Question Analysis',
    'Progress & Insights',
    'How You Can Improve',
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tabs
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              padding: const EdgeInsets.all(8),
              child: Row(
                children: List.generate(
                  _tabs.length,
                  (index) => Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedTabIndex = index;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: _selectedTabIndex == index
                              ? kPrimaryColor.withAlpha(25)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _tabs[index],
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: _selectedTabIndex == index
                                ? kPrimaryColor
                                : Colors.grey[700],
                            fontWeight: _selectedTabIndex == index
                                ? FontWeight.w600
                                : FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Tab content
            _buildTabContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildSummaryTab();
      case 1:
        return _buildComingSoonTab('Subject-wise Analysis');
      case 2:
        return _buildComingSoonTab('Question Analysis');
      case 3:
        return _buildComingSoonTab('Progress & Insights');
      case 4:
        return _buildHowYouCanImproveTab();
      default:
        return _buildSummaryTab();
    }
  }

  Widget _buildComingSoonTab(String tabName) {
    // Skip implemented tabs
    if (tabName == 'Subject-wise Analysis') {
      return _buildSubjectWiseAnalysisTab();
    } else if (tabName == 'Question Analysis') {
      return _buildQuestionAnalysisTab();
    } else if (tabName == 'Progress & Insights') {
      return _buildProgressInsightsTab();
    } else if (tabName == 'How You Can Improve') {
      return _buildHowYouCanImproveTab();
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(48.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              '$tabName Coming Soon',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'We are working on this feature and it will be available soon.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressInsightsTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Radar Chart and Table Row
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Radar Chart Section
            Expanded(
              flex: 1,
              child: Container(
                height: 400,
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Radar Chart
                    Expanded(
                      child: _buildRadarChart(),
                    ),

                    // Legend
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Your Score Legend
                        Row(
                          children: [
                            Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.purple[100],
                                border: Border.all(color: Colors.purple),
                              ),
                            ),
                            const SizedBox(width: 8),
                            const Text('Your Score'),
                          ],
                        ),

                        const SizedBox(width: 24),

                        // Cohort Score Legend
                        Row(
                          children: [
                            Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.green[100],
                                border: Border.all(color: Colors.green),
                              ),
                            ),
                            const SizedBox(width: 8),
                            const Text('Cohort Score'),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Score Table Section
            Expanded(
              flex: 1,
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  children: [
                    // Table Header
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              'Subject',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              'Your Score(%)',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              'Cohort Average(%)',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Table Rows
                    _buildScoreTableRow('Reading', '85%', '70%'),
                    _buildScoreTableRow('Maths', '80%', '75%'),
                    _buildScoreTableRow('Thinking Skills', '55%', '60%'),
                    _buildScoreTableRow('Writing', '70%', '70%'),
                  ],
                ),
              ),
            ),
          ],
        ),

        // Insights Section
        Container(
          padding: const EdgeInsets.all(16),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Insight 1
              Row(
                children: [
                  Text(
                    'You\'re ahead of the average in Reading',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    '🚀',
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12),

              // Insight 2
              Text(
                'You can improve Thinking Skills - try 5 practice questions.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),

              SizedBox(height: 12),

              // Insight 3
              Text(
                'You\'ve matched the class in Writing. Great job!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Back Button
        Center(
          child: ElevatedButton(
            onPressed: () {
              // Handle back button
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: kPrimaryColor,
              side: const BorderSide(color: kPrimaryColor),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text('Back'),
          ),
        ),
      ],
    );
  }

  Widget _buildRadarChart() {
    return CustomPaint(
      size: const Size(300, 300),
      painter: RadarChartPainter(),
    );
  }

  Widget _buildScoreTableRow(
      String subject, String yourScore, String cohortAverage) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(subject),
          ),
          Expanded(
            flex: 1,
            child: Text(
              yourScore,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: _getScoreColor(yourScore, cohortAverage),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(cohortAverage),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(String yourScore, String cohortAverage) {
    final yourScoreValue = int.parse(yourScore.replaceAll('%', ''));
    final cohortAverageValue = int.parse(cohortAverage.replaceAll('%', ''));

    if (yourScoreValue > cohortAverageValue) {
      return Colors.green;
    } else if (yourScoreValue < cohortAverageValue) {
      return Colors.red;
    } else {
      return Colors.blue;
    }
  }

  Widget _buildQuestionAnalysisTab() {
    // State variables for the tab
    bool isOverviewSelected = false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Subject Selection Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Subject',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: 300,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: DropdownButton<String>(
                value: 'Mathematics',
                isExpanded: true,
                underline: const SizedBox(),
                icon: const Icon(Icons.keyboard_arrow_down),
                items: ['Mathematics', 'English', 'Science', 'History']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle dropdown change
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Overview/Compare Toggle
        Row(
          children: [
            // Toggle buttons
            Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  // Overview Button
                  InkWell(
                    onTap: () {
                      setState(() {
                        isOverviewSelected = true;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 8),
                      decoration: BoxDecoration(
                        color: isOverviewSelected
                            ? Colors.white
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Overview',
                        style: TextStyle(
                          color: isOverviewSelected
                              ? kPrimaryColor
                              : Colors.grey[600],
                          fontWeight: isOverviewSelected
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),

                  // Compare Button
                  InkWell(
                    onTap: () {
                      setState(() {
                        isOverviewSelected = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 8),
                      decoration: BoxDecoration(
                        color: !isOverviewSelected
                            ? Colors.white
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Compare',
                        style: TextStyle(
                          color: !isOverviewSelected
                              ? kPrimaryColor
                              : Colors.grey[600],
                          fontWeight: !isOverviewSelected
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Content based on selected tab
        if (isOverviewSelected)
          const QuestionOverviewContent()
        else
          const QuestionCompareContent(),

        const SizedBox(height: 24),

        // Back Button
        Center(
          child: ElevatedButton(
            onPressed: () {
              // Handle back button
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: kPrimaryColor,
              side: const BorderSide(color: kPrimaryColor),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text('Back'),
          ),
        ),
      ],
    );
  }

  Widget _buildSubjectWiseAnalysisTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Subject Selection Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Subject',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: 300,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: DropdownButton<String>(
                value: 'Mathematics',
                isExpanded: true,
                underline: const SizedBox(),
                icon: const Icon(Icons.keyboard_arrow_down),
                items: ['Mathematics', 'English', 'Science', 'History']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle dropdown change
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Mathematics Section Title
        const Text(
          'Mathematics',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),

        const SizedBox(height: 16),

        // Mathematics Table
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            children: [
              // Table Header
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'Question Type',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'No. of Questions',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Accuracy',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Avg. Time',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Cohort Accuracy',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Cohort Avg. Time',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Table Rows
              _buildSubjectTableRow(
                questionType: 'Comprehension',
                numQuestions: 10,
                accuracy: '80%',
                avgTime: '45 sec',
                cohortAccuracy: '90%',
                cohortAvgTime: '45 sec',
                bgColor: Colors.grey[50],
              ),
              _buildSubjectTableRow(
                questionType: 'Numerical Reasoning',
                numQuestions: 8,
                accuracy: '60%',
                avgTime: '60 sec',
                cohortAccuracy: '80%',
                cohortAvgTime: '60 sec',
              ),
              _buildSubjectTableRow(
                questionType: 'Logical Thinking',
                numQuestions: 7,
                accuracy: '90%',
                avgTime: '40 sec',
                cohortAccuracy: '94%',
                cohortAvgTime: '40 sec',
                bgColor: Colors.grey[50],
              ),
            ],
          ),
        ),

        const SizedBox(height: 32),

        // Average Score by Subject Section
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Average Score by subject',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Legend
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: Colors.blue[400],
                    ),
                    const SizedBox(width: 4),
                    const Text('Your Average'),
                    const SizedBox(width: 16),
                    Container(
                      width: 12,
                      height: 12,
                      color: Colors.green[400],
                    ),
                    const SizedBox(width: 4),
                    const Text('Cohort Average'),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Charts Row
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Bar Chart
                Expanded(
                  child: Container(
                    height: 300,
                    padding: const EdgeInsets.all(16),
                    child: BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: 100,
                        barTouchData: BarTouchData(enabled: false),
                        titlesData: FlTitlesData(
                          show: true,
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: false,
                              getTitlesWidget: (value, meta) {
                                return const SizedBox();
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                if (value % 10 == 0 && value > 0) {
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: Text(
                                      value.toInt().toString(),
                                      style: const TextStyle(fontSize: 10),
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false)),
                          rightTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false)),
                        ),
                        gridData: FlGridData(
                          show: true,
                          horizontalInterval: 10,
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color: Colors.grey[300],
                              strokeWidth: 1,
                            );
                          },
                        ),
                        borderData: FlBorderData(show: false),
                        barGroups: [
                          BarChartGroupData(
                            x: 0,
                            barRods: [
                              BarChartRodData(
                                toY: 88,
                                color: Colors.blue[400],
                                width: 40,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ],
                          ),
                          BarChartGroupData(
                            x: 1,
                            barRods: [
                              BarChartRodData(
                                toY: 82,
                                color: Colors.green[400],
                                width: 40,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Horizontal Bar Chart
                Expanded(
                  child: Container(
                    height: 300,
                    padding: const EdgeInsets.all(16),
                    child: BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: 100,
                        barTouchData: BarTouchData(enabled: false),
                        titlesData: FlTitlesData(
                          show: true,
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                const titles = ['', 'Algebra', 'Fraction', ''];
                                if (value >= 0 && value < titles.length) {
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      titles[value.toInt()],
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                if (value % 20 == 0 && value > 0) {
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: Text(
                                      value.toInt().toString(),
                                      style: const TextStyle(fontSize: 10),
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false)),
                          rightTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false)),
                        ),
                        gridData: FlGridData(
                          show: true,
                          horizontalInterval: 20,
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color: Colors.grey[300],
                              strokeWidth: 1,
                            );
                          },
                        ),
                        borderData: FlBorderData(show: false),
                        barGroups: [
                          // Algebra - Your Average
                          BarChartGroupData(
                            x: 1,
                            barRods: [
                              BarChartRodData(
                                toY: 80,
                                color: Colors.blue[400],
                                width: 20,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ],
                          ),
                          // Algebra - Cohort Average
                          BarChartGroupData(
                            x: 1,
                            groupVertically: true,
                            barRods: [
                              BarChartRodData(
                                toY: 70,
                                color: Colors.green[400],
                                width: 20,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ],
                          ),
                          // Fraction - Your Average
                          BarChartGroupData(
                            x: 2,
                            barRods: [
                              BarChartRodData(
                                toY: 95,
                                color: Colors.blue[400],
                                width: 20,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ],
                          ),
                          // Fraction - Cohort Average
                          BarChartGroupData(
                            x: 2,
                            groupVertically: true,
                            barRods: [
                              BarChartRodData(
                                toY: 80,
                                color: Colors.green[400],
                                width: 20,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  topRight: Radius.circular(4),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Bar Chart Labels
            Row(
              children: [
                Expanded(
                  child: Center(
                    child: Column(
                      children: [
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '88%',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[700],
                              ),
                            ),
                            const SizedBox(width: 40),
                            Text(
                              '82%',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const Expanded(child: SizedBox()),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubjectTableRow({
    required String questionType,
    required int numQuestions,
    required String accuracy,
    required String avgTime,
    required String cohortAccuracy,
    required String cohortAvgTime,
    Color? bgColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: bgColor ?? Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(questionType),
          ),
          Expanded(
            child: Text(numQuestions.toString()),
          ),
          Expanded(
            child: Text(
              accuracy,
              style: TextStyle(
                color: double.parse(accuracy.replaceAll('%', '')) >= 70
                    ? Colors.green
                    : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(avgTime),
          ),
          Expanded(
            child: Text(cohortAccuracy),
          ),
          Expanded(
            child: Text(cohortAvgTime),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Summary cards
        Row(
          children: [
            // Total Percentage
            Expanded(
              child: _buildSummaryCard(
                title: 'Total Percentage',
                content: '75.5%',
                contentStyle: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: kPrimaryColor,
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Time Spent
            Expanded(
              child: _buildSummaryCard(
                title: 'Time Spent',
                content: '48 min, 56s',
                contentStyle: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: kPrimaryColor,
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Cohort Average
            Expanded(
              child: _buildSummaryCard(
                title: 'Cohort Average',
                content: '43.09%',
                contentStyle: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: kPrimaryColor,
                ),
                extraWidget: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'SCORE',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.red[700],
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        color: Colors.red[700],
                        size: 16,
                      ),
                    ],
                  ),
                ),
                chart: SizedBox(
                  height: 40,
                  child: LineChart(
                    LineChartData(
                      gridData: const FlGridData(show: false),
                      titlesData: const FlTitlesData(show: false),
                      borderData: FlBorderData(show: false),
                      lineBarsData: [
                        LineChartBarData(
                          spots: const [
                            FlSpot(0, 3),
                            FlSpot(1, 1),
                            FlSpot(2, 4),
                            FlSpot(3, 2),
                            FlSpot(4, 5),
                          ],
                          isCurved: true,
                          color: Colors.red,
                          barWidth: 2,
                          isStrokeCapRound: true,
                          dotData: const FlDotData(show: false),
                          belowBarData: BarAreaData(
                            show: true,
                            color: Colors.red.withAlpha(25),
                          ),
                        ),
                      ],
                      lineTouchData: const LineTouchData(enabled: false),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Percentile
            Expanded(
              child: _buildSummaryCard(
                title:
                    'You scored in the bottom 15% of people who took this exam.',
                content: '15th Percentile',
                contentStyle: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: kPrimaryColor,
                ),
                titleStyle: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Test Result and Pie Chart
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Result
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Test Result',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildResultItem(
                    color: Colors.green,
                    label: 'Correct Answers',
                    count: 12,
                  ),
                  const SizedBox(height: 12),
                  _buildResultItem(
                    color: Colors.orange,
                    label: 'Incorrect Answers',
                    count: 5,
                  ),
                  const SizedBox(height: 12),
                  _buildResultItem(
                    color: Colors.blue,
                    label: 'Partially correct answers',
                    count: 2,
                  ),
                  const SizedBox(height: 12),
                  _buildResultItem(
                    color: Colors.grey,
                    label: 'Unanswered answers',
                    count: 1,
                  ),
                ],
              ),
            ),

            // Pie Chart
            Expanded(
              child: SizedBox(
                height: 200,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    PieChart(
                      PieChartData(
                        sectionsSpace: 0,
                        centerSpaceRadius: 70,
                        sections: [
                          PieChartSectionData(
                            value: 12,
                            color: Colors.green,
                            radius: 40,
                            showTitle: false,
                          ),
                          PieChartSectionData(
                            value: 5,
                            color: Colors.orange,
                            radius: 40,
                            showTitle: false,
                          ),
                          PieChartSectionData(
                            value: 2,
                            color: Colors.blue,
                            radius: 40,
                            showTitle: false,
                          ),
                          PieChartSectionData(
                            value: 1,
                            color: Colors.grey,
                            radius: 40,
                            showTitle: false,
                          ),
                        ],
                      ),
                    ),
                    const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Total Questions',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '20',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Time Breakdown Table
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Time Breakdown Table',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Table Header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            'Subject',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'Time Spent',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'Ideal Time',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'Avg. Time per Question',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'Accuracy',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Table Rows
                  _buildTimeBreakdownRow(
                    subject: 'Reading',
                    timeSpent: '12 min',
                    idealTime: '12 min',
                    avgTime: '45 sec',
                    accuracy: '85%',
                  ),
                  _buildTimeBreakdownRow(
                    subject: 'Maths',
                    timeSpent: '18 min',
                    idealTime: '18 min',
                    avgTime: '60 sec',
                    accuracy: '75%',
                  ),
                  _buildTimeBreakdownRow(
                    subject: 'Thinking Skills',
                    timeSpent: '22 min',
                    idealTime: '22 min',
                    avgTime: '80 sec',
                    accuracy: '60%',
                  ),
                  _buildTimeBreakdownRow(
                    subject: 'Writing',
                    timeSpent: '30 min',
                    idealTime: '30 min',
                    avgTime: 'N/A',
                    accuracy: 'N/A',
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Comparison to Past Performance
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Comparison to Past Performance',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Chart
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SizedBox(
                      height: 200,
                      child: BarChart(
                        BarChartData(
                          alignment: BarChartAlignment.spaceAround,
                          maxY: 100,
                          barTouchData: BarTouchData(enabled: false),
                          titlesData: FlTitlesData(
                            show: true,
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  const titles = [
                                    'Test 1',
                                    'Test 2',
                                    'Test 3',
                                    'Test 4',
                                    'Test 5'
                                  ];
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      titles[value.toInt()],
                                      style: const TextStyle(fontSize: 10),
                                    ),
                                  );
                                },
                              ),
                            ),
                            leftTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  if (value % 20 == 0) {
                                    return Padding(
                                      padding:
                                          const EdgeInsets.only(right: 8.0),
                                      child: Text(
                                        '${value.toInt()}%',
                                        style: const TextStyle(fontSize: 10),
                                      ),
                                    );
                                  }
                                  return const SizedBox();
                                },
                              ),
                            ),
                            topTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false)),
                            rightTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false)),
                          ),
                          gridData: FlGridData(
                            show: true,
                            horizontalInterval: 20,
                            getDrawingHorizontalLine: (value) {
                              return FlLine(
                                color: Colors.grey[300],
                                strokeWidth: 1,
                              );
                            },
                          ),
                          borderData: FlBorderData(show: false),
                          barGroups: [
                            _buildBarGroup(0, [70, 75]),
                            _buildBarGroup(1, [65, 70]),
                            _buildBarGroup(2, [60, 75]),
                            _buildBarGroup(3, [50, 45]),
                            _buildBarGroup(4, [55, 70]),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Legend
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(width: 8),
                        const Text('Current Test'),
                        const SizedBox(width: 24),
                        Container(
                          width: 16,
                          height: 16,
                          color: Colors.blue[300],
                        ),
                        const SizedBox(width: 8),
                        const Text('Last Test'),
                      ],
                    ),
                  ),

                  // Table
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Table Header
                        const Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                'Subject',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                'Current Test',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                'Last Test',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                'Improvement',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Table Rows
                        _buildComparisonRow(
                          subject: 'Reading',
                          currentTest: '80%',
                          lastTest: '85%',
                          improvement: '85%',
                        ),
                        const SizedBox(height: 8),
                        _buildComparisonRow(
                          subject: 'Maths',
                          currentTest: '75%',
                          lastTest: '80%',
                          improvement: '75%',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Back Button
        Center(
          child: ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: kPrimaryColor,
              side: const BorderSide(color: kPrimaryColor),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text('Back'),
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String content,
    TextStyle? titleStyle,
    required TextStyle contentStyle,
    Widget? extraWidget,
    Widget? chart,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: titleStyle ??
                const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                content,
                style: contentStyle,
              ),
              if (extraWidget != null) ...[
                const SizedBox(width: 8),
                extraWidget,
              ],
            ],
          ),
          if (chart != null) ...[
            const SizedBox(height: 8),
            chart,
          ],
        ],
      ),
    );
  }

  Widget _buildResultItem({
    required Color color,
    required String label,
    required int count,
  }) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
          ),
        ),
        const Spacer(),
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeBreakdownRow({
    required String subject,
    required String timeSpent,
    required String idealTime,
    required String avgTime,
    required String accuracy,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(subject),
          ),
          Expanded(
            flex: 1,
            child: Text(timeSpent),
          ),
          Expanded(
            flex: 1,
            child: Text(idealTime),
          ),
          Expanded(
            flex: 1,
            child: Text(avgTime),
          ),
          Expanded(
            flex: 1,
            child: Text(
              accuracy,
              style: TextStyle(
                color: accuracy != 'N/A'
                    ? double.parse(accuracy.replaceAll('%', '')) >= 70
                        ? Colors.green
                        : Colors.red
                    : Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonRow({
    required String subject,
    required String currentTest,
    required String lastTest,
    required String improvement,
  }) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(subject),
        ),
        Expanded(
          flex: 1,
          child: Text(currentTest),
        ),
        Expanded(
          flex: 1,
          child: Text(lastTest),
        ),
        Expanded(
          flex: 1,
          child: Text(
            improvement,
            style: TextStyle(
              color: double.parse(improvement.replaceAll('%', '')) >= 70
                  ? Colors.green
                  : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  BarChartGroupData _buildBarGroup(int x, List<double> values) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: values[0],
          color: Colors.grey[400],
          width: 12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
        BarChartRodData(
          toY: values[1],
          color: Colors.blue[300],
          width: 12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildHowYouCanImproveTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        const Text(
          'How You Can Improve',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 24),

        // Let's Get Better Together
        const Text(
          'Let\'s Get Better Together',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),

        // Improvement Cards Row 1
        Row(
          children: [
            Expanded(
              child: _buildImprovementCard(
                'Practice Just a Little Every Day',
                [
                  'Focus on 1 or 2 weak topics at a time.',
                  'Even 10-15 minutes a day helps build strong skills.'
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildImprovementCard(
                'Set a Small Goal',
                [
                  'Example: "I want to get 3 out of 5 correct in Reading next time."',
                  'Small goals help you feel progress and boost confidence.'
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Improvement Cards Row 2
        Row(
          children: [
            Expanded(
              child: _buildImprovementCard(
                'Ask for Help If You\'re Stuck',
                [
                  'It\'s okay to not understand everything.',
                  'If you get stuck, ask a parent, teacher, or friend for help.'
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildImprovementCard(
                'Don\'t Worry About Mistakes',
                [
                  'Mistakes help you learn!',
                  'Everyone makes them. What matters is trying again.'
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Improvement Cards Row 3
        Row(
          children: [
            Expanded(
              child: _buildImprovementCard(
                'Stay Positive & Be Kind to Yourself',
                [
                  'Remind yourself: "I\'m learning. I\'ll get better."',
                  'Celebrate small wins — even just completing a test is a win!'
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(child: Container()),
          ],
        ),

        const SizedBox(height: 32),

        // Support & Feedback
        const Text(
          'Support & Feedback',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),

        const Text(
          'We\'re here for you! If you have any questions, suggestions, or need help using the app, feel free to reach out.',
          style: TextStyle(
            fontSize: 16,
          ),
        ),

        const SizedBox(height: 16),

        // Contact Info
        Row(
          children: [
            const Text(
              '📧 Email us at: ',
              style: TextStyle(
                fontSize: 16,
              ),
            ),
            InkWell(
              onTap: () {
                // Handle email link tap
              },
              child: const Text(
                '<EMAIL>',
                style: TextStyle(
                  fontSize: 16,
                  color: kPrimaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        const Text(
          '⏱ We\'ll get back to you within 24-48 hours.',
          style: TextStyle(
            fontSize: 16,
          ),
        ),

        const SizedBox(height: 32),

        // Back Button
        SizedBox(
          width: 120,
          child: OutlinedButton(
            onPressed: () {
              // Handle back button tap
              setState(() {
                _selectedTabIndex = 0; // Go back to Summary tab
              });
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: kPrimaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text(
              'Back',
              style: TextStyle(
                color: kPrimaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildImprovementCard(String title, List<String> points) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with pink square
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  color: Colors.pink[100],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Points
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: points
                  .map((point) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('• ', style: TextStyle(fontSize: 16)),
                            Expanded(
                              child: Text(
                                point,
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}
