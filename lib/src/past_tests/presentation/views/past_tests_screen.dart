import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/widgets/loading_widget.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import 'package:skillapp/src/past_tests/domain/entities/past_test.dart';
import 'package:skillapp/src/past_tests/presentation/blocs/past_tests_bloc.dart';
import 'package:skillapp/src/past_tests/presentation/widgets/past_test_table.dart';
import 'package:skillapp/src/past_tests/presentation/widgets/past_tests_summary.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/domain/entitites/test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class PastTestsScreen extends StatefulWidget {
  const PastTestsScreen({super.key});

  static Widget routeBuilder(BuildContext context, GoRouterState state) {
    return const PastTestsScreen();
  }

  @override
  State<PastTestsScreen> createState() => _PastTestsScreenState();
}

class _PastTestsScreenState extends State<PastTestsScreen> {
  // Track which test rows are expanded
  final Set<String> _expandedTestIds = <String>{};

  @override
  void initState() {
    super.initState();
    // Use OnLoadPastTest event to fetch grouped sectional test attempts for accordion display
    context
        .read<PastTestsBloc>()
        .add(const OnLoadPastTest(TestTypes.sectional));
    //TODO - TestTypes.sectional is hardcoded here, we can make it dynamic in future based on what type should load initially
  }

  void _toggleExpansion(String testId) {
    setState(() {
      if (_expandedTestIds.contains(testId)) {
        _expandedTestIds.remove(testId);
      } else {
        _expandedTestIds.add(testId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(40),
      child: BlocBuilder<PastTestsBloc, PastTestsState>(
        builder: (context, state) {
          if (state is PastTestsLoading) {
            return const Center(child: LoadingWidget());
          } else if (state is PastTestsLoaded) {
            return _buildContent(context, state.tests);
          } else if (state is GroupedPastTestsLoaded) {
            return _buildGroupedContent(context, state.groupedTests);
          } else if (state is PastTestsError) {
            return Center(
              child: Text(
                state.message,
                style: const TextStyle(color: Colors.red),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, List<PastTest> tests) {
    final passedTests =
        tests.where((test) => test.status == TestResultStatus.completed).length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Summary section
        PastTestsSummary(
          totalTests: tests.length,
          passedTests: passedTests,
          onTakeNewTest: () {
            _showSubjectSelection(context);
          },
        ),
        const SizedBox(height: 32),

        // Tests table
        PastTestTable(tests: tests),
        const SizedBox(height: 24),

        // Back button
        SizedBox(
          width: 120,
          child: OutlinedButton(
            onPressed: () {
              context.go('/home');
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text('Back'),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupedContent(
      BuildContext context, List<GroupedSectionalTestAttempts> groupedTests) {
    // Calculate summary statistics from grouped tests
    final totalTests = groupedTests.length;
    final passedTests = groupedTests
        .where(
            (groupedTest) => groupedTest.latestAttempt.scorePercentage >= 50.0)
        .length; // Assuming 50% is passing

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab-based UI
        Expanded(
          child: DefaultTabController(
            length: 3,
            child: Column(
              children: [
                // Tab Bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: TabBar(
                    labelColor: Theme.of(context).primaryColor,
                    unselectedLabelColor: Colors.grey[600],
                    indicatorColor: Theme.of(context).primaryColor,
                    indicatorWeight: 3,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                    tabs: const [
                      Tab(text: 'Sectional Test'),
                      Tab(text: 'Daily Test'),
                      Tab(text: 'Full Mock Test'),
                    ],
                  ),
                ),

                // Tab Content
                Expanded(
                  child: TabBarView(
                    children: [
                      /*  _buildTabContent(
                        'Sectional Test',
                        'View and analyze your sectional test attempts. These tests focus on specific subjects and help you identify areas for improvement.',
                        Icons.quiz,
                        Colors.blue,
                      )*/
                      _buildSectionalTestContent(groupedTests),
                      _buildTabContent(
                        'Daily Test',
                        'Track your daily practice sessions. Complete daily tests to maintain consistency and build a strong foundation.',
                        Icons.today,
                        Colors.green,
                      ),
                      _buildTabContent(
                        'Full Mock Test',
                        'Review your comprehensive mock test performances. These full-length tests simulate the actual exam experience.',
                        Icons.assignment,
                        Colors.orange,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Back button
        SizedBox(
          width: 120,
          child: OutlinedButton(
            onPressed: () {
              context.go('/home');
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text('Back'),
          ),
        ),
      ],
    );
  }

  Widget _buildSectionalTestContent(
      List<GroupedSectionalTestAttempts> groupedTests) {
    return groupedTests.isNotEmpty
        ? Column(
            children: [
              // Summary section
              PastTestsSummary(
                totalTests: groupedTests.length,
                passedTests: 0,
                onTakeNewTest: () {
                  _showSubjectSelection(context);
                },
              ),
              const SizedBox(height: 32),

              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 32),
                    child: _buildGroupedTestsList(groupedTests),
                  ),
                ),
              ),
            ],
          )
        : Column(
            children: [
              const SizedBox(height: 32),
              const Text(
                'Hey Ninja! looks like you haven\'t taken any tests yet. Click on the button below to start your journey.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                  fontFamily: 'Poppins',
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  //Condition for taking new test
                  _showSubjectSelection(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: kPrimaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                icon: const Icon(
                  Icons.bolt,
                  color: Colors.white,
                ),
                label: const Text('Take Sectional Test'),
              ),
            ],
          );
  }

  void _showSubjectSelection(BuildContext context) {
    // Capture the original context that has access to TestsBloc
    final originalContext = context;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            color: Colors.white,
            width: 420,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                const Text(
                  'Choose the subject',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 8),

                // Divider
                Container(
                  height: 1,
                  color: Colors.grey.shade300,
                  margin: const EdgeInsets.symmetric(vertical: 16),
                ),

                // Subject options
                _buildModernSubjectTile(
                  originalContext,
                  dialogContext,
                  'Mathematical Reasoning',
                  'maths',
                  'assets/images/dashboard/calculator.png',
                  const Color(0xFFEDECF5),
                ),
                const SizedBox(height: 12),
                _buildModernSubjectTile(
                  originalContext,
                  dialogContext,
                  'Thinking Skills',
                  'thinking',
                  'assets/images/dashboard/light-bulb.png',
                  const Color(0xFFEDECF5),
                ),
                const SizedBox(height: 12),
                _buildModernSubjectTile(
                  originalContext,
                  dialogContext,
                  'Reading',
                  'reading',
                  'assets/images/dashboard/ios-books-application.png',
                  const Color(0xFFEDECF5),
                ),
                const SizedBox(height: 12),
                _buildModernSubjectTile(
                  originalContext,
                  dialogContext,
                  'Writing',
                  'writing',
                  'assets/images/dashboard/pencil.png',
                  const Color(0xFFEDECF5),
                ),

                const SizedBox(height: 24),

                // Back button
                Align(
                  alignment: Alignment.centerLeft,
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(dialogContext).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                      side: const BorderSide(color: kPrimaryColor),
                    ),
                    child: const Text(
                      'Close',
                      style: TextStyle(
                        color: kPrimaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernSubjectTile(
    BuildContext originalContext,
    BuildContext dialogContext,
    String title,
    String subject,
    String image,
    Color backgroundColor,
  ) {
    return InkWell(
      onTap: () {
        Navigator.of(dialogContext).pop();
        _startSectionalTest(originalContext, subject);
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Emoji icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: getBoxDecColor(title),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: SizedBox(
                  //  width: 44 * fem,
                  width: 44,
                  height: 44,
                  child: Image.asset(
                    image,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Subject title
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),

            // Arrow icon
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectTile(
    BuildContext originalContext,
    BuildContext dialogContext,
    String title,
    String subject,
    IconData icon,
    Color color,
  ) {
    return InkWell(
      onTap: () {
        Navigator.of(dialogContext).pop();
        _startSectionalTest(originalContext, subject);
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  void _startSectionalTest(BuildContext context, String subject) {
    final testsBloc = context.read<TestsBloc>();
    if (!testsBloc.isClosed) {
      testsBloc.add(FetchNewSectionalTestEvent(subject: subject));
      context.push('/test-instructions', extra: {
        'subject': subject,
        'testType': TestTypes.sectional,
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to start sectional test. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _retakeTest(
      BuildContext context, GroupedSectionalTestAttempts groupedTest) {
    final testsBloc = context.read<TestsBloc>();
    if (!testsBloc.isClosed) {
      // this will create a newFetchTestState with the existing details
      testsBloc.add(RetakeSectionalTestEvent(
          subject: groupedTest.subject,
          testId: groupedTest.testId,
          testName: groupedTest.testName));

      // Navigate to test instructions screen
      context.push('/test-instructions', extra: {
        'subject': groupedTest.subject,
        'testType': TestTypes.sectional,
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to retake test. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildTabContent(
      String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 40,
              color: color,
            ),
          ),
          const SizedBox(height: 24),

          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Description
          Text(
            description,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black54,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),

          // Coming Soon Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Coming Soon',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupedTestsList(
      List<GroupedSectionalTestAttempts> groupedTests) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    'Sl No',
                    style: TextStyle(
                      color: kPrimaryTextColor,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                    flex: 3,
                    child: Text(
                      'Test Name',
                      style: TextStyle(
                        color: kPrimaryTextColor,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                Expanded(
                    flex: 2,
                    child: Text(
                      'Date',
                      style: TextStyle(
                        color: kPrimaryTextColor,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                Expanded(
                    flex: 2,
                    child: Text(
                      'Duration',
                      style: TextStyle(
                        color: kPrimaryTextColor,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                Expanded(
                    flex: 1,
                    child: Text(
                      'Score',
                      style: TextStyle(
                        color: kPrimaryTextColor,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                /*  Expanded(
                    flex: 2,
                    child: Text('Status',
                        style: TextStyle(fontWeight: FontWeight.bold))),*/
                Expanded(
                    flex: 2,
                    child: Padding(
                      padding: EdgeInsets.only(left: 8),
                      child: Text(
                        'Result',
                        style: TextStyle(
                          color: kPrimaryTextColor,
                          fontSize: 16,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )),
                Expanded(
                    flex: 2,
                    child: Text('',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('',
                        style: TextStyle(
                            fontWeight: FontWeight.bold))), // For expand icon
              ],
            ),
          ),
          // Test rows with accordion functionality
          ...groupedTests.asMap().entries.map((entry) {
            final index = entry.key;
            final groupedTest = entry.value;
            return _buildAccordionTestRow(groupedTest, index + 1);
          }),
        ],
      ),
    );
  }

  Widget _buildAccordionTestRow(
      GroupedSectionalTestAttempts groupedTest, int serialNumber) {
    final isExpanded = _expandedTestIds.contains(groupedTest.testId);
    final latestAttempt = groupedTest.latestAttempt;

    print("Grouped Test Data is --> $groupedTest");

    return Column(
      children: [
        // Main row
        InkWell(
          onTap: () => groupedTest.previousAttempts.isNotEmpty
              ? _toggleExpansion(groupedTest.testId)
              : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
            ),
            child: Row(
              children: [
                // Serial Number
                Expanded(
                  flex: 1,
                  child: Text(
                    '$serialNumber',
                    style: const TextStyle(
                      color: kPrimaryTextColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                    ),
                  ),
                ),
                // Test Name with question count
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        groupedTest.testName,
                        style: const TextStyle(
                          color: kPrimaryTextColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          fontFamily: 'Poppins',
                        ),
                      ),
                      Text(
                        '${latestAttempt.totalQuestions} Questions',
                        style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF808080),
                        ),
                      ),
                    ],
                  ),
                ),
                // Date
                Expanded(
                  flex: 2,
                  child: Text(
                    _formatDate(latestAttempt.attemptDate),
                    style: const TextStyle(
                      color: kPrimaryTextColor,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                // Duration
                Expanded(
                  flex: 2,
                  child: Text(
                    latestAttempt.duration,
                    style: const TextStyle(
                      color: kPrimaryTextColor,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                // Score
                Expanded(
                  flex: 1,
                  child: Text(
                    '${latestAttempt.scorePercentage.toStringAsFixed(0)}%',
                    style: const TextStyle(
                      color: kPrimaryTextColor,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // Status

                Expanded(
                  flex: 2,
                  child: TextButton.icon(
                    onPressed: () {
                      context.go('/test-result/${latestAttempt.testAttemptId}');
                    },
                    label: const Text(
                      'View Summary',
                      style: TextStyle(
                        color: kPrimaryColor,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.50,
                      ),
                    ),
                    iconAlignment: IconAlignment.end,
                    icon: const Icon(Icons.arrow_forward, size: 16),
                    style: TextButton.styleFrom(
                      foregroundColor: kPrimaryColor,
                      padding: const EdgeInsets.fromLTRB(0, 0, 8, 0),
                    ),
                  ),
                ),

                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      // this OnPressed action should launch the test again
                      _retakeTest(context, groupedTest);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: kPrimaryColor,
                      side: const BorderSide(color: kPrimaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Retake test'),
                  ),
                ),
                // Expand/Collapse Icon
                groupedTest.previousAttempts.isNotEmpty
                    ? Expanded(
                        flex: 1,
                        child: Icon(
                          isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: Colors.grey[600],
                        ),
                      )
                    : Expanded(flex: 1, child: Text('')),
              ],
            ),
          ),
        ),
        // Expanded content
        if (isExpanded) _buildExpandedContent(groupedTest),
      ],
    );
  }

  Widget _buildExpandedContent(GroupedSectionalTestAttempts groupedTest) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F9FA),
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Details Section
          //I comment this line as this functionality is not required now, But Dont remove this code as it may be required in future.
          /*  _buildTestDetailsSection(groupedTest.latestAttempt),
          const SizedBox(height: 24),*/

          // Test History Section
          if (groupedTest.previousAttempts.isNotEmpty) ...[
            _buildTestHistorySection(groupedTest.previousAttempts),
            const SizedBox(height: 24),
          ],

          // Action Buttons are not required for now.
          // _buildActionButtons(groupedTest.latestAttempt),
        ],
      ),
    );
  }

  Widget _buildTestDetailsSection(SectionalTestAttemptSummary latestAttempt) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Test Details',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildDetailItem('Test ID',
                    '#${latestAttempt.testAttemptId.substring(0, 8)}'),
              ),
              Expanded(
                child: _buildDetailItem(
                    'Questions', '${latestAttempt.totalQuestions} questions'),
              ),
              Expanded(
                child: _buildDetailItem(
                    'Date Taken', _formatDate(latestAttempt.attemptDate)),
              ),
              Expanded(
                child: _buildDetailItem('Duration', latestAttempt.duration),
              ),
              Expanded(
                child: _buildDetailItem('Score',
                    '${latestAttempt.scorePercentage.toStringAsFixed(0)}%'),
              ),
              Expanded(
                child: _buildDetailItem('Status', latestAttempt.status.name),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTestHistorySection(
      List<SectionalTestAttemptSummary> previousAttempts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Test History',
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
            height: 1.50,
            color: kPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        ...previousAttempts.map((attempt) => _buildHistoryItem(attempt)),
      ],
    );
  }

  Widget _buildHistoryItem(SectionalTestAttemptSummary attempt) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              _formatDateTime(attempt.attemptDate),
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              attempt.duration,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              '${attempt.scorePercentage.toStringAsFixed(0)}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: TextButton.icon(
              onPressed: () {
                context.go('/test-result/${attempt.testAttemptId}');
              },
              label: const Text(
                'View Summary',
                style: TextStyle(
                  color: kPrimaryColor,
                  fontSize: 16,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 1.50,
                ),
              ),
              iconAlignment: IconAlignment.end,
              icon: const Icon(Icons.arrow_forward, size: 16),
              style: TextButton.styleFrom(
                foregroundColor: kPrimaryColor,
                padding: const EdgeInsets.fromLTRB(0, 0, 8, 0),
              ),
            ),
            /*   TextButton(
              onPressed: () {
                print("Test Attempt Id: ${attempt.testAttemptId}");
                context.go('/test-result/${attempt.testAttemptId}');
              },
              child: const Text(
                'View Summary',
                style: TextStyle(fontSize: 12),
              ),
            ),*/
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(SectionalTestAttemptSummary latestAttempt) {
    return Row(
      children: [
        ElevatedButton(
          onPressed: () {
            context.go('/test-result/${latestAttempt.testAttemptId}');
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('View Results'),
        ),
        const SizedBox(width: 12),
        OutlinedButton.icon(
          onPressed: () {
            // TODO: Implement download certificate functionality
          },
          icon: const Icon(Icons.download, size: 16),
          label: const Text('Download Certificate'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
        const SizedBox(width: 12),
        OutlinedButton(
          onPressed: () {
            context.read<PastTestsBloc>().add(RetakeTestEvent(
                TestTypes.sectional, int.parse(latestAttempt.testId)));
            //TODO - TestTypes.sectional is hardcoded here, we can make it dynamic in future to include daily tests as well
          },
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('Retake Test'),
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(TestStatus status) {
    switch (status) {
      case TestStatus.completed:
        return Colors.green;
      case TestStatus.inProgress:
      case TestStatus.started:
        return Colors.orange;
      case TestStatus.notStarted:
        return Colors.grey;
      case TestStatus.abandoned:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(TestStatus status) {
    switch (status) {
      case TestStatus.completed:
        return 'Completed';
      case TestStatus.inProgress:
        return 'In Progress';
      case TestStatus.started:
        return 'Started';
      case TestStatus.notStarted:
        return 'Not Started';
      case TestStatus.abandoned:
        return 'Abandoned';
      default:
        return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
  }
}
