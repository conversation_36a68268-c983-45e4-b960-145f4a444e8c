part of 'past_tests_bloc.dart';

abstract class PastTestsEvent extends Equatable {
  const PastTestsEvent();

  @override
  List<Object> get props => [];
}

class PastTestsWithType extends PastTestsEvent {
  final TestTypes testType;

  const PastTestsWithType(this.testType);

  @override
  List<Object> get props => [testType];
}

class LoadPastTests extends PastTestsWithType {
  const LoadPastTests(super.testType);
  @override
  List<Object> get props => [testType];
}

class OnLoadPastTest extends PastTestsWithType {
  const OnLoadPastTest(super.testType);
  @override
  List<Object> get props => [testType];
}

class RetakeTestEvent extends PastTestsWithType {
  final int testId;

  const RetakeTestEvent(super.testType, this.testId);

  @override
  List<Object> get props => [testId, testType];
}
