import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/past_test.dart';
import '../../domain/usecases/get_past_tests.dart';
import '../../domain/usecases/retake_test.dart';
import '../../../test/domain/entitites/test.dart';
import '../../../test/domain/usecases/fetch_grouped_sectional_test_attempt_history.dart';

part 'past_tests_event.dart';
part 'past_tests_state.dart';

class PastTestsBloc extends Bloc<PastTestsEvent, PastTestsState> {
  final GetPastTests getPastTests;
  final FetchGroupedSectionalTestAttemptHistory
      fetchGroupedSectionalTestAttemptHistory;
  final RetakeTest retakeTest;

  PastTestsBloc({
    required this.getPastTests,
    required this.fetchGroupedSectionalTestAttemptHistory,
    required this.retakeTest,
  }) : super(PastTestsInitial()) {
    on<LoadPastTests>(_onLoadPastTests);
    on<OnLoadPastTest>(_onLoadPastTest);
    on<RetakeTestEvent>(_onRetakeTest);
  }

  Future<void> _onLoadPastTests(
    LoadPastTests event,
    Emitter<PastTestsState> emit,
  ) async {
    emit(PastTestsLoading());
    final result = await getPastTests(NoParams());
    result.fold(
      (failure) => emit(PastTestsError(_mapFailureToMessage(failure))),
      (tests) => emit(PastTestsLoaded(tests)),
    );
  }

  Future<void> _onLoadPastTest(
    OnLoadPastTest event,
    Emitter<PastTestsState> emit,
  ) async {
    emit(PastTestsLoading());
    try {
      final result =
          await fetchGroupedSectionalTestAttemptHistory(event.testType);
      result.fold(
        (failure) => emit(const PastTestsError(
            'Failed to load past tests. Please try again.')),
        (groupedTests) => emit(GroupedPastTestsLoaded(groupedTests)),
      );
    } catch (e) {
      emit(
          const PastTestsError('Failed to load past tests. Please try again.'));
    }
  }

  Future<void> _onRetakeTest(
    RetakeTestEvent event,
    Emitter<PastTestsState> emit,
  ) async {
    emit(RetakingTest());
    event.testType; // Use this if you need to handle different test types
    final result = await retakeTest(RetakeTestParams(testId: event.testId));
    result.fold(
      (failure) => emit(PastTestsError(_mapFailureToMessage(failure))),
      (_) => add(LoadPastTests(event.testType)), // Reload tests after retaking
    );
  }

  String _mapFailureToMessage(Failure failure) {
    // Map different failure types to user-friendly messages
    return 'Failed to load past tests. Please try again.';
  }
}
