part of 'past_tests_bloc.dart';

abstract class PastTestsState extends Equatable {
  const PastTestsState();

  @override
  List<Object> get props => [];
}

class PastTestsInitial extends PastTestsState {}

class PastTestsLoading extends PastTestsState {}

class RetakingTest extends PastTestsState {}

class PastTestsLoaded extends PastTestsState {
  final List<PastTest> tests;

  const PastTestsLoaded(this.tests);

  @override
  List<Object> get props => [tests];
}

class GroupedPastTestsLoaded extends PastTestsState {
  final List<GroupedSectionalTestAttempts> groupedTests;

  const GroupedPastTestsLoaded(this.groupedTests);

  @override
  List<Object> get props => [groupedTests];
}

class PastTestsError extends PastTestsState {
  final String message;

  const PastTestsError(this.message);

  @override
  List<Object> get props => [message];
}
