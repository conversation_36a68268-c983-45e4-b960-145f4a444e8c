import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/admin/domain/entities/question_bundle.dart';
import 'package:skillapp/src/admin/domain/entities/question_upload_data.dart';

abstract class ExcelRemoteDataSource {
  Future<Either<Failure, Unit>> uploadQuestionBundles(
      List<QuestionBundle> questionBundles,
      String subject,
      );

  Future<Either<Failure, Unit>> uploadTestQuestions(
      List<QuestionUploadData> testQuestions,
      String subject,
      );
}

class ExcelRemoteDataSourceImpl implements ExcelRemoteDataSource {
  final FirebaseFirestore firestore;

  ExcelRemoteDataSourceImpl({required this.firestore});

  @override
  Future<Either<Failure, Unit>> uploadQuestionBundles(
      List<QuestionBundle> questionBundles,
      String subject,
      ) async {
    try {
      WriteBatch batch = firestore.batch();

      // Get the current highest bundle number for this subject
      int currentBundleNumber = await _getNextBundleNumber(subject);

      for (final questionBundle in questionBundles) {
        // Check if we can add to existing bundle or need to create new one
        final bundleNumber = await _findOrCreateBundle(
          subject,
          questionBundle.level,
          questionBundle.questionIds.length,
          currentBundleNumber,
        );

        // Create document reference for this bundle
        final DocumentReference bundleDocRef = firestore
            .collection('subjects')
            .doc(subject)
            .collection('question_bundles')
            .doc(bundleNumber.toString());

        // Add the question IDs to the bundle as subcollection documents
        await _addQuestionIdsToBundle(batch, bundleDocRef,
            questionBundle.questionIds, questionBundle.level);

        currentBundleNumber = bundleNumber;
      }

      await batch.commit();
      return const Right(unit);
    } on FirebaseException catch (e) {
      print('Firestore upload error: ${e.code} - ${e.message}');
      return Left(FirestoreUploadFailure(
          message: 'Firestore error: ${e.message ?? e.code}'));
    } catch (e) {
      print('Unexpected upload error: ${e.toString()}');
      return Left(
          FirestoreUploadFailure(message: 'Unexpected error: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Unit>> uploadTestQuestions(
      List<QuestionUploadData> testQuestions,
      String subject,
      ) async {
    try {
      WriteBatch batch = firestore.batch();

      // Group questions by testId
      final Map<String, List<QuestionUploadData>> questionsByTestId = {};

      for (final question in testQuestions) {
        final testId = question.jsonDataMap['testId'] as String?;
        if (testId != null && testId.isNotEmpty) {
          questionsByTestId.putIfAbsent(testId, () => []);
          questionsByTestId[testId]!.add(question);
        }
      }

      // Upload each test's questions
      for (final entry in questionsByTestId.entries) {
        final testId = entry.key;
        final questions = entry.value;

        // Create/update the test master document
        final testMasterRef = firestore.collection('tests_master').doc(testId);

        // Get test metadata from first question
        final firstQuestion = questions.first;
        final testName = firstQuestion.jsonDataMap['name'] as String?;
        final testSubject = firstQuestion.jsonDataMap['subject'] as String?;

        // Set/update test master document
        final testMasterData = <String, dynamic>{
          'name': testName ?? 'Test $testId',
          'subject': testSubject ?? subject,
        };

        batch.set(testMasterRef, testMasterData, SetOptions(merge: true));

        // Upload individual questions to subjects/{subject}/questions/{questionId}
        for (final question in questions) {
          final questionId = question.questionId;

          final questionDocRef = firestore
              .collection('subjects')
              .doc(subject)
              .collection('questions')
              .doc(questionId);

          // Convert the entire question data to JSON string
          final questionData = Map<String, dynamic>.from(question.jsonDataMap);
          questionData.remove('testId');
          questionData.remove('questionId');

          final String questionJsonString = jsonEncode(questionData);

          final documentData = {
            'data': questionJsonString,
          };

          batch.set(questionDocRef, documentData);

          // Add question ID reference to test master
          final questionIdRef =
          testMasterRef.collection('question_ids').doc(questionId);

          batch.set(questionIdRef, <String, dynamic>{});
        }
      }

      await batch.commit();
      return const Right(unit);
    } on FirebaseException catch (e) {
      print('Firestore test upload error: ${e.code} - ${e.message}');
      return Left(FirestoreUploadFailure(
          message: 'Firestore error: ${e.message ?? e.code}'));
    } catch (e) {
      print('Unexpected test upload error: ${e.toString()}');
      return Left(
          FirestoreUploadFailure(message: 'Unexpected error: ${e.toString()}'));
    }
  }

  Future<int> _getNextBundleNumber(String subject) async {
    try {
      final QuerySnapshot snapshot = await firestore
          .collection('subjects')
          .doc(subject)
          .collection('question_bundles')
          .orderBy(FieldPath.documentId, descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        return 1;
      }

      final String lastBundleId = snapshot.docs.first.id;
      return (int.tryParse(lastBundleId) ?? 0) + 1;
    } catch (e) {
      print('Error getting next bundle number: $e');
      return 1;
    }
  }

  Future<int> _findOrCreateBundle(
      String subject,
      String level,
      int questionsToAdd,
      int currentBundleNumber,
      ) async {
    try {
      final DocumentSnapshot currentBundleDoc = await firestore
          .collection('subjects')
          .doc(subject)
          .collection('question_bundles')
          .doc(currentBundleNumber.toString())
          .get();

      if (currentBundleDoc.exists) {
        final data = currentBundleDoc.data() as Map<String, dynamic>?;
        final String? bundleLevel = data?['level'];

        // Check if levels match and there's space (max 10 questions per bundle)
        if (bundleLevel == level) {
          // Count existing question IDs in subcollection
          final QuerySnapshot questionIdsSnapshot = await firestore
              .collection('subjects')
              .doc(subject)
              .collection('question_bundles')
              .doc(currentBundleNumber.toString())
              .collection('question_ids')
              .get();

          final int currentCount = questionIdsSnapshot.docs.length;

          if (currentCount + questionsToAdd <= 10) {
            return currentBundleNumber;
          }
        }
      }

      return currentBundleNumber + 1;
    } catch (e) {
      print('Error finding or creating bundle: $e');
      return currentBundleNumber + 1;
    }
  }

  Future<void> _addQuestionIdsToBundle(
      WriteBatch batch,
      DocumentReference bundleDocRef,
      List<String> questionIds,
      String level,
      ) async {
    try {
      final Map<String, dynamic> bundleData = {
        'level': level,
      };
      batch.set(bundleDocRef, bundleData, SetOptions(merge: true));

      final QuerySnapshot existingQuestionIds =
      await bundleDocRef.collection('question_ids').get();

      final Set<String> existingIds =
      existingQuestionIds.docs.map((doc) => doc.id).toSet();

      // Add new question IDs as documents in the subcollection
      for (final questionId in questionIds) {
        if (!existingIds.contains(questionId)) {
          final DocumentReference questionIdDocRef =
          bundleDocRef.collection('question_ids').doc(questionId);

          batch.set(questionIdDocRef, <String, dynamic>{});
        }
      }
    } catch (e) {
      print('Error adding question IDs to bundle: $e');
      rethrow;
    }
  }
}