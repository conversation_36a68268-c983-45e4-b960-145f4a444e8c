import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:excel/excel.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/admin/domain/entities/question_upload_data.dart';
import 'package:skillapp/src/admin/domain/entities/question_bundle.dart';
import 'package:skillapp/src/admin/domain/entities/subject.dart';
import '../../domain/repos/excel_repository.dart';
import '../datasources/excel_remote_data_source.dart';
import 'package:skillapp/src/admin/domain/entities/upload_type.dart';

class ExcelRepositoryImpl implements ExcelRepository {
  final ExcelRemoteDataSource remoteDataSource;

  ExcelRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, Unit>> pickParseAndUploadExcel({
    required Subject subject,
    required String filePath,
    required UploadType uploadType,
  }) async {
    final Either<Failure, List<QuestionUploadData>> parseResult =
        await _parseExcelFile(filePath: filePath, uploadType: uploadType);

    return await parseResult.fold(
      (failure) => Left(failure),
      (questionsDataList) async {
        if (questionsDataList.isEmpty) {
          return const Left(
            NoDataFoundFailure(message: "No data parsed from Excel to upload."),
          );
        }

        final String subjectString = subject.name;

        if (uploadType == UploadType.practice) {
          // Handle practice questions (existing logic)
          final questionBundles = _createQuestionBundles(questionsDataList);

          if (questionBundles.isEmpty) {
            return const Left(
              NoDataFoundFailure(message: "No valid question bundles created."),
            );
          }

          return await remoteDataSource.uploadQuestionBundles(
            questionBundles,
            subjectString,
          );
        } else {
          // Handle test questions (new logic)
          return await remoteDataSource.uploadTestQuestions(
            questionsDataList,
            subjectString,
          );
        }
      },
    );
  }

  List<QuestionBundle> _createQuestionBundles(
      List<QuestionUploadData> questionsDataList) {
    // Group questions by level
    final Map<String, List<String>> questionsByLevel = {};

    for (final questionData in questionsDataList) {
      final level = _extractLevelFromQuestionData(questionData);
      if (level.isNotEmpty) {
        questionsByLevel.putIfAbsent(level, () => []);
        questionsByLevel[level]!.add(questionData.questionId);
      }
    }

    // Create bundles of 10 questions each
    final List<QuestionBundle> bundles = [];

    questionsByLevel.forEach((level, questionIds) {
      // Split questions into groups of 10
      for (int i = 0; i < questionIds.length; i += 10) {
        final endIndex =
            (i + 10 < questionIds.length) ? i + 10 : questionIds.length;
        final bundleQuestions = questionIds.sublist(i, endIndex);

        bundles.add(QuestionBundle(
          level: level,
          questionIds: bundleQuestions,
        ));
      }
    });

    return bundles;
  }

  String _extractLevelFromQuestionData(QuestionUploadData questionData) {
    final jsonData = questionData.jsonDataMap;

    final levelKeys = ['level', 'difficulty', 'Level', 'Difficulty'];

    for (final key in levelKeys) {
      if (jsonData.containsKey(key) && jsonData[key] != null) {
        return jsonData[key].toString().trim();
      }
    }
    return '1';
  }

  Future<Either<Failure, List<QuestionUploadData>>> _parseExcelFile({
    required String filePath,
    required UploadType uploadType,
  }) async {
    try {
      final File file = File(filePath);
      if (!await file.exists()) {
        return const Left(FilePickingFailure(
          message: 'File not found at the provided path.',
        ));
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      if (excel.tables.isEmpty) {
        return const Left(NoDataFoundFailure(
          message: 'No sheets found in the Excel file.',
        ));
      }

      final sheet = excel.tables.values.first;
      if (sheet.rows.isEmpty) {
        return const Left(NoDataFoundFailure(
          message: 'The sheet is empty.',
        ));
      }

      // Extract headers
      final headers = sheet.rows.first
          .map((cell) => _unwrapCellValue(cell?.value)?.toString().trim() ?? '')
          .toList();

      if (uploadType == UploadType.practice) {
        return _parsePracticeExcel(sheet, headers);
      } else {
        return _parseTestExcel(sheet, headers);
      }
    } catch (e, st) {
      print('Error parsing Excel: $e');
      print(st);
      return Left(ExcelParsingFailure(
        message: 'Error parsing Excel: $e',
      ));
    }
  }

  Future<Either<Failure, List<QuestionUploadData>>> _parsePracticeExcel(
      Sheet sheet, List<String> headers) async {
    const subjectHeader = 'Type';
    const serialNoHeader = 'Serial No.';

    if (!headers.contains(subjectHeader)) {
      return const Left(ExcelParsingFailure(
        message: 'Missing required column "Type".',
      ));
    }

    if (!headers.contains(serialNoHeader)) {
      return const Left(ExcelParsingFailure(
        message: 'Missing required column "Serial No.".',
      ));
    }

    final List<QuestionUploadData> dataModels = [];

    for (int i = 1; i < sheet.rows.length; i++) {
      final row = sheet.rows[i];
      if (row.every((c) =>
          c == null ||
          _unwrapCellValue(c.value)?.toString().trim().isEmpty == true))
        continue;

      final Map<String, dynamic> rowMap = {};
      for (var j = 0; j < headers.length; j++) {
        final key = headers[j];
        if (key.isEmpty) continue;
        final cell = j < row.length ? row[j] : null;
        final raw = _unwrapCellValue(cell?.value);
        rowMap[key] = raw;
      }

      final subject = rowMap[subjectHeader]?.toString().trim();
      if (subject == null || subject.isEmpty) continue;

      final serialNo = rowMap[serialNoHeader]?.toString().trim();
      if (serialNo == null || serialNo.isEmpty) {
        print('Warning: Empty Serial No. found in row ${i + 1}, skipping...');
        continue;
      }

      // Extract module/type before removing it
      final moduleType = rowMap[subjectHeader]?.toString().trim();

      rowMap.remove(subjectHeader);
      rowMap.remove(serialNoHeader);

      final transformedJsonData = _transformToStructuredFormat(rowMap);

      // Add the module to the transformed data
      if (moduleType != null && moduleType.isNotEmpty) {
        transformedJsonData['module'] = moduleType;
      }

      dataModels.add(QuestionUploadData(
        subject: subject,
        questionId: serialNo,
        jsonDataMap: transformedJsonData,
      ));
    }

    if (dataModels.isEmpty) {
      return const Left(NoDataFoundFailure(
        message: 'No valid question data found.',
      ));
    }

    return Right(dataModels);
  }

  Future<Either<Failure, List<QuestionUploadData>>> _parseTestExcel(
      Sheet sheet, List<String> headers) async {
    const testIdHeader = 'Test ID';
    const questionIdHeader = 'Question_ID';
    const nameHeader = 'Name';
    const subjectHeader = 'Subject';

    if (!headers.contains(testIdHeader)) {
      return const Left(ExcelParsingFailure(
        message: 'Missing required column "Test ID" for test upload.',
      ));
    }

    if (!headers.contains(questionIdHeader)) {
      return const Left(ExcelParsingFailure(
        message: 'Missing required column "Question_ID" for test upload.',
      ));
    }

    final List<QuestionUploadData> dataModels = [];

    for (int i = 1; i < sheet.rows.length; i++) {
      final row = sheet.rows[i];
      if (row.every((c) =>
          c == null ||
          _unwrapCellValue(c.value)?.toString().trim().isEmpty == true))
        continue;

      final Map<String, dynamic> rowMap = {};
      for (var j = 0; j < headers.length; j++) {
        final key = headers[j];
        if (key.isEmpty) continue;
        final cell = j < row.length ? row[j] : null;
        final raw = _unwrapCellValue(cell?.value);
        rowMap[key] = raw;
      }

      final testId = rowMap[testIdHeader]?.toString().trim();
      final questionId = rowMap[questionIdHeader]?.toString().trim();
      final name = rowMap[nameHeader]?.toString().trim();
      final subject = rowMap[subjectHeader]?.toString().trim();

      if (testId == null || testId.isEmpty) {
        print('Warning: Empty Test ID found in row ${i + 1}, skipping...');
        continue;
      }

      if (questionId == null || questionId.isEmpty) {
        print('Warning: Empty Question_ID found in row ${i + 1}, skipping...');
        continue;
      }

      rowMap.remove(testIdHeader);
      rowMap.remove(questionIdHeader);
      rowMap.remove(nameHeader);
      rowMap.remove(subjectHeader);

      final transformedJsonData = _transformToStructuredFormat(rowMap);

      transformedJsonData['testId'] = testId;
      transformedJsonData['questionId'] = questionId;

      if (name != null && name.isNotEmpty) {
        transformedJsonData['name'] = name;
      }

      if (subject != null && subject.isNotEmpty) {
        transformedJsonData['subject'] = subject;
      }

      dataModels.add(QuestionUploadData(
        subject: subject ?? 'unknown',
        questionId: questionId,
        jsonDataMap: transformedJsonData,
      ));
    }

    if (dataModels.isEmpty) {
      return const Left(NoDataFoundFailure(
        message: 'No valid test data found.',
      ));
    }

    return Right(dataModels);
  }

  Map<String, dynamic> _transformToStructuredFormat(
      Map<String, dynamic> rowData) {
    final Map<String, dynamic> structuredData = {};

    // Extract question data with image support
    final questionContent = _extractQuestionWithImages(rowData);
    if (questionContent.isNotEmpty) {
      structuredData['question'] = questionContent;
    }

    // Extract options
    final options = _extractOptions(rowData);
    if (options.isNotEmpty) {
      structuredData['options'] = options;
    }

    // Extract correct answer
    final correctAnswer = _extractCorrectAnswer(rowData);
    if (correctAnswer.isNotEmpty) {
      final match = options.firstWhere(
        (option) =>
            option['description'] != null &&
            option['description'] is List &&
            (option['description'] as List).any((desc) =>
                desc['description']?.toString().trim() == correctAnswer.trim()),
        orElse: () => <String, dynamic>{},
      );

      if (match['id'] != null) {
        structuredData['correctAnswer'] = match['id'];
      } else {
        structuredData['correctAnswer'] = correctAnswer;
      }
    }

    // Extract explanation
    final explanation = _extractExplanation(rowData);
    if (explanation.isNotEmpty) {
      structuredData['explanation'] = [
        {
          'type': "text",
          'description': explanation,
        }
      ];
    }

    // Extract help tip
    final helpTip = _extractHelpTip(rowData);
    if (helpTip.isNotEmpty) {
      structuredData['helptip'] = [
        {'type': 'text', 'description': helpTip}
      ];
    }

    rowData.forEach((key, value) {
      if (value != null && value.toString().trim().isNotEmpty) {
        final cleanKey = key.toLowerCase().replaceAll(' ', '_');

        switch (cleanKey) {
          case 'level':
          case 'difficulty':
            structuredData['level'] = value.toString();
            break;
          case 'complexity':
            structuredData['complexity'] = value.toString();
            break;
          case 'module':
          case 'modules':
            structuredData['module'] = value.toString();
            break;
          case 'short_description':
          case 'description':
            if (!_isQuestionField(cleanKey)) {
              structuredData['short_description'] = value.toString();
            }
            break;
          case 'stackable':
            structuredData['stackable'] =
                value.toString().toLowerCase() == 'true' ||
                    value.toString() == '1';
            break;
          default:
            // Skip fields that are already processed
            if (!_isProcessedField(cleanKey)) {
              structuredData[cleanKey] = value;
            }
        }
      }
    });

    return structuredData;
  }

  List<Map<String, dynamic>> _extractQuestionWithImages(
      Map<String, dynamic> rowData) {
    final List<Map<String, dynamic>> questionContent = [];

    final questionText = _extractQuestionText(rowData);
    if (questionText.isEmpty) {
      return questionContent;
    }

    // Handle both <text> and <image> tags
    final RegExp combinedRegex = RegExp(r'<(text|image)>(.*?)</\1>');

    if (combinedRegex.hasMatch(questionText)) {
      // Process each tag match in order
      for (final match in combinedRegex.allMatches(questionText)) {
        final tagType = match.group(1)?.toLowerCase() ?? '';
        final content = match.group(2)?.trim() ?? '';

        if (content.isNotEmpty) {
          if (tagType == 'text') {
            questionContent.add({
              'type': 'text',
              'description': content
            });
          } else if (tagType == 'image') {
            questionContent.add({
              'type': 'image',
              'isAnimated': 'false',
              'images': [
                {'path': content, 'tooltip': 'Image'}
              ]
            });
          }
        }
      }
    } else {
    
      final RegExp imageRegex = RegExp(r'<image>(.*?)</image>');

      if (imageRegex.hasMatch(questionText)) {
        int lastIndex = 0;

        for (final match in imageRegex.allMatches(questionText)) {
          if (match.start > lastIndex) {
            final textBefore =
            questionText.substring(lastIndex, match.start).trim();
            if (textBefore.isNotEmpty) {
              questionContent.add({'type': 'text', 'description': textBefore});
            }
          }

          // Extract image path from the tag
          final imagePath = match.group(1)?.trim() ?? '';
          if (imagePath.isNotEmpty) {
            questionContent.add({
              'type': 'image',
              'isAnimated': 'false',
              'images': [
                {'path': imagePath, 'tooltip': 'Image'}
              ]
            });
          }

          lastIndex = match.end;
        }

        if (lastIndex < questionText.length) {
          final textAfter = questionText.substring(lastIndex).trim();
          if (textAfter.isNotEmpty) {
            questionContent.add({'type': 'text', 'description': textAfter});
          }
        }
      } else {
        questionContent.add({'type': 'text', 'description': questionText});
      }
    }

    return questionContent;
  }

  String _extractQuestionText(Map<String, dynamic> rowData) {
    final questionKeys = ['question', 'questions', 'question_text', 'text'];

    for (final key in rowData.keys) {
      final lowerKey = key.toLowerCase().replaceAll(' ', '_');
      if (questionKeys.contains(lowerKey) || lowerKey.contains('question')) {
        final value = rowData[key];
        if (value != null && value.toString().trim().isNotEmpty) {
          return value.toString().trim();
        }
      }
    }
    return '';
  }

  List<Map<String, dynamic>> _extractOptions(Map<String, dynamic> rowData) {
    final List<Map<String, dynamic>> options = [];
    final Map<String, String> optionMap = {};

    rowData.forEach((key, value) {
      if (value != null && value.toString().trim().isNotEmpty) {
        // Check for single letter option keys
        if (RegExp(r'^[A-E]$').hasMatch(key.trim())) {
          final optionId = key.trim().toUpperCase();
          optionMap[optionId] = value.toString().trim();
        }
      }
    });

    optionMap.forEach((id, description) {
      options.add({
        'id': id,
        'description': [
          {'type': 'text', 'description': description}
        ]
      });
    });

    return options;
  }

  String _extractCorrectAnswer(Map<String, dynamic> rowData) {
    final answerKeys = [
      'correct_answer',
      'answer',
      'correct',
      'right_answer',
      'correctanswer',
    ];

    for (final key in rowData.keys) {
      final lowerKey = key.toLowerCase().replaceAll(' ', '_');
      if (answerKeys.contains(lowerKey)) {
        final value = rowData[key];
        if (value != null && value.toString().trim().isNotEmpty) {
          return value.toString().trim();
        }
      }
    }
    return '';
  }

  String _extractExplanation(Map<String, dynamic> rowData) {
    final explanationKeys = ['explanation', 'exp', 'explain', 'solution'];

    for (final key in rowData.keys) {
      final lowerKey = key.toLowerCase().replaceAll(' ', '_');
      if (explanationKeys.contains(lowerKey)) {
        final value = rowData[key];
        if (value != null && value.toString().trim().isNotEmpty) {
          return value.toString().trim();
        }
      }
    }
    return '';
  }

  String _extractHelpTip(Map<String, dynamic> rowData) {
    final helpTipKeys = ['help_tip', 'helptip', 'tip', 'hint'];

    for (final key in rowData.keys) {
      final lowerKey = key.toLowerCase().replaceAll(' ', '_');
      if (helpTipKeys.contains(lowerKey)) {
        final value = rowData[key];
        if (value != null && value.toString().trim().isNotEmpty) {
          return value.toString().trim();
        }
      }
    }
    return '';
  }

  bool _isQuestionField(String key) {
    final questionFields = ['question', 'questions', 'question_text'];
    return questionFields.contains(key) || key.contains('question');
  }

  bool _isProcessedField(String key) {
    final processedFields = [
      'question',
      'questions',
      'question_text',
      'correct_answer',
      'correctanswer',
      'answer',
      'explanation',
      'helptip',
      'help_tip',
      'test_id',
      'question_id',
      'name',
      'subject'
    ];
    // Check for single letter option keys (a, b, c, d, e)
    if (RegExp(r'^[a-e]$').hasMatch(key)) {
      return true;
    }

    dynamic _unwrapCellValue(dynamic cellValue) {
      if (cellValue == null) return null;

      if (cellValue is String) return cellValue;
      if (cellValue is num) return cellValue;
      if (cellValue is bool) return cellValue;

      if (cellValue.runtimeType.toString() == 'TextSpan') {
        try {
          // Try to get the text property from TextSpan
          return cellValue.text?.toString() ?? '';
        } catch (e) {
          return cellValue.toString();
        }
      }

      try {
        final value = cellValue.value;

        if (value != null && value.runtimeType.toString() == 'TextSpan') {
          return value.text?.toString() ?? '';
        }

        return value;
      } catch (e) {
        try {
          return cellValue.toString();
        } catch (e2) {
          print(
              'Warning: Could not unwrap cell value: $cellValue (${cellValue.runtimeType})');
          return '';
        }
      }
    }

    return processedFields.contains(key);
  }

  dynamic _unwrapCellValue(dynamic cellValue) {
    if (cellValue == null) return null;

    if (cellValue is String) return cellValue;
    if (cellValue is num) return cellValue;
    if (cellValue is bool) return cellValue;

    if (cellValue.runtimeType.toString() == 'TextSpan') {
      try {
        // Try to get the text property from TextSpan
        return cellValue.text?.toString() ?? '';
      } catch (e) {
        return cellValue.toString();
      }
    }

    try {
      final value = cellValue.value;

      if (value != null && value.runtimeType.toString() == 'TextSpan') {
        return value.text?.toString() ?? '';
      }

      return value;
    } catch (e) {
      try {
        return cellValue.toString();
      } catch (e2) {
        print(
            'Warning: Could not unwrap cell value: $cellValue (${cellValue.runtimeType})');
        return '';
      }
    }
  }
}
