import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/src/admin/domain/entities/subject.dart';
import 'package:skillapp/src/admin/presentation/bloc/excel_file_upload_bloc.dart';
import 'package:skillapp/src/admin/presentation/bloc/excel_file_upload_event.dart';
import 'package:skillapp/src/admin/presentation/bloc/excel_file_upload_state.dart';
import 'package:skillapp/src/admin/domain/entities/upload_type.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  factory UploadScreen.routeBuilder(BuildContext context, GoRouterState state) {
    return const UploadScreen();
  }

  @override
  _UploadScreenState createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  String? selectedSubjectName;
  UploadType? selectedUploadType;
  String? _pickedFilePath;
  String? _pickedFileName;

  final List<String> subjects = [
    'maths',
    'reading',
    'thinking',
    'writing'
  ];

  final Map<UploadType, String> uploadTypeLabels = {
    UploadType.practice: 'Practice',
    UploadType.test: 'Test',
  };

  Future<void> _pickExcelFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        withData: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _pickedFilePath = result.files.single.path;
          _pickedFileName = result.files.single.name;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('File picking cancelled or no file selected.'), backgroundColor: Colors.orange),
        );
        setState(() {
          _pickedFilePath = null;
          _pickedFileName = null;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking file: $e'), backgroundColor: Colors.red),
      );
      setState(() {
        _pickedFilePath = null;
        _pickedFileName = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Upload Excel')),
      body: BlocProvider(
        create: (context) => sl<ExcelFileUploadBloc>(),
        child: BlocConsumer<ExcelFileUploadBloc, ExcelFileUploadState>(
          listener: (context, state) {
            if (state is ExcelFileUploadSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message), backgroundColor: Colors.green),
              );
              // clear the selection after successful upload
              setState(() {
                selectedSubjectName = null;
                selectedUploadType = null;
                _pickedFilePath = null;
                _pickedFileName = null;
              });
            } else if (state is ExcelFileUploadError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message), backgroundColor: Colors.red),
              );
            }
          },
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Subject Dropdown
                  DropdownButtonFormField<String>(
                    value: selectedSubjectName,
                    hint: const Text("Select Subject"),
                    items: subjects
                        .map((subject) =>
                        DropdownMenuItem(value: subject, child: Text(subject)))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedSubjectName = value;
                      });
                    },
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'Subject',
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Upload Type Dropdown
                  DropdownButtonFormField<UploadType>(
                    value: selectedUploadType,
                    hint: const Text("Select Upload Type"),
                    items: UploadType.values
                        .map((type) => DropdownMenuItem(
                        value: type,
                        child: Text(uploadTypeLabels[type]!)))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedUploadType = value;
                      });
                    },
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'Upload Type',
                    ),
                  ),
                  const SizedBox(height: 20),

                  ElevatedButton.icon(
                    onPressed: _pickExcelFile,
                    icon: const Icon(Icons.attach_file),
                    label: const Text('Select Excel File'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                    ),
                  ),
                  const SizedBox(height: 10),
                  if (_pickedFileName != null)
                    Text('Selected file: $_pickedFileName')
                  else
                    const Text('No file selected.'),
                  const SizedBox(height: 30),
                  if (state is ExcelFileUploadLoading)
                    const CircularProgressIndicator()
                  else
                    ElevatedButton.icon(
                      onPressed: selectedSubjectName == null ||
                          selectedUploadType == null ||
                          _pickedFilePath == null
                          ? null
                          : () {
                        final Subject? selectedSubjectEnum = SubjectExtension.fromName(selectedSubjectName!);

                        if (selectedSubjectEnum != null && _pickedFilePath != null) {
                          context.read<ExcelFileUploadBloc>().add(
                            PickParseUploadExcelFile(
                              subject: selectedSubjectEnum,
                              filePath: _pickedFilePath!,
                              uploadType: selectedUploadType!,
                            ),
                          );
                        } else {
                          String errorMessage = 'Unknown error.';
                          if (selectedSubjectName == null) {
                            errorMessage = 'Please select a subject.';
                          } else if (selectedUploadType == null) {
                            errorMessage = 'Please select upload type.';
                          } else if (_pickedFilePath == null) {
                            errorMessage = 'Please select an Excel file.';
                          } else if (selectedSubjectEnum == null) {
                            errorMessage = 'Invalid subject selected.';
                          }
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text(errorMessage), backgroundColor: Colors.orange),
                          );
                        }
                      },
                      icon: const Icon(Icons.upload_file),
                      label: const Text('Upload Selected File'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                    ),
                  const SizedBox(height: 20),
                  if (state is ExcelFileUploadInitial)
                    const Text('Select subject, upload type, pick an Excel file, then press upload.'),
                  if (state is ExcelFileUploadSuccess)
                    const Text('Upload complete! You can upload another file.'),
                  if (state is ExcelFileUploadError)
                    Column(
                      children: [
                        const Text('Upload failed.', style: TextStyle(color: Colors.red)),
                        Text(state.message,
                            style: const TextStyle(color: Colors.redAccent, fontSize: 12)),
                      ],
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}