import 'package:equatable/equatable.dart';
import 'package:skillapp/src/admin/domain/entities/subject.dart';
import 'package:skillapp/src/admin/domain/entities/upload_type.dart';

abstract class ExcelFileUploadEvent extends Equatable {
  const ExcelFileUploadEvent();

  @override
  List<Object> get props => [];
}

class PickParseUploadExcelFile extends ExcelFileUploadEvent {
  final Subject subject;
  final String filePath;
  final UploadType uploadType;

  const PickParseUploadExcelFile({
    required this.subject,
    required this.filePath,
    required this.uploadType,
  });

  @override
  List<Object> get props => [subject, filePath, uploadType];
}