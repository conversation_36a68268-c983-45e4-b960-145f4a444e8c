import 'package:equatable/equatable.dart';

abstract class ExcelFileUploadState extends Equatable {
  const ExcelFileUploadState();

  @override
  List<Object> get props => [];
}

class ExcelFileUploadInitial extends ExcelFileUploadState {
  const ExcelFileUploadInitial();
}

class ExcelFileUploadLoading extends ExcelFileUploadState {
  const ExcelFileUploadLoading();
}

class ExcelFileUploadSuccess extends ExcelFileUploadState {
  final String message;

  const ExcelFileUploadSuccess({this.message = 'File uploaded successfully!'});

  @override
  List<Object> get props => [message];
}

class ExcelFileUploadError extends ExcelFileUploadState {
  final String message;

  const ExcelFileUploadError({required this.message});

  @override
  List<Object> get props => [message];
}