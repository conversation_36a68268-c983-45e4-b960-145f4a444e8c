import 'package:bloc/bloc.dart';
import 'package:skillapp/src/admin/domain/usecases/upload_excel_data_usecase.dart';
import 'excel_file_upload_event.dart';
import 'excel_file_upload_state.dart';
import 'package:skillapp/src/admin/domain/entities/upload_type.dart';

class ExcelFileUploadBloc
    extends Bloc<ExcelFileUploadEvent, ExcelFileUploadState> {
  final PickParseAndUploadExcelUsecase usecase;

  ExcelFileUploadBloc({ required this.usecase })
      : super(const ExcelFileUploadInitial()) {
    on<PickParseUploadExcelFile>(_onPickParseUploadExcelFile);
  }

  Future<void> _onPickParseUploadExcelFile(
      PickParseUploadExcelFile event,
      Emitter<ExcelFileUploadState> emit,
      ) async {
    emit(const ExcelFileUploadLoading());

    final result = await usecase.call(
      filePath: event.filePath,
      subject: event.subject,
      uploadType: event.uploadType,
    );

    result.fold(
          (failure) {
        emit(ExcelFileUploadError(message: failure.message));
      },
          (_) {
        final uploadTypeText = event.uploadType == UploadType.practice ? 'practice' : 'test';
        emit(ExcelFileUploadSuccess(
          message: 'Excel file processed and $uploadTypeText data uploaded successfully!',
        ));
      },
    );
  }
}