import 'package:dartz/dartz.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/admin/domain/entities/subject.dart';
import 'package:skillapp/src/admin/domain/entities/upload_type.dart';

abstract class ExcelRepository {
  Future<Either<Failure, Unit>> pickParseAndUploadExcel({
    required Subject subject,
    required String filePath,
    required UploadType uploadType,
  });
}