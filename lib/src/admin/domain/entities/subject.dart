enum Subject {
  maths,
  reading,
  thinking,
  writing,
}

extension SubjectExtension on Subject {
  String get name {
    switch (this) {
      case Subject.maths:
        return 'maths';
      case Subject.reading:
        return 'reading';
      case Subject.thinking:
        return 'thinking';
      case Subject.writing:
        return 'writing';
    }
  }

  static Subject? fromName(String name) {
    switch (name.toLowerCase()) {
      case 'maths':
        return Subject.maths;
      case 'reading':
        return Subject.reading;
      case 'thinking':
        return Subject.thinking;
      case 'writing':
        return Subject.writing;
      default:
        return null;
    }
  }
}