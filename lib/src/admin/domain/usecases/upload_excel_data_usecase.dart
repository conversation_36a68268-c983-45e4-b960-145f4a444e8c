import 'package:dartz/dartz.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/admin/domain/repos/excel_repository.dart';
import 'package:skillapp/src/admin/domain/entities/subject.dart';
import 'package:skillapp/src/admin/domain/entities/upload_type.dart';

class PickParseAndUploadExcelUsecase {
  final ExcelRepository repository;

  const PickParseAndUploadExcelUsecase({required this.repository});

  Future<Either<Failure, Unit>> call({
    required String filePath,
    required Subject subject,
    required UploadType uploadType,
  }) async {
    return await repository.pickParseAndUploadExcel(
      filePath: filePath,
      subject: subject,
      uploadType: uploadType,
    );
  }
}