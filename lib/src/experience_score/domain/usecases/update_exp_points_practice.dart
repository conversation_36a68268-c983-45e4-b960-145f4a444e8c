import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/experience_score/common/experience_points_cache.dart';
import 'package:skillapp/src/experience_score/common/experience_points_enums.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_repo.dart';

class UpdateExperienceScore
    extends FutureUsecaseWithParams<void, UpdateExperienceScoreParams> {
  final ExperienceScoreRepo _experienceScoreRepo;
  final CacheContext _cacheContext;
  final ExperiencePointsCache _experiencePointsCache;

  UpdateExperienceScore(
      {required ExperienceScoreRepo experienceScoreRepo,
      required CacheContext cacheContext,
      required ExperiencePointsCache experiencePointsCache})
      : _experienceScoreRepo = experienceScoreRepo,
        _cacheContext = cacheContext,
        _experiencePointsCache = experiencePointsCache;

  @override
  ResultFuture<void> call(params) async {
    int points = _experiencePointsCache.getPointsForActivity(
        params.activity, params.result, params.complexity);

    await _experienceScoreRepo.updateExperienceScoreForTheProfile(
        _cacheContext.profileId, points);

    ExperiencePointEntry experiencePointEntry = ExperiencePointEntry(
      activity: params.activity,
      result: params.result,
      complexity: params.complexity,
      points: points,
    );

    return await _experienceScoreRepo.addAuditEntryForExpPointChange(
        _cacheContext.profileId, experiencePointEntry);
  }
}

class UpdateExperienceScoreParams {
  final ExperiencePointsActivityType activity;
  final ExperiencePointsResultType result;
  final ExperiencePointsComplexityType complexity;

  UpdateExperienceScoreParams(
      {required this.activity, required this.result, required this.complexity});
}
