import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_repo.dart';

class FetchExperienceScore extends FutureUsecaseWithoutParams<ExperienceScore> {
  FetchExperienceScore({required ExperienceScoreRepo experienceScoreRepo})
      : _experienceScoreRepo = experienceScoreRepo,
        super();

  final ExperienceScoreRepo _experienceScoreRepo;

  @override
  ResultFuture<ExperienceScore> call() =>
      _experienceScoreRepo.getExperienceScore();
}
