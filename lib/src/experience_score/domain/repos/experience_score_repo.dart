import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';

abstract class ExperienceScoreRepo {
  ResultFuture<ExperienceScore> getExperienceScore();

  ResultFuture<void> updateExperienceScoreForTheProfile(
      String profileId, int points);

  ResultFuture<void> addAuditEntryForExpPointChange(
      String profileId, ExperiencePointEntry entry);
}
