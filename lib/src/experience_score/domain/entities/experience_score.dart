import 'package:equatable/equatable.dart';
import 'package:skillapp/src/experience_score/common/experience_points_enums.dart';

class ExperienceScore extends Equatable {
  final int score;

  const ExperienceScore(this.score);

  @override
  List<Object> get props => [score];
}

class ExperienceScoreConfig extends Equatable {
  final ExperiencePointsActivityType activityType;
  final int failureEasy;
  final int failureMedium;
  final int failureHard;
  final int successEasy;
  final int successMedium;
  final int successHard;

  const ExperienceScoreConfig({
    required this.activityType,
    required this.failureEasy,
    required this.failureMedium,
    required this.failureHard,
    required this.successEasy,
    required this.successMedium,
    required this.successHard,
  });

  @override
  List<Object> get props => [activityType];
}

class ExperiencePointEntry extends Equatable {
  final ExperiencePointsActivityType activity;
  final ExperiencePointsResultType result;
  final ExperiencePointsComplexityType complexity;
  final int points;

  const ExperiencePointEntry(
      {required this.activity,
      required this.result,
      required this.complexity,
      required this.points});

  @override
  List<Object?> get props => [activity, result, complexity, points];
}
