import 'package:skillapp/core/errors/exceptions.dart';

enum ExperiencePointsActivityType { practice, test }

enum ExperiencePointsResultType { success, failure }

enum ExperiencePointsComplexityType { easy, medium, hard }

//convert to string
String experiencePointResultTypeToString(ExperiencePointsResultType type) {
  switch (type) {
    case ExperiencePointsResultType.success:
      return 'success';
    case ExperiencePointsResultType.failure:
      return 'failure';
  }
}

//string to enum
ExperiencePointsResultType stringToExperiencePointResultType(String value) {
  switch (value) {
    case 'success':
      return ExperiencePointsResultType.success;
    case 'failure':
      return ExperiencePointsResultType.failure;
    default:
      throw ServerException(
          message: 'Invalid ExperiencePointResultType $value',
          statusCode: '500');
  }
}

//convert to string
String experiencePointsActivityTypeToString(ExperiencePointsActivityType type) {
  switch (type) {
    case ExperiencePointsActivityType.practice:
      return 'practice';
    case ExperiencePointsActivityType.test:
      return 'test';
  }
}

//string to enum
ExperiencePointsActivityType stringToExperiencePointsActivityType(
    String value) {
  switch (value) {
    case 'practice':
      return ExperiencePointsActivityType.practice;
    case 'test':
      return ExperiencePointsActivityType.test;
    default:
      throw ServerException(
          message: 'Invalid ExperiencePointsActivityType $value',
          statusCode: '500');
  }
}

//convert to string
String experiencePointsLevelTypeToString(ExperiencePointsComplexityType type) {
  switch (type) {
    case ExperiencePointsComplexityType.easy:
      return 'easy';
    case ExperiencePointsComplexityType.medium:
      return 'medium';
    case ExperiencePointsComplexityType.hard:
      return 'hard';
  }
}

//string to enum
ExperiencePointsComplexityType stringToExperiencePointsLevelType(String value) {
  switch (value) {
    case 'easy':
      return ExperiencePointsComplexityType.easy;
    case 'medium':
      return ExperiencePointsComplexityType.medium;
    case 'hard':
      return ExperiencePointsComplexityType.hard;
    default:
      throw ServerException(
          message: 'Invalid ExperiencePointsLevelType $value',
          statusCode: '500');
  }
}

enum ExperiencePointDataType {
  failureEasy,
  failureMedium,
  failureHard,
  successEasy,
  successMedium,
  successHard
}

//convert to string
String experiencePointDataTypeToString(ExperiencePointDataType type) {
  switch (type) {
    case ExperiencePointDataType.failureEasy:
      return 'failure_easy';
    case ExperiencePointDataType.failureMedium:
      return 'failure_medium';
    case ExperiencePointDataType.failureHard:
      return 'failure_hard';
    case ExperiencePointDataType.successEasy:
      return 'success_easy';
    case ExperiencePointDataType.successMedium:
      return 'success_medium';
    case ExperiencePointDataType.successHard:
      return 'success_hard';
  }
}

//string to enum
ExperiencePointDataType stringToExperiencePointDataType(String value) {
  switch (value) {
    case 'failure_easy':
      return ExperiencePointDataType.failureEasy;
    case 'failure_medium':
      return ExperiencePointDataType.failureMedium;
    case 'failure_hard':
      return ExperiencePointDataType.failureHard;
    case 'success_easy':
      return ExperiencePointDataType.successEasy;
    case 'success_medium':
      return ExperiencePointDataType.successMedium;
    case 'success_hard':
      return ExperiencePointDataType.successHard;
    default:
      throw ServerException(
          message: 'Invalid ExperiencePointDataType $value', statusCode: '500');
  }
}

ExperiencePointDataType getExperiencePointDataType(
    ExperiencePointsResultType result,
    ExperiencePointsComplexityType complexity) {
  switch (result) {
    case ExperiencePointsResultType.success:
      switch (complexity) {
        case ExperiencePointsComplexityType.easy:
          return ExperiencePointDataType.successEasy;
        case ExperiencePointsComplexityType.medium:
          return ExperiencePointDataType.successMedium;
        case ExperiencePointsComplexityType.hard:
          return ExperiencePointDataType.successHard;
      }
    case ExperiencePointsResultType.failure:
      switch (complexity) {
        case ExperiencePointsComplexityType.easy:
          return ExperiencePointDataType.failureEasy;
        case ExperiencePointsComplexityType.medium:
          return ExperiencePointDataType.failureMedium;
        case ExperiencePointsComplexityType.hard:
          return ExperiencePointDataType.failureHard;
      }
  }
}
