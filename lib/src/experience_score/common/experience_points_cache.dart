import 'package:skillapp/src/experience_score/common/experience_points_enums.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_config_repo.dart';

class ExperiencePointsCache {
  final Map<ExperiencePointsActivityType, ExperienceScoreConfig>
      _xpPointsConfigcache = {};
  final ExperienceScoreConfigRepo _experienceScoreConfigRepo;

  /*ExperiencePointsCache(this._experienceScoreConfigRepo) {
    initConfig();
  }*/

  ExperiencePointsCache(
      {required ExperienceScoreConfigRepo experienceScoreConfigRepo})
      : _experienceScoreConfigRepo = experienceScoreConfigRepo {
    initConfig();
  }

  void initConfig() {
    final result = _experienceScoreConfigRepo.getExperienceScoreConfig();

    result.then((value) {
      value.fold((l) => null, (r) {
        for (var element in r) {
          ExperienceScoreConfig e = element;
          _xpPointsConfigcache[e.activityType] = e;
        }
      });
    });
  }

  void clear() {
    _xpPointsConfigcache.clear();
  }

  ExperienceScoreConfig getExperiencePointsConfigData(
      ExperiencePointsActivityType activityType) {
    return _xpPointsConfigcache[activityType]!;
  }

  int getPointsForActivity(
      ExperiencePointsActivityType activityType,
      ExperiencePointsResultType result,
      ExperiencePointsComplexityType complexity) {
    ExperienceScoreConfig? conf = _xpPointsConfigcache[activityType];

    if (conf == null) {
      return 0;
    }

    ExperiencePointDataType dataType =
        getExperiencePointDataType(result, complexity);
    switch (dataType) {
      case ExperiencePointDataType.successEasy:
        return conf.successEasy;
      case ExperiencePointDataType.successMedium:
        return conf.successMedium;
      case ExperiencePointDataType.successHard:
        return conf.successHard;
      case ExperiencePointDataType.failureEasy:
        return conf.failureEasy;
      case ExperiencePointDataType.failureMedium:
        return conf.failureMedium;
      case ExperiencePointDataType.failureHard:
        return conf.failureHard;
      default:
        return 0;
    }
  }
}
