import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/src/experience_score/common/experience_points_enums.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';

abstract class ExperienceScoreRemoteDataSource {
  Future<int> getExperienceScore(String email, String profileId);

  Future<List<ExperienceScoreConfig>> getExperienceScoreConfig();

  Future<void> updateExperienceScoreForTheProfile(String profileId, int points);

  Future<void> addAuditEntryForExpPointChange(
      String profileId, ExperiencePointEntry entry);
}

class ExperienceScoreRemoteDataSourceImpl
    implements ExperienceScoreRemoteDataSource {
  final FirebaseFirestore _firestore;

  ExperienceScoreRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  @override
  Future<int> getExperienceScore(String email, String profileId) async {
    //return doc.get('experience_score');

    try {
      DocumentSnapshot<Map<String, dynamic>> experienceScoreDoc =
          await _firestore
              .collection('experience_score_audit')
              .doc(profileId)
              .get();
      if (experienceScoreDoc.exists) {
        Map<String, dynamic> experienceScoreData = experienceScoreDoc.data()!;
        return experienceScoreData['current_score'];
      } else {
        throw const ServerException(
            message: 'Experience score data not available', statusCode: '404');
      }
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<List<ExperienceScoreConfig>> getExperienceScoreConfig() async {
    try {
      /* QuerySnapshot questionBundleSnapshot =
          await _firebaseFirestore.collection('skill_point_config').get();

      return questionBundleSnapshot.docs.map((config) {
        return SkillpointsConfig(
            eligiblePoints: config['eligible_points'],
            maxPointsAllowedPerDay: config['max_limit_per_day'],
            skillPointEventType: stringToSkillEventType(config.id));
      }).toList();
*/
      QuerySnapshot questionBundleSnapshot =
          await _firestore.collection('experience_score_config').get();
      return questionBundleSnapshot.docs.map((config) {
        print(config.id);
        //Map<String, dynamic> configData = config.data();
        return ExperienceScoreConfig(
            failureEasy: config['failure_easy'],
            failureMedium: config['failure_medium'],
            failureHard: config['failure_hard'],
            successEasy: config['success_easy'],
            successMedium: config['success_medium'],
            successHard: config['success_hard'],
            activityType: stringToExperiencePointsActivityType(config.id));
      }).toList();
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e, s) {
      print("Error: $e");
      debugPrintStack(stackTrace: s);
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<void> updateExperienceScoreForTheProfile(
      String profileId, int points) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> profileDoc = await _firestore
          .collection('experience_score_audit')
          .doc(profileId)
          .get();

      if (!profileDoc.exists) {
        await _firestore
            .collection('experience_score_audit')
            .doc(profileId)
            .set({
          'mod_time': DateTime.now(),
          'current_score': points,
        });
      } else {
        //fetch the current balance
        int totalPoints = profileDoc['current_score'];

        await _firestore
            .collection('experience_score_audit')
            .doc(profileId)
            .update({
          'mod_time': DateTime.now(),
          'current_score': totalPoints + points
        });
      }
      return;
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }

  @override
  Future<void> addAuditEntryForExpPointChange(
      String profileId, ExperiencePointEntry entry) async {
    try {
      return await _firestore
          .collection('experience_score_audit/$profileId/audit_entries')
          .doc()
          .set(
        {
          'cre_time': DateTime.now(),
          'score': entry.points,
          'activity': experiencePointsActivityTypeToString(entry.activity),
          'result': experiencePointResultTypeToString(entry.result),
          'complexity': experiencePointsLevelTypeToString(entry.complexity),
        },
      );
    } on FirebaseException catch (e) {
      throw ServerException(
          message: e.message ?? 'Unknown error', statusCode: e.code);
    } on ServerException {
      rethrow;
    } catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(message: e.toString(), statusCode: '500');
    }
  }
}
