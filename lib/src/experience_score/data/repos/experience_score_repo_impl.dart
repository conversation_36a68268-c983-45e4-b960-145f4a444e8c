import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/experience_score/data/datasources/experience_score_remote_datasource.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_repo.dart';

class ExperienceScoreRepoImpl extends ExperienceScoreRepo {
  final ExperienceScoreRemoteDataSource _experienceScoreRemoteDataSource;
  final CacheContext _cacheContext;

  ExperienceScoreRepoImpl(
      {required ExperienceScoreRemoteDataSource experienceScoreRemoteDataSource,
      required CacheContext cacheContext})
      : _experienceScoreRemoteDataSource = experienceScoreRemoteDataSource,
        _cacheContext = cacheContext;

  @override
  ResultFuture<ExperienceScore> getExperienceScore() async {
    try {
      final result = await _experienceScoreRemoteDataSource.getExperienceScore(
          _cacheContext.email, _cacheContext.profileId);

      return Right(ExperienceScore(result));
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> addAuditEntryForExpPointChange(
      String profileId, ExperiencePointEntry entry) async {
    try {
      await _experienceScoreRemoteDataSource.addAuditEntryForExpPointChange(
          profileId, entry);

      return const Right(null);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> updateExperienceScoreForTheProfile(
      String profileId, int points) async {
    try {
      await _experienceScoreRemoteDataSource.updateExperienceScoreForTheProfile(
          profileId, points);

      return const Right(null);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }
}
