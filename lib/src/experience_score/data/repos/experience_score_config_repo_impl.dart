import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/experience_score/data/datasources/experience_score_remote_datasource.dart';
import 'package:skillapp/src/experience_score/domain/entities/experience_score.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_config_repo.dart';

class ExperienceScoreConfigRepoImpl extends ExperienceScoreConfigRepo {
  final ExperienceScoreRemoteDataSource _dataSource;
  ExperienceScoreConfigRepoImpl(
      {required ExperienceScoreRemoteDataSource dataSource})
      : _dataSource = dataSource;

  @override
  ResultFuture<List<ExperienceScoreConfig>> getExperienceScoreConfig() async {
    try {
      final result = await _dataSource.getExperienceScoreConfig();

      return Right(result);
    } on ServerException catch (e, s) {
      debugPrintStack(stackTrace: s);
      return Left(ServerFailure.fromException(e));
    }
  }
}
