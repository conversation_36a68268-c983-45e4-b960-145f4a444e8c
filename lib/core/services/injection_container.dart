import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';
import 'package:skillapp/src/practice/domain/usecases/check_practice_limit_exceeded_usecase.dart';
import 'package:skillapp/src/subscription_config/data/datasources/attempted_question_count_remote_datasource.dart';
import 'package:skillapp/src/subscription_config/data/datasources/subscription_config_remote_datasource.dart';
import 'package:skillapp/src/subscription_config/data/repos/attempted_question_count_repo_impl.dart';
import 'package:skillapp/src/subscription_config/data/repos/subscription_config_repo_impl.dart';
import 'package:skillapp/src/subscription_config/domain/repos/attempted_question_count_repo.dart';
import 'package:skillapp/src/subscription_config/domain/repos/subscription_config_repo.dart';
import 'package:skillapp/src/subscription_config/domain/usecases/fetch_and_cache_subscription_config.dart';
import 'package:skillapp/src/subscription_config/domain/usecases/fetch_attempted_question_count_usecase.dart';
import 'package:skillapp/src/test/domain/usecases/daily_test/fetch_todays_daily_test.dart';
import 'package:skillapp/src/test/domain/usecases/daily_test/is_daily_test_attempted.dart';
import '../../src/edit_profile/data/datasources/editprofile_remote_data_source.dart';
import '../../src/edit_profile/data/repos/editprofile_repo_impl.dart';
import '../../src/subscription/di/subscription_injection_container.dart'
    as subscription_di;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/features/questions/data/datasources/questions_remote_datasource.dart';
import 'package:skillapp/core/common/features/questions/data/repos/question_repo_impl.dart';
import 'package:skillapp/core/common/features/questions/data/repos/util/question_repo_util.dart';
import 'package:skillapp/core/common/features/questions/domain/repos/question_repo.dart';
import 'package:skillapp/core/common/features/questions/domain/usecases/fetch_single_question_data.dart';
import 'package:skillapp/core/common/features/user/data/datasources/user_remote_data_source.dart';
import 'package:skillapp/core/common/features/user/data/repos/user_repo_impl.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/features/user/domain/repos/user_repo.dart';
import 'package:skillapp/core/common/features/user/domain/usecases/get_current_profile.dart';
import 'package:skillapp/core/common/features/user/domain/usecases/set_current_profile.dart';
import 'package:skillapp/core/common/providers/user_provider.dart';
import 'package:skillapp/src/attempt_history/data/datasources/attempt_history_remote_datasource.dart';
import 'package:skillapp/src/attempt_history/data/repos/attempt_history_repo_impl.dart';
import 'package:skillapp/src/attempt_history/domain/repos/attempt_history_repo.dart';
import 'package:skillapp/src/attempt_history/domain/usecases/fetch_attempt_history.dart';
import 'package:skillapp/src/attempt_history/presentation/blocs/attempt_history_bloc.dart';
import 'package:skillapp/src/auth/data/datasources/auth_remote_data_source.dart';
import 'package:skillapp/src/auth/data/repos/auth_repo_impl.dart';
import 'package:skillapp/src/auth/domain/repos/auth_repo.dart';
import 'package:skillapp/src/auth/domain/usecases/create_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/fetch_profile.dart'
    as auth_usecases;
import 'package:skillapp/src/auth/domain/usecases/get_user_auth_changes.dart';
import 'package:skillapp/src/auth/domain/usecases/populate_default_profile.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_in.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_out.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_up.dart';
import 'package:skillapp/src/auth/domain/usecases/sign_up_google.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';
import 'package:skillapp/src/auth/presentation/cubits/login_cubit.dart';
import 'package:skillapp/src/auth/presentation/cubits/signup_cubit.dart';
import 'package:skillapp/src/experience_score/common/experience_points_cache.dart';
import 'package:skillapp/src/experience_score/data/datasources/experience_score_remote_datasource.dart';
import 'package:skillapp/src/experience_score/data/repos/experience_score_config_repo_impl.dart';
import 'package:skillapp/src/experience_score/data/repos/experience_score_repo_impl.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_config_repo.dart';
import 'package:skillapp/src/experience_score/domain/repos/experience_score_repo.dart';
import 'package:skillapp/src/experience_score/domain/usecases/fetch_experience_score.dart';
import 'package:skillapp/src/experience_score/domain/usecases/update_exp_points_practice.dart';
import 'package:skillapp/src/flag_question/data/datasources/flag_question_remote_datasources.dart';
import 'package:skillapp/src/flag_question/data/repos/flaged_question_repo_impl.dart';
import 'package:skillapp/src/flag_question/domain/repos/flagged_questions_repo.dart';
import 'package:skillapp/src/flag_question/domain/usecases/check_question_flagged.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_flagged_questions.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_flagged_subjects.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_next_questionid_from_flagged_list.dart';
import 'package:skillapp/src/flag_question/domain/usecases/flag_question.dart';
import 'package:skillapp/src/flag_question/domain/usecases/remove_flag.dart';
import 'package:skillapp/src/flag_question/presentation/blocs/flagged_questions_bloc.dart';
import 'package:skillapp/src/home/<USER>/datasources/subjects_remote_data_source.dart';
import 'package:skillapp/src/home/<USER>/repos/subjects_repo_impl.dart';
import 'package:skillapp/src/home/<USER>/repos/subjects_repo.dart';
import 'package:skillapp/src/home/<USER>/usecases/fetch_subjects.dart';
import 'package:skillapp/src/home/<USER>/blocs/dashboard_summary/dashboard_summary_bloc.dart';
import 'package:skillapp/src/home/<USER>/blocs/home/<USER>';
import 'package:skillapp/src/practice/data/datasources/practice_questionbank_remote_datasource.dart';
import 'package:skillapp/src/practice/data/repos/practice_questionbank_repo_impl.dart';
import 'package:skillapp/src/practice/domain/repos/practice_questionbank_repo.dart';
import 'package:skillapp/src/practice/domain/usecases/fetch_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/load_next_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/populate_practice_questionbank.dart';
import 'package:skillapp/src/practice/domain/usecases/reset_question_for_retry.dart';
import 'package:skillapp/src/practice/domain/usecases/submit_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/update_selected_option.dart';
import 'package:skillapp/src/practice/domain/usecases/validate_practice_question_buffer.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:skillapp/src/skillpoints/data/datasources/skillpoints_remote_datasource.dart';
import 'package:skillapp/src/skillpoints/data/repos/skillpoints_config_repo_impl.dart';
import 'package:skillapp/src/skillpoints/data/repos/skillpoints_repo_impl.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoints_cache.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_config_repo.dart';
import 'package:skillapp/src/skillpoints/domain/repos/skillpoints_repo.dart';
import 'package:skillapp/src/skillpoints/domain/usecases/fetch_total_skill_points.dart';
import 'package:skillapp/src/skillpoints/domain/usecases/update_skill_points.dart';
import 'package:skillapp/src/streaks/data/datasources/streaks_remote_datasource.dart';
import 'package:skillapp/src/streaks/data/repos/streak_details_repo_impl.dart';
import 'package:skillapp/src/streaks/domain/common/streak_cache.dart';
import 'package:skillapp/src/streaks/domain/repos/streak_details_repo.dart';
import 'package:skillapp/src/streaks/domain/usecases/config/fetch_streak_config_by_id.dart';
import 'package:skillapp/src/streaks/domain/usecases/config/fetch_streak_config_data.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/add_current_day_entry_to_current_streak.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/fetch_current_day_streak_entry.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/increment_streakday_along_with_adding_if_not_present.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/add_new_streak_for_the_user.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/add_streak_day_to_a_streak.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/mark_streak_day_as_completed.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_complete_streaks_data.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_day_count.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_details.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_entries.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_current_streak_summary.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_streak_details.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/fetch_streak_status.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/mark_streak_as_inactive.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_maintenance/update_current_streak_details.dart';
import 'package:skillapp/src/streaks/presentation/blocs/config/streaks_config_bloc.dart';
import 'package:skillapp/src/streaks/presentation/blocs/streaks/streaks_bloc.dart';
import 'package:skillapp/src/edit_profile/presentation/blocs/edit_profile_bloc.dart';
import 'package:skillapp/src/edit_profile/domain/repos/editprofile_repo.dart';
import 'package:skillapp/src/edit_profile/domain/usecases/fetch_profile.dart'
    as edit_profile_usecases;
import 'package:skillapp/src/edit_profile/domain/usecases/update_profile.dart';
import 'package:skillapp/src/streaks/presentation/providers/current_day_streak_status_provider.dart';
import 'package:skillapp/src/test/data/datasources/tests_config_remote_datasource.dart';
import 'package:skillapp/src/test/data/datasources/tests_data_remote_datasource.dart';
import 'package:skillapp/src/test/data/repos/tests_configuration_repo_impl.dart';
import 'package:skillapp/src/test/data/repos/tests_data_repo_impl.dart';
import 'package:skillapp/src/test/domain/common/tests_config_cache.dart';
import 'package:skillapp/src/test/domain/repos/tests_configuration_repo.dart';
import 'package:skillapp/src/test/domain/repos/tests_data_repo.dart';
import 'package:skillapp/src/test/domain/usecases/answer_a_question.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_new_sectional_test.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_grouped_sectional_test_attempt_history.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_sectional_test_attempt_details.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_sectional_test_attempt_history.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/fetch_sectional_test_attempt_status.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_test_attempt_analysis.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_test_questions_with_answer_status.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_test_result_summary.dart';
import 'package:skillapp/src/test/domain/usecases/fetch_tests_data.dart';
import 'package:skillapp/src/test/domain/usecases/mark_test_as_started.dart';
import 'package:skillapp/src/test/domain/usecases/sectional_test/submit_sectional_test.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/admin/data/datasources/excel_remote_data_source.dart';
import 'package:skillapp/src/admin/data/repo/excel_repository_impl.dart';
import 'package:skillapp/src/admin/domain/repos/excel_repository.dart';
import 'package:skillapp/src/admin/domain/usecases/upload_excel_data_usecase.dart';
import 'package:skillapp/src/admin/presentation/bloc/excel_file_upload_bloc.dart';
import 'package:file_picker/file_picker.dart';

// Past Tests
import 'package:skillapp/src/past_tests/data/repositories/past_test_repository_impl.dart';
import 'package:skillapp/src/past_tests/domain/repositories/past_test_repository.dart';
import 'package:skillapp/src/past_tests/domain/usecases/get_past_tests.dart';
import 'package:skillapp/src/past_tests/domain/usecases/retake_test.dart';
import 'package:skillapp/src/past_tests/presentation/blocs/past_tests_bloc.dart';

part 'injection_container.main.dart';
