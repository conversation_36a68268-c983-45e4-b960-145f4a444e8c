part of 'injection_container.dart';

final sl = GetIt.instance;

Future<void> init() async {
  await _initCore();
  await _initUser();
  await _initAuth();
  await _initQuestions();
  await _initHome();
  await _initAdmin();
  await _initExcelFileUpload();

  FirebaseAuth firebaseAuth = sl();

  if (firebaseAuth.currentUser != null) {
    print("JB:Inside router user not null");
    UserProvider userProvider = sl();
    await userProvider.populateUserProfile();
    print("JB: After populateUserProfile in injection container");
    CacheContext cacheContext = sl();
    LocalUser user = cacheContext.getCurrentUser();
    print("JB:Inside router user not null user= $user");
  } else {
    print("JB:Inside router user is null hence not logged in");
  }

  await _initFlagQuestion();
  await _initSkillPoints();
  await _initExperienceScore();

  await _initDashboardSummary();
  await _initSubscriptionConfig();

  await _initPractice();
  await _initAttemptHistory();
  await _initStreaksConfig();
  await _initStreaks();
  await _initTests();
  await _initPastTests();
  await subscription_di.initSubscriptionDependencies();
  await _initEditProfile();
}

Future<void> _initCore() async {
  sl.registerSingleton<CacheContext>(CacheContextImpl());
}

Future<void> _initUser() async {
  sl
    ..registerLazySingleton(() => FirebaseFirestore.instance)
    ..registerLazySingleton(() => FirebaseAuth.instance)
    ..registerLazySingleton(() => GoogleSignIn())
    ..registerLazySingleton<UserRemoteDataSource>(
      () =>
          UserRemoteDataSourceImpl(firebaseFirestore: sl(), firebaseAuth: sl()),
    )
    ..registerLazySingleton<UserRepo>(
      () => UserRepoImpl(userRemoteDataSource: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton(() => GetCurrentProfile(userRepo: sl()))
    ..registerLazySingleton(() => UserProvider(getCurrentProfile: sl()));
}

Future<void> _initAuth() async {
  sl
    ..registerLazySingleton<AuthRemoteDataSource>(
      () =>
          AuthRemoteDataSourceImpl(firebaseAuth: sl(), firebaseFirestore: sl()),
    )
    ..registerLazySingleton<AuthRepo>(
      () => AuthRepoImpl(remoteDataSource: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton(() => SignIn(authRepo: sl()))
    ..registerLazySingleton(() => SignUp(authRepo: sl()))
    ..registerLazySingleton(() => SignUpGoogle(authRepo: sl()))
    ..registerLazySingleton(() => SetCurrentUserProfile(userRepo: sl()))
    ..registerLazySingleton(
      () => PopulateDefaultProfile(authRepo: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton(() => GetUserAuthChanges(authRepo: sl()))
    ..registerLazySingleton(() => auth_usecases.FetchProfile(authRepo: sl()))
    ..registerLazySingleton(() => CreateProfile(authRepo: sl()))
    ..registerLazySingleton(() => SignOut(authRepo: sl()))
    ..registerFactory(() => LoginCubit(signIn: sl(), setCurrentProfile: sl()))
    ..registerFactory(
      () => SignupCubit(
        signUp: sl(),
        signUpGoogle: sl(),
        fetchProfile: sl(),
        createProfile: sl(),
        setCurrentProfile: sl(),
        //googleSignIn: sl(),
        populateDefaultProfile: sl(),
      ),
    )
    ..registerFactory(
      () => AppBloc(
        getCurrentProfile: sl(),
        getUserAuthChanges: sl(),
        populateDefaultProfile: sl(),
        signOut: sl(),
        firebaseAuth: sl(),
      ),
    );
}

Future<void> _initQuestions() async {
  sl
    ..registerLazySingleton(() => QuestionDataUtil())
    ..registerLazySingleton<QuestionRemoteDataSource>(
      () => QuestionRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<QuestionRepo>(
      () => QuestionRepoImpl(
        questionBankRemoteDataSource: sl(),
        questionDataUtil: sl(),
      ),
    )
    ..registerLazySingleton(() => FetchSingleQuestionData(questionRepo: sl()));
}

Future<void> _initHome() async {
  sl
    ..registerFactory(() => HomeBloc(fetchSubjects: sl()))
    ..registerLazySingleton<SubjectsRepo>(() => SubjectsRepoImpl(sl()))
    ..registerLazySingleton(
      () => FetchSubjects(subjectsRepo: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton<SubjectsRemoteDataSource>(
      () => SubjectsRemoteDataSourceImpl(firestore: sl()),
    );
}

Future<void> _initPractice() async {
  sl
    ..registerLazySingleton<PracticeQuestionBankRemoteDataSource>(
      () => QuestionBankRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<PracticeQuestionBankRepo>(
      () => PracticeQuestionBankRepoImpl(
        questionBankRemoteDataSource: sl(),
        questionDataUtil: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(
      () =>
          FetchPracticeQuestion(populateQuestionBank: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton(
      () => PopulatePracticeQuestionBank(
        questionBankRepo: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(
      () => LoadNextPracticeQuestion(
        bankRepo: sl(),
        fetchQuestion: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(() => ResetPracticeQuestionForRetry())
    ..registerLazySingleton(() => UpdateSelectedOption())
    ..registerLazySingleton(
      () => SubmitPracticeQuestion(questionBankRepo: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton(
      () => ValidatePracticeQuestionBuffer(
        populateQuestionBank: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton<CheckQuestionFlagged>(
      () => CheckQuestionFlagged(flaggedQuestionsRepo: sl()),
    )
    ..registerFactory(
      () => PracticeBloc(
        fetchQuestion: sl(),
        loadNextQuestion: sl(),
        submitQuestion: sl(),
        resetQuestionForRetry: sl(),
        populateQuestionBank: sl(),
        updateSelectedOption: sl(),
        flagQuestion: sl(),
        removeFlag: sl(),
        checkQuestionFlagged: sl(),
        fetchSingleQuestionData: sl(),
        fetchNextQuestionIdFromFlaggedList: sl(),
        incrementCurrentStreakDayAlongWithAddingIfNotPresent: sl(),
        updateSkillpoints: sl(),
        updateExperienceScore: sl(),
        cacheContext: sl(),
        checkPracticeLimitExceededUseCase: sl(),
      ),
    );
}

Future<void> _initFlagQuestion() async {
  sl
    ..registerFactory(
      () => FlaggedQuestionsBloc(
        fetchFlaggedQuestions: sl(),
        fetchFlaggedSubjects: sl(),
      ),
    )
    ..registerLazySingleton<FlaggedQuestionsRepo>(
      () =>
          FlaggedQuestionsRepoImpl(remoteDataSource: sl(), cacheContext: sl()),
    )
    ..registerLazySingleton<FlagQuestionRemoteDataSource>(
      () => FlagQuestionRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton(
      () => FetchFlaggedQuestions(flaggedQuestionsRepo: sl()),
    )
    // ..registerLazySingleton(() => CheckQuestionFlagged(flaggedQuestionsRepo: sl()))
    ..registerLazySingleton(
      () => FetchFlaggedSubjects(flaggedQuestionsRepo: sl()),
    )
    ..registerLazySingleton(() => FlagQuestion(flaggedQuestionsRepo: sl()))
    ..registerLazySingleton(() => RemoveFlag(flaggedQuestionsRepo: sl()))
    ..registerLazySingleton(
      () => FetchNextQuestionIdFromFlaggedList(cacheContext: sl()),
    );
}

Future<void> _initAttemptHistory() async {
  sl
    ..registerLazySingleton<AttemptHistoryRemoteDataSource>(
      () => AttemptHistoryRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<AttemptHistoryRepo>(
      () => AttemptHistoryRepoImpl(
        attemptHistoryRemoteDataSource: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(() => FetchAttemptHistory(repository: sl()))
    ..registerFactory(() => AttemptHistoryBloc(fetchAttemptHistory: sl()));
}

Future<void> _initStreaksConfig() async {
  sl
    ..registerLazySingleton(() => StreaksCache())
    ..registerFactory(
      () => StreaksConfigBloc(
        fetchStreakConfigById: sl(),
        fetchStreakConfigsData: sl(),
      ),
    )
    ..registerLazySingleton(
      () => FetchStreakConfigById(streakDetailsRepo: sl(), streaksCache: sl()),
    )
    ..registerLazySingleton(
      () => FetchStreakConfigsData(streakDetailsRepo: sl()),
    );
}

Future<void> _initStreaks() async {
  sl
    ..registerFactory(
      () => StreaksBloc(
        addNewDayToStreak: sl(),
        addNewStreakForTheUser: sl(),
        fetchCurrentStreakSummary: sl(),
        fetchStreakDetails: sl(),
        markStreakDayEntryAsCompleted: sl(),
        updateCurrentStreakData: sl(),
        updateCurrentStreakCount: sl(),
        updateCurrentStreakId: sl(),
        updateCurrentStreakStatus: sl(),
        incrementCurrentStreakDayAlongWithAddingIfNotPresent: sl(),
        fetchCurrentDayStreakEntry: sl(),
        fetchCompleteStreaksData: sl(),
        fetchCurrentStreakDetails: sl(),
        addCurrentDayEntryToCurrentStreak: sl(),
        fetchCurrentStreakEntries: sl(),
      ),
    )
    ..registerLazySingleton<StreakDetailsRepo>(
      () => StreakDetailsRepoImpl(
        streaksRemoteDataSource: sl(),
        streakCache: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(
      () => AddNewStreakForTheUser(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => FetchCurrentDayStreakEntry(
        streakDetailsRepo: sl(),
        streaksCache: sl(),
        fetchCurrentStreakDetails: sl(),
      ),
    )
    ..registerLazySingleton(
      () => IncrementCurrentStreakDayAlongWithAddingIfNotPresent(
        streaksCache: sl(),
        streakDetailsRepo: sl(),
        fetchCurrentStreakSummary: sl(),
        fetchCurrentDayStreakStatus: sl(),
        addNewStreakForTheUser: sl(),
        addStreakDayEntryToStreak: sl(),
      ),
    )
    ..registerLazySingleton(
      () => MarkStreakDayEntryAsCompleted(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => AddStreakDayEntryToStreak(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => AddCurrentDayEntryToCurrentStreak(
        streakDetailsRepo: sl(),
        streaksCache: sl(),
      ),
    )
    ..registerLazySingleton(
      () => FetchCurrentStreakSummary(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(() => FetchStreakDetails(streakDetailsRepo: sl()))
    ..registerLazySingleton(
      () => UpdateCurrentStreakId(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => UpdateCurrentStreakStatus(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => UpdateCurrentStreakCount(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => UpdateCurrentStreakData(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton<StreaksRemoteDataSource>(
      () => StreakRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton(
      () => FetchCompleteStreaksData(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => FetchCurrentStreakDetails(streakDetailsRepo: sl()),
    )
    ..registerLazySingleton(
      () => CurrentDayStreakStatusProvider(fetchCurrentDayStreakEntry: sl()),
    )
    ..registerLazySingleton(
      () => FetchCurrentStreakDayCount(
        streakDetailsRepo: sl(),
        fetchCurrentStreakSummary: sl(),
        fetchStreakStatus: sl(),
        markStreakAsInactive: sl(),
        fetchCurrentStreakDetails: sl(),
      ),
    )
    ..registerLazySingleton(() => FetchStreakStatus(streakDetailsRepo: sl()))
    ..registerLazySingleton(() => MarkStreakAsInactive(streakDetailsRepo: sl()))
    ..registerLazySingleton(
      () => FetchCurrentStreakEntries(
        streakDetailsRepo: sl(),
        streaksCache: sl(),
      ),
    );
}

Future<void> _initPastTests() async {
  // Bloc
  sl.registerFactory(
    () => PastTestsBloc(
      getPastTests: sl(),
      fetchGroupedSectionalTestAttemptHistory: sl(),
      retakeTest: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetPastTests(sl()));
  sl.registerLazySingleton(() => RetakeTest(sl()));

  // Repository
  sl.registerLazySingleton<PastTestRepository>(
    () => PastTestRepositoryImpl(fetchGroupedSectionalTestAttemptHistory: sl()),
  );
}

Future<void> _initEditProfile() async {
  sl
    // 1. Bloc
    ..registerFactory(
      () => EditProfileBloc(fetchProfile: sl(), updateProfile: sl()),
    )
    ..registerLazySingleton(
      () => edit_profile_usecases.FetchProfile(repository: sl()),
    )
    ..registerLazySingleton(() => UpdateProfile(repository: sl()))
    ..registerLazySingleton<EditProfileRepository>(
      () => EditProfileRepositoryImpl(remoteDataSource: sl()),
    )
    ..registerLazySingleton<EditProfileRemoteDataSource>(
      () => EditProfileRemoteDataSourceImpl(firestore: sl()),
    );
}

Future<void> _initSkillPoints() async {
  sl
    ..registerLazySingleton<SkillpointsRemoteDatasource>(
      () => SkillpointsRemoteDatasourceImpl(firebaseFirestore: sl()),
    )
    ..registerLazySingleton<SkillpointsConfigRepo>(
      () => SkillpointsConfigRepoImpl(skillPointsRemoteDatasource: sl()),
    )
    ..registerLazySingleton(() => SkillpointsCache(sl()))
    ..registerLazySingleton<SkillpointsRepo>(
      () => SkillPointsRepoImpl(
        skillPointsRemoteDatasource: sl(),
        skillPointsCache: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(() => FetchTotalSkillpoints(skillPointsRepo: sl()))
    ..registerLazySingleton(() => UpdateSkillpoints(skillPointsRepo: sl()));
}

Future<void> _initExperienceScore() async {
  sl
    ..registerLazySingleton<ExperienceScoreRemoteDataSource>(
      () => ExperienceScoreRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<ExperienceScoreConfigRepo>(
      () => ExperienceScoreConfigRepoImpl(dataSource: sl()),
    )
    ..registerLazySingleton(
      () => ExperiencePointsCache(experienceScoreConfigRepo: sl()),
    )
    ..registerLazySingleton<ExperienceScoreRepo>(
      () => ExperienceScoreRepoImpl(
        experienceScoreRemoteDataSource: sl(),
        cacheContext: sl(),
      ),
    )
    ..registerLazySingleton(
      () => FetchExperienceScore(experienceScoreRepo: sl()),
    )
    ..registerLazySingleton(
      () => UpdateExperienceScore(
        experienceScoreRepo: sl(),
        cacheContext: sl(),
        experiencePointsCache: sl(),
      ),
    );
}

Future<void> _initDashboardSummary() async {
  sl.registerLazySingleton(
    () => DashboardSummaryBloc(
      fetchTotalSkillpoints: sl(),
      fetchCurrentStreakDayCount: sl(),
      fetchExperienceScore: sl(),
    ),
  );
}

Future<void> _initSubscriptionConfig() async {
  // Register dependencies for subscription config and attempted question count
  sl
    ..registerLazySingleton<SubscriptionConfigRemoteDataSource>(
      () => SubscriptionConfigRemoteDataSourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<SubscriptionConfigRepository>(
      () => SubscriptionConfigRepositoryImpl(remoteDataSource: sl()),
    )
    ..registerLazySingleton<AttemptedQuestionCountRemoteDatasource>(
      () => AttemptedQuestionCountRemoteDatasourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<AttemptedQuestionCountRepo>(
      () => AttemptedQuestionCountRepoImpl(sl(), sl()),
    )
    ..registerLazySingleton(() => FetchAndCacheSubscriptionConfigUseCase(
          cacheContext: sl(),
          repository: sl(),
        ))
    ..registerLazySingleton(() => FetchAttemptedQuestionCountUseCase(
          attemptedQuestionCountRepo: sl(),
        ))
    ..registerLazySingleton(() => CheckPracticeLimitExceededUseCase(
          fetchAttemptedCount: sl(),
          fetchConfig: sl(),
        ));
}

Future<void> _initAdmin() async {}

Future<void> _initExcelFileUpload() async {
  if (!sl.isRegistered<FilePicker>()) {
    sl.registerLazySingleton<FilePicker>(() => FilePicker.platform);
  }

  if (!sl.isRegistered<FirebaseFirestore>()) {
    sl.registerLazySingleton<FirebaseFirestore>(
      () => FirebaseFirestore.instance,
    );
  }

  sl.registerLazySingleton<ExcelRemoteDataSource>(
    () => ExcelRemoteDataSourceImpl(firestore: sl()),
  );

  sl.registerLazySingleton<ExcelRepository>(
    () => ExcelRepositoryImpl(remoteDataSource: sl()),
  );

  sl.registerLazySingleton<PickParseAndUploadExcelUsecase>(
    () => PickParseAndUploadExcelUsecase(repository: sl()),
  );

  sl.registerFactory(() => ExcelFileUploadBloc(usecase: sl()));
}

Future<void> _initTests() async {
  sl
    ..registerLazySingleton<TestsConfigRemoteDatasource>(
      () => TestsConfigRemoteDatasourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<TestsDataRemoteDatasource>(
      () => TestsDataRemoteDatasourceImpl(firestore: sl()),
    )
    ..registerLazySingleton<TestsConfigurationRepo>(
      () => TestsConfigurationRepoImpl(remoteDatasource: sl()),
    )
    ..registerSingleton(TestsConfigCache(sl()))
    ..registerLazySingleton<TestsDataRepo>(
      () => TestsDataRepoImpl(
        testsDataRemoteDatasource: sl(),
        questionRepo: sl(),
        cacheContext: sl(),
      ),
    )
    //register all the usecases under tests bloc
    ..registerLazySingleton(() => FetchTestsData(testsDataRepo: sl()))
    ..registerLazySingleton(() => FetchNewSectionalTest(testsDataRepo: sl()))
    ..registerLazySingleton(() => FetchTodaysDailyTest(testsDataRepo: sl()))
    ..registerLazySingleton(() => IsDailyTestAttempted(testsDataRepo: sl()))
    ..registerLazySingleton(() => MarkTestAsStarted(testsDataRepo: sl()))
    ..registerLazySingleton(() => AnswerAQuestion(testsDataRepo: sl()))
    ..registerLazySingleton(() => SubmitTest(testsDataRepo: sl()))
    ..registerLazySingleton(() => FetchTestAttemptDetails(testsDataRepo: sl()))
    ..registerLazySingleton(
      () => FetchSectionalTestAttemptStatus(testsDataRepo: sl()),
    )
    ..registerLazySingleton(
      () => FetchSectionalTestAttemptHistory(testsDataRepo: sl()),
    )
    ..registerLazySingleton(
      () => FetchGroupedSectionalTestAttemptHistory(testsDataRepo: sl()),
    )
    ..registerLazySingleton(() => FetchTestAttemptAnalysis(testsDataRepo: sl()))
    ..registerLazySingleton(() => FetchTestResultSummary(testsDataRepo: sl()))
    ..registerLazySingleton(
      () => FetchTestQuestionsWithAnswerStatus(testsDataRepo: sl()),
    )
    // Register TestsBloc as a singleton to ensure the same instance is used throughout the app
    ..registerSingleton(
      TestsBloc(
        fetchTestData: sl(),
        fetchNewSectionalTest: sl(),
        markTestAsStarted: sl(),
        answerAQuestion: sl(),
        submitSectionalTest: sl(),
        fetchSectionalTestAttemptDetails: sl(),
        fetchSectionalTestAttemptStatus: sl(),
        fetchSectionalTestAttemptHistory: sl(),
        fetchGroupedSectionalTestAttemptHistory: sl(),
        fetchTestAttemptAnalysis: sl(),
        fetchTestResultSummary: sl(),
        fetchTestQuestionsWithAnswerStatus: sl(),
        fetchTodaysDailyTest: sl(),
        isDailyTestAttempted: sl(),
      ),
    );
}
