import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/web/web_main_content_area.dart';
import 'package:skillapp/core/common/widgets/web/web_main_profile_page.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/src/admin/presentation/bloc/excel_file_upload_bloc.dart';
import 'package:skillapp/src/admin/presentation/views/upload_screen.dart';
import 'package:skillapp/src/attempt_history/presentation/blocs/attempt_history_bloc.dart';
import 'package:skillapp/src/attempt_history/presentation/views/history_question.dart';
import 'package:skillapp/src/attempt_history/presentation/views/history_questions_list.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';
import 'package:skillapp/src/auth/presentation/cubits/login_cubit.dart';
import 'package:skillapp/src/auth/presentation/cubits/signup_cubit.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_flagged_questions.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_flagged_subjects.dart';
import 'package:skillapp/src/flag_question/presentation/blocs/flagged_questions_bloc.dart';
import 'package:skillapp/src/flag_question/presentation/views/flagged_question_list.dart';
import 'package:skillapp/src/flag_question/presentation/views/flagged_subjects.dart';
import 'package:skillapp/src/home/<USER>/blocs/dashboard_summary/dashboard_summary_bloc.dart';
import 'package:skillapp/src/home/<USER>/blocs/home/<USER>';
import 'package:skillapp/src/home/<USER>/views/home_screen.dart';
import 'package:skillapp/src/auth/presentation/views/landing_screen.dart';
import 'package:skillapp/src/notification/notification_screen.dart';
import 'package:skillapp/src/auth/presentation/views/signin_screen.dart';
import 'package:skillapp/src/auth/presentation/views/signup_screen.dart';
import 'package:skillapp/src/auth/presentation/views/google_sign_up_profile_screen.dart';
import 'package:skillapp/src/auth/presentation/views/successpop_screen.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:skillapp/src/practice/presentation/views/practice_screen.dart';
import 'package:skillapp/src/edit_profile/presentation/views/edit_profile.dart';
import 'package:skillapp/src/edit_profile/presentation/blocs/edit_profile_bloc.dart';
import 'package:skillapp/src/change_password/presentation/views/change_password.dart';
import 'package:skillapp/src/streaks/presentation/views/streak_calendar_page.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/views/test_instructions_screen.dart';
import 'package:skillapp/src/test/presentation/views/test_confirmation_screen.dart';
import 'package:skillapp/src/test/presentation/views/daily_test_confirmation_screen.dart';
import 'package:skillapp/src/test/presentation/views/daily_test_success_screen.dart';
import 'package:skillapp/src/test/presentation/views/test_screen.dart'
    as test_views;
import 'package:skillapp/src/test/presentation/views/test_result_screen.dart';
import 'package:skillapp/src/test/presentation/views/question_detail_screen.dart';
import 'package:skillapp/src/ui_for_bloc_testing.dart';
import 'package:skillapp/src/user_level_map/presentation/views/user_level_map.dart';
import 'package:skillapp/src/subscription/presentation/views/subscription_plan_page.dart';
import 'package:skillapp/src/quiz_demo/presentation/views/quiz_demo_screen.dart';
import 'package:skillapp/src/past_tests/presentation/blocs/past_tests_bloc.dart';
import 'package:skillapp/src/past_tests/presentation/views/past_tests_screen.dart';
import 'package:skillapp/src/past_tests/presentation/views/test_summary_page.dart';

import '../common/views/splash_screen.dart';

part 'router.main.dart';
