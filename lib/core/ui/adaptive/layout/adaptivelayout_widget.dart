
import 'package:flutter/material.dart';
import 'package:skillapp/core/ui/adaptive/layout/responsive_widget.dart';

class AdaptiveLayout extends StatelessWidget{

  final Widget mobileBody;
  final Widget tabletBody;
  final Widget desktopBody;

  const AdaptiveLayout({super.key, required this.mobileBody, required this.tabletBody, required this.desktopBody});
  
  @override
  Widget build(Object context) {
    return ResponsiveWidget(mobile: mobileBody, tablet: tabletBody, desktop: desktopBody);
  }

  

}