/// Flutter icons AppIcons
/// Copyright (C) 2022 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  AppIcons
///      fonts:
///       - asset: fonts/AppIcons.ttf
///
///
///
library;

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class AppIcons {
  AppIcons._();

  static const _kFontFam = 'AppIcons';
  static const String? _kFontPkg = null;

  static const IconData contact =
      IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData email =
      IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData github =
      IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData logout =
      IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData menu =
      IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData menuleft =
      IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData peace =
      IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData rate =
      IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData trophyoutline =
      IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData web =
      IconData(0xe809, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData code =
      IconData(0xe80a, fontFamily: _kFontFam, fontPackage: _kFontPkg);

  // enum FlaggedStatus { easy, moderate, hard, veryhard, notFlagged }
  static const String veryhard = "assets/images/veryhard.svg";
  static const String hard = "assets/images/hard.svg";
  static const String moderate = "assets/images/moderate.svg";
  static const String easy = "assets/images/other.svg";
  static const String notFlagged = "assets/images/practice/flag.svg";

  final ImageProvider veryHardImage =
      const ExactAssetImage("assets/images/veryhard.svg");
}
