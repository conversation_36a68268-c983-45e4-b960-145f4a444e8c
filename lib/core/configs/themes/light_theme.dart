import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/themes/sub_theme_data_mixin.dart';

@immutable
class LightTheme with SubThemeData {
  const LightTheme({
    this.primaryColorLT = const Color(0xFF50409A),
    this.scaffoldBackgroundColorLT = const Color(0xFFFEFEFE),
  });

  final Color primaryColorLT, scaffoldBackgroundColorLT;

  ThemeData toThemeData() {
    // return ThemeData(useMaterial3: true);

    final ThemeData systemLightTheme = ThemeData.light();
    return systemLightTheme.copyWith(
      primaryColor: primaryColorLT,
      scaffoldBackgroundColor: scaffoldBackgroundColorLT,
      elevatedButtonTheme:
          ElevatedButtonThemeData(style: getElavatedButtonTheme()),
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }
}
