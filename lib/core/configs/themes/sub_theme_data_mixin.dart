import 'package:flutter/material.dart';
//import 'package:google_fonts/google_fonts.dart';


mixin SubThemeData{

 ButtonStyle getElavatedButtonTheme(){
  return ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF964EC2),
            foregroundColor: Colors.white,
            textStyle: const TextStyle(fontSize: 16),
            padding: const EdgeInsets.symmetric(vertical: 16),
        );
  }
}
