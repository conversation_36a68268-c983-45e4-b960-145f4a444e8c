import 'package:flutter/material.dart';
import 'package:skillapp/core/enums/enum_master.dart';

const kSecondaryColor = Color(0xFF964EC2);
const kPrimaryColor = Color(0xFF50409A);
const kMainGradientColor = Color(0xFF070123);
const kOnSurfaceTextColor = Color(0XFFFFFFFF);
const kPrimaryTextColor = Color(0xFF121212);

const kReadingBoxDecColor = Color(0xffffc8b0);
const kMathsBoxDecColor = Color(0xff9dd3e6);
const kThinkingDecColor = Color(0xff9bd9ac);
const kWritingDecColor = Color(0xffa8aae5);

const kVeryhardFlagColor = Color(0xff4E4E4E);
const kHardFlagColor = Color(0xffFF0000);
const kModeratFlagColor = Color(0xffFE7A01);
const kEasyFlagColor = Color(0xff0067C7);
const kDefaultFlagColor = kPrimaryColor;

const kVeryHardBgColor = Color(0xffDEDEDE);
const kHardBgColor = Color(0xffFFDFDF);
const kModerateBgColor = Color(0xffFFF5E5);
const kEasyBgColor = Color(0xffD7ECFF);
const kAllBgColor = Colors.white;

const kAppBarMenuItemTextColor = Color(0xff121212);
const kProfileBadgeTextColor = Color(0xff575757);
const kProfileHeaderBarColor = Color(0xffF7F7FA);
const kProfileMenuSeparationLine = Color(0xffE0E0E0);

const kProfileSavedQuestionBgColor = Color(0XffF2F2F2);
const practiceScaffoldBgColor = Color(0XFFF5F5F5);
const kCalendarRangeHighlightedColor = Color(0x4CC94FE0);

const kAppBarProgressCompletionBorderColor = Color(0xFFE7E7E7);
const kAppBarProgressColor = Color(0xFF6BC100);

//Web

const kNavigationMenuTextColor = Color(0xFF666666);
const kWebMainContentBgColor = Color(0xFFFBF4FF);

const kWebMainContentBgGreyColor = Color(0xFFF2F2F2);

const kWebTestAnsweredBgColor = Color(0xFFD8EFD2); //Color(0xFF297A14);
const kWebTestUnAnsweredBgColor = Color(0xFFE5E5E5);
const kWebTestCurrentBgColor = kPrimaryColor;
const kWebTestFlaggedBgColor = Color(0xFFFFE5B2); // Color(0xFF805500);

const onBoarding1TopRightGradient = LinearGradient(
  begin: Alignment(0, -1),
  end: Alignment(0, 1.204),
  colors: <Color>[Color(0xff9a4091), Color(0xff070123)],
  stops: <double>[0, 1],
);

const onBoardingcarouselTextBoxGradient = LinearGradient(
  begin: Alignment(-1.977, -1.506),
  end: Alignment(0.904, 1.162),
  colors: <Color>[Color(0x8cffffff), Color(0x8c000000)],
  stops: <double>[0.261, 1],
);

/*const onBoarding1TopRightGradient = LinearGradient(
            begin: Alignment(0.0, -1.0),
            end: Alignment(0.0, 0.0),
            colors: [
              Color.fromRGBO(216, 179, 239, 0.3),
              Color.fromRGBO(61, 1, 98, 0),
            ],
            stops: [0.116, 0.7424],
            transform: GradientRotation(-2.5558337),
);*/

const onBoarding1MainGradient = LinearGradient(
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
  colors: [
    kSecondaryColor,
    kMainGradientColor,
  ],
  stops: [0.0, 1.1022],
  transform: GradientRotation(3.14159265),
);

const onBoarding1BottomRightGradient = LinearGradient(
  begin: Alignment(0.6633962218446463, -0.2707145188652646),
  end: Alignment(-0.2159743522008016, 0.999847502011092),
  colors: [
    kSecondaryColor,
    Color.fromRGBO(13, 8, 37, 0),
  ],
  stops: [0.1707, 0.5926],
  transform: GradientRotation(0.729273842191457),
);

Color getAnswerOptionBorderColor(AnswerOptionStatuses answerOptionStatus) {
  switch (answerOptionStatus) {
    case AnswerOptionStatuses.selected:
      return const Color(0xff964EC2); // Color for selected status
    case AnswerOptionStatuses.rightAnswer:
      return const Color(0xff208C2A); // Color for right answer status
    case AnswerOptionStatuses.wrongAnswer:
      return const Color(0xffCD1414); // Color for wrong answer status
    default:
      return const Color(0x1e000000); // Default color
  }
}

Color getAnswerOptionBgColor(
    AnswerOptionStatuses answerOptionStatus, AttemptStatus lastAttemptStatus) {
  switch (answerOptionStatus) {
    case AnswerOptionStatuses.selected:
      return const Color(0xffffffff); // Color for selected status
    case AnswerOptionStatuses.rightAnswer:
      return const Color(0xffDEFFE1); // Color for right answer status
    case AnswerOptionStatuses.wrongAnswer:
      return const Color(0xffFFE4E4); // Color for wrong answer status
    default:
      return const Color(0xffffffff); // Default color

    // condition Written by Ranjith to show the background of answer options in different color, Keeping it as it can be used later
    /*   if(lastAttemptStatus==AttemptStatus.success || lastAttemptStatus==AttemptStatus.failure 
      || lastAttemptStatus==AttemptStatus.lastAttemptDone){
        return  const Color(0xffC5C6D0);
      }else{
        return const Color(0xffffffff); // Default color
      }
    */
  }
}

Color getAnswerTextColor(
    AnswerOptionStatuses answerOptionStatus, AttemptStatus lastAttemptStatus) {
  Color normalOptionColor = const Color(0xff121212);
  Color disabledOptionColor = const Color(0xff959595);

  switch (answerOptionStatus) {
    case AnswerOptionStatuses.selected:
      return normalOptionColor; // Color for selected status
    case AnswerOptionStatuses.rightAnswer:
      return normalOptionColor; // Color for right answer status
    case AnswerOptionStatuses.wrongAnswer:
      return normalOptionColor; // Color for wrong answer status
    default:
      if (lastAttemptStatus == AttemptStatus.success ||
          lastAttemptStatus == AttemptStatus.failure ||
          lastAttemptStatus == AttemptStatus.lastAttemptDone) {
        return disabledOptionColor;
      } else {
        return normalOptionColor;
      }
  }
}

Color getAnswerDisplayIdTextColor(
    AnswerOptionStatuses answerOptionStatus, AttemptStatus lastAttemptStatus) {
  Color normalOptionColor = const Color(0xff575757);
  Color disabledOptionColor = const Color(0xff959595);

  switch (answerOptionStatus) {
    case AnswerOptionStatuses.selected:
      return normalOptionColor; // Color for selected status
    case AnswerOptionStatuses.rightAnswer:
      return normalOptionColor; // Color for right answer status
    case AnswerOptionStatuses.wrongAnswer:
      return normalOptionColor; // Color for wrong answer status
    default:
      if (lastAttemptStatus == AttemptStatus.success ||
          lastAttemptStatus == AttemptStatus.failure ||
          lastAttemptStatus == AttemptStatus.lastAttemptDone) {
        return disabledOptionColor;
      } else {
        return normalOptionColor;
      }
  }
}

Color getAnswerOptionIdBorderColor(AnswerOptionStatuses answerOptionStatus) {
  switch (answerOptionStatus) {
    case AnswerOptionStatuses.selected:
      return const Color(0xffF7E7FF); // Color for selected status
    case AnswerOptionStatuses.rightAnswer:
      return const Color(0xffDEFFE1); // Color for right answer status
    case AnswerOptionStatuses.wrongAnswer:
      return const Color(0xffCD1414); // Color for wrong answer status
    default:
      return const Color(0xffefefef); // Default color
  }
}

Color getBoxDecColor(String text) {
  switch (text) {
    case 'Reading':
      return kReadingBoxDecColor;
    case 'Maths':
      return kMathsBoxDecColor;
    case 'Thinking skills':
      return kThinkingDecColor;
    case 'Writing':
      return kWritingDecColor;
    default:
      return kReadingBoxDecColor; // Default color if text doesn't match any case
  }
}

Color getSubmitButtonColor(
    bool optionSelected, AttemptStatus lastAttemptStatus) {
  if (optionSelected &&
      (lastAttemptStatus == AttemptStatus.notAttempted ||
          lastAttemptStatus == AttemptStatus.failure ||
          lastAttemptStatus == AttemptStatus.retryingAfterFailure)) {
    return const Color(0xff50409a);
  } else {
    return const Color(0xff9C94BE);
  }
}

Color getFlagColor(String flagType) {
  switch (flagType) {
    case 'easy':
      return kEasyFlagColor;
    case 'moderate':
      return kModeratFlagColor;
    case 'hard':
      return kHardFlagColor;
    case 'veryhard':
      return kVeryhardFlagColor;
    case 'all':
      return Colors.white;
    default:
      return kDefaultFlagColor; // Default color if text doesn't match any case
  }
}

Color getFlagBgColor(String flagType) {
  switch (flagType) {
    case 'easy':
      return kEasyBgColor;
    case 'moderate':
      return kModerateBgColor;
    case 'hard':
      return kHardBgColor;
    case 'veryhard':
      return kVeryHardBgColor;
    case 'all':
      return kAllBgColor;
    default:
      return kAllBgColor; // Default color if text doesn't match any case
  }
}

// main gradient pattern for light theme
/*const mainGradientLT = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      kPrimayLightColorLT,
      kPrimayColorLT,
    ]);

// main gradient pattern for Dark theme
const mainGradientDT = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      kPrimayLightColorDT,
      kPrimayColorDT,
    ]);

LinearGradient mainGradient() =>
    UIParameters.isDarkMode() ? mainGradientDT : mainGradientLT;



Color customScaffoldColor(BuildContext context) =>
    UIParameters.isDarkMode()
        ? const Color(0xFF2e3c62)
        : const Color.fromARGB(255, 240, 237, 255); 

Color answerBorderColor(BuildContext context) =>
    UIParameters.isDarkMode()
        ? const Color.fromARGB(255, 20, 46, 158)
        : const Color.fromARGB(255, 221, 221, 221);

Color answerSelectedColor(BuildContext context) =>  Get.isDarkMode?Theme.of(context).cardColor.withOpacity(0.5):Theme.of(context).primaryColor;
*/
const signUpTopRightGradient = LinearGradient(
  begin: Alignment(-0, -1),
  end: Alignment(0.003, 1.101),
  colors: <Color>[Color(0xff9a4091), Color(0xff070123)],
  stops: <double>[0, 1],
);
