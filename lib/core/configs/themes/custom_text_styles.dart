import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:skillapp/core/configs/configs.dart';

const kFontFamily = 'Poppins';
const kLogoTextFamily = 'Arial Rounded MT Bold';

const kCarouselFirstLineTs = TextStyle(
  fontSize: 15,
  fontWeight: FontWeight.normal,
  color: Colors.white54,
  height: 1.2,
  letterSpacing: 0.5,
  fontStyle: FontStyle.normal,
  decoration: TextDecoration.none,
  fontFamily: kFontFamily,
);

const kDashboardAppbarTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w600,
  color: kPrimaryTextColor,
  fontSize: 16,
);

const kPracticeAppbarTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w500,
  color: kPrimaryTextColor,
  fontSize: 16,
  height: 1.5, // This represents the line height in Flutter
);

const kQuestionsTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w600,
  color: kPrimaryTextColor,
  fontSize: 14,
  height: 1.4, // This represents the line height in Flutter
);

const kWebQuestionsTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  // fontWeight: FontWeight.w600,
  fontWeight: FontWeight.w500,
  color: kPrimaryTextColor,
  fontSize: 16,
  height: 1.4, // This represents the line height in Flutter
);

const kPraticeHintTextTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w500,
  color: kPrimaryColor,
  fontSize: 14,
  height: 1.5, // This represents the line height in Flutter
);

const kPracticeAppBarMenuItemTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w400,
  color: kAppBarMenuItemTextColor,
  fontSize: 14,
  height: 1, // This represents the line height in Flutter
);

const kProfileNameTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w600,
  color: kAppBarMenuItemTextColor,
  fontSize: 20,
  height: 1,
);

const kProfileLinkTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w500,
  color: kPrimaryColor,
  fontSize: 14,
  height: 1,
);

const kProfileBadgeTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w600,
  color: kProfileBadgeTextColor,
  fontSize: 14,
  height: 1,
);

const kProfileMenuItemtextTs = TextStyle(
  fontFamily: kFontFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w500,
  color: kPrimaryTextColor,
  fontSize: 14,
  height: 1,
);

TextStyle kSavedQuestionFlagTs(Color textColor) {
  return TextStyle(
    fontFamily: kFontFamily,
    fontStyle: FontStyle.normal,
    fontWeight: FontWeight.w400,
    color: textColor,
    fontSize: 10,
    height: 1,
  );
}

TextStyle SafeGoogleFont(
  String fontFamily, {
  TextStyle? textStyle,
  Color? color,
  Color? backgroundColor,
  double? fontSize,
  FontWeight? fontWeight,
  FontStyle? fontStyle,
  double? letterSpacing,
  double? wordSpacing,
  TextBaseline? textBaseline,
  double? height,
  Locale? locale,
  Paint? foreground,
  Paint? background,
  List<Shadow>? shadows,
  List<FontFeature>? fontFeatures,
  TextDecoration? decoration,
  Color? decorationColor,
  TextDecorationStyle? decorationStyle,
  double? decorationThickness,
}) {
  try {
    return GoogleFonts.getFont(
      fontFamily,
      textStyle: textStyle,
      color: color,
      backgroundColor: backgroundColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      letterSpacing: letterSpacing,
      wordSpacing: wordSpacing,
      textBaseline: textBaseline,
      height: height,
      locale: locale,
      foreground: foreground,
      background: background,
      shadows: shadows,
      fontFeatures: fontFeatures,
      decoration: decoration,
      decorationColor: decorationColor,
      decorationStyle: decorationStyle,
      decorationThickness: decorationThickness,
    );
  } catch (ex) {
    return GoogleFonts.getFont(
      "Source Sans Pro",
      textStyle: textStyle,
      color: color,
      backgroundColor: backgroundColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      letterSpacing: letterSpacing,
      wordSpacing: wordSpacing,
      textBaseline: textBaseline,
      height: height,
      locale: locale,
      foreground: foreground,
      background: background,
      shadows: shadows,
      fontFeatures: fontFeatures,
      decoration: decoration,
      decorationColor: decorationColor,
      decorationStyle: decorationStyle,
      decorationThickness: decorationThickness,
    );
  }
}

// Styling for Web

const kNavigationbarLogoTs = TextStyle(
  fontFamily: kLogoTextFamily,
  fontStyle: FontStyle.normal,
  fontWeight: FontWeight.w400,
  color: kPrimaryColor,
  fontSize: 24,
);

const knavigationMenuTextNotSelectedTs = TextStyle(
  fontFamily: kFontFamily,
  color: kNavigationMenuTextColor,
  fontSize: 16,
  fontWeight: FontWeight.w700,
  height: 0,
);

const knavigationMenuTextSelectedTs = TextStyle(
  fontFamily: kFontFamily,
  color: kPrimaryColor,
  fontSize: 16,
  fontWeight: FontWeight.w700,
  height: 0,
);

const kHeaderTextTs = TextStyle(
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w500,
  color: kPrimaryTextColor,
  fontSize: 20,
  height: 0,
);

const kHeaderButtonTs = TextStyle(
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w500,
  color: kPrimaryColor,
  fontSize: 14,
  height: 0,
);

const kLearningInfoMetricLabelTs = TextStyle(
  color: kOnSurfaceTextColor,
  fontSize: 14,
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w400,
  height: 0,
);

const kLearningInfoMetricValueTs = TextStyle(
  color: kOnSurfaceTextColor,
  fontSize: 20,
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w600,
  height: 0,
);

const kWebSubjectGridViewHeadingTs = TextStyle(
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w500,
  color: kPrimaryTextColor,
  fontSize: 20,
  height: 0,
);

const kWebSavedQuestionTitleTs = TextStyle(
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w400,
  color: kPrimaryTextColor,
  fontSize: 16,
  height: 0,
);

TextStyle kWebSavedQuestionFlagTs(Color textColor) {
  return TextStyle(
    fontFamily: kFontFamily,
    fontWeight: FontWeight.w400,
    color: textColor,
    fontSize: 16,
  );
}

const kWebSavedQuestionHeaderTs = TextStyle(
  color: kPrimaryTextColor,
  fontSize: 20,
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w500,
  height: 0,
);

const kWebAttemptedQuestionSubHeaderTs = TextStyle(
  color: kPrimaryTextColor,
  fontSize: 16,
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w600,
  height: 0,
);

const kWebAttemptedQuestionDescriptionTs = TextStyle(
  color: kPrimaryTextColor,
  fontSize: 16,
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w400,
  height: 0,
);

const kTableHeaderTs = TextStyle(
  color: kPrimaryTextColor,
  fontSize: 14,
  fontFamily: kFontFamily,
  fontWeight: FontWeight.w600,
  height: 0,
);
