import 'package:equatable/equatable.dart';
import 'package:skillapp/core/errors/exceptions.dart';

abstract class Failure extends Equatable {
  const Failure({required this.message, required this.statusCode});

  final String message;
  final dynamic statusCode;

  String get errorMessage => '$statusCode Error: $message';

  @override
  List<dynamic> get props => [message, statusCode];
}

class CacheFailure extends Failure {
  const CacheFailure({required super.message, required super.statusCode});
}

class ServerFailure extends Failure {
  const ServerFailure({required super.message, required super.statusCode});

  const ServerFailure.simple()
      : this(message: 'Server error occurred', statusCode: 500);

  ServerFailure.fromException(ServerException exception)
      : this(message: exception.message, statusCode: exception.statusCode);
}

class FilePickingFailure extends Failure {
  const FilePickingFailure({required super.message, super.statusCode = 400});
}

class ExcelParsingFailure extends Failure {
  const ExcelParsingFailure({required super.message, super.statusCode = 422});
}

class NoDataFoundFailure extends Failure {
  const NoDataFoundFailure({required super.message, super.statusCode = 204});
}

class FirestoreUploadFailure extends Failure {
  const FirestoreUploadFailure({required super.message, super.statusCode = 500});
}

class LocalFileFailure extends Failure {
  const LocalFileFailure({required super.message, super.statusCode = 500});
}

class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.statusCode = 502});
}