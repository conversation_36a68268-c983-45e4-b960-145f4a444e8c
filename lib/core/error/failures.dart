import 'package:equatable/equatable.dart';

/*
abstract class Failure extends Equatable {
  @override
  List<Object> get props => [];
}

class ServerFailure extends Failure {}

class NetworkFailure extends Failure {}

class NotFoundFailure extends Failure {}

class PaymentFailure extends Failure {
  final String message;

  PaymentFailure({required this.message});

  @override
  List<Object> get props => [message];
}
*/

abstract class Failure extends Equatable {
  final String message;

  const Failure({this.message = 'An unexpected error occurred'});

  @override
  List<Object> get props => [message];

}

// Added default messages for consistency

class ServerFailure extends Failure {
  const ServerFailure({super.message = 'Server Error'});
}

class NetworkFailure extends Failure {
  const NetworkFailure({super.message = 'Network connectivity issue'});
}

class NotFoundFailure extends Failure {
  const NotFoundFailure({super.message = 'Resource not found'});
}

class PaymentFailure extends Failure {
  const PaymentFailure({required super.message});

  @override
  List<Object> get props => [message];
}

// Failure when there's an issue with file picking (e.g., user cancels, file invalid).
class FilePickingFailure extends Failure {
  const FilePickingFailure({super.message = 'File picking cancelled or failed'});
}

// Failure when the Excel file cannot be read or parsed correctly.
class ExcelParsingFailure extends Failure {
  const ExcelParsingFailure({super.message = 'Failed to parse Excel file'});
}

// Failure when no valid data rows are found after parsing the Excel file.
class NoDataFoundFailure extends Failure {
  const NoDataFoundFailure({super.message = 'No data found in the file'});
}

// Failure specifically related to uploading data to Firestore.
class FirestoreUploadFailure extends Failure {
  const FirestoreUploadFailure({super.message = 'Failed to upload data to Firestore'});
}

// A general failure for other local file system operations (if needed beyond just picking).
class LocalFileFailure extends Failure {
  const LocalFileFailure({super.message = 'Local file operation failed'});
}
