import 'package:flutter/widgets.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/features/user/domain/usecases/get_current_profile.dart';

class UserProvider extends ChangeNotifier {
  LocalUser? _user;

  final GetCurrentProfile _getCurrentProfile;

  UserProvider({required GetCurrentProfile getCurrentProfile})
      : _getCurrentProfile = getCurrentProfile;

  LocalUser? get user => _user;

  void initUser(LocalUser? user) async {
    //TODO- Check if profile details is present in shared preferences.
    //If not fetch from firestore and save in shared preferences.
    if (user == null || user.isEmpty || user.currentProfile.isEmpty) {
      await _getCurrentProfile();
    }
    //GetCurrentProfile
    if (_user != user) {
      _user = user;
    }
  }

  Future<void> populateUserProfile() async {
    print("JB:Inside populateUserProfile-start");
    await _getCurrentProfile();
    print("JB:Inside populateUserProfile-after getcurrentprofile");
    //GetCurrentProfile
    if (_user != user) {
      _user = user;
    }
  }

  void setUser(LocalUser? user) {
    if (user != _user) {
      _user = user;
      Future.delayed(Duration.zero, () => notifyListeners());
    }
  }
}
