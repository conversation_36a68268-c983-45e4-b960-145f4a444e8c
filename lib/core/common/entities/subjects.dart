import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';

class Subject extends Equatable {
  const Subject({
    required this.id,
    required this.description,
    required this.image,
  });

  factory Subject.empty() {
    return const Subject(
      id: '',
      description: '',
      image: '',
    );
  }

  final String id;
  final String description;
  final String image;

  DataMap toMap() {
    return {
      'id': id,
      'description': description,
      'image': image,
    };
  }

  factory Subject.fromMap(DataMap map) {
    return Subject(
      id: map['id'] as String,
      description: map['description'] as String,
      image: map['image'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory Subject.fromJson(String source) =>
      Subject.fromMap(json.decode(source) as DataMap);

  @override
  List<Object?> get props => [id, description];
}
