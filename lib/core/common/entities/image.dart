import 'dart:convert';

class ImageDetails {
  final String path;
  final String toolTip;

  const ImageDetails({required this.path, required this.toolTip});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{'path': path, 'toolTip': toolTip};
  }

  factory ImageDetails.fromMap(Map<String, dynamic> map) {
    return ImageDetails(
        path: map['path'] as String, toolTip: map['tooltip'] as String);
  }

  String toJson() => json.encode(toMap());

  factory ImageDetails.fromJson(String source) =>
      ImageDetails.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "ImageDetails[ path=$path, toolTip=$toolTip]";
  }
}
