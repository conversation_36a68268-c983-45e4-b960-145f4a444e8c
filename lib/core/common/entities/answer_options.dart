import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/enums/enum_master.dart';

class AnswerOptions extends Equatable {
  final List<OptionEntry> entries;
  final bool alignOptionsInRow;

  //const AnswerOptionsData(this.alignOptionsInRow, {required this.entries, this.alignOptionsInRow=false});

  const AnswerOptions({required this.entries, this.alignOptionsInRow = false});

  AnswerOptions copyWith(
      {List<OptionEntry>? entries, bool? alignOptionsInRow}) {
    return AnswerOptions(
        entries: entries ?? this.entries,
        alignOptionsInRow: alignOptionsInRow ?? this.alignOptionsInRow);
  }

  factory AnswerOptions.fromMap(Map<String, dynamic> map) {
    return AnswerOptions(
      alignOptionsInRow: map['alignOptionsInRow'] != null
          ? map['alignOptionsInRow'] == 'true'
          : false,
      entries: List<OptionEntry>.from(
        (map['entries'] as List<dynamic>).map<OptionEntry>(
          (x) => OptionEntry.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'entries': entries,
      'alignOptionsInRow': alignOptionsInRow.toString(),
    };
  }

  String toJson() => json.encode(toMap());

  factory AnswerOptions.fromJson(String source) =>
      AnswerOptions.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  List<Object?> get props => [entries];

  @override
  String toString() {
    return "AnswerOptionsData[entries = ${entries.toString()} and alignOptionsInRow = $alignOptionsInRow ]";
  }
}

class OptionEntry extends Equatable {
  final String id;
  final String displayId;
  final DescriptionAndImageDataWrapper descAndImgDataWrapper;
  final AnswerOptionStatuses status;

  const OptionEntry(
      {required this.id,
      required this.descAndImgDataWrapper,
      required this.displayId,
      this.status = AnswerOptionStatuses.notSelected});

  OptionEntry copyWith(
      {String? id,
      String? displayId,
      DescriptionAndImageDataWrapper? descAndImgDataWrapper,
      AnswerOptionStatuses? status}) {
    return OptionEntry(
        id: id ?? this.id,
        descAndImgDataWrapper:
            descAndImgDataWrapper ?? this.descAndImgDataWrapper,
        status: status ?? this.status,
        displayId: displayId ?? this.displayId);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'displayId': displayId,
      'descAndImgDataWrapper': descAndImgDataWrapper,
    };
  }

  factory OptionEntry.fromMap(Map<String, dynamic> map) {
    return OptionEntry(
      id: map['id'] as String,
      displayId: map['displayId'] as String,
      descAndImgDataWrapper:
          map['descAndImgDataWrapper'] as DescriptionAndImageDataWrapper,
    );
  }

  String toJson() => json.encode(toMap());

  factory OptionEntry.fromJson(String source) =>
      OptionEntry.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "OptionEntry[id=$id, displayId=$displayId, description=$descAndImgDataWrapper]";
  }

  @override
  List<Object?> get props => [id, status];
}

class AnswerOptionsRaw {
  //This class is for conversion of data to latest models.

  final String id;
  final List<DescriptionAndImageEntry> description;

  AnswerOptionsRaw({required this.id, required this.description});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description.map((x) => x.toMap()).toList(),
    };
  }

  factory AnswerOptionsRaw.fromMap(Map<String, dynamic> map) {
    return AnswerOptionsRaw(
      id: map['id'] as String,
      description: List<DescriptionAndImageEntry>.from(
        (map['description'] as List<dynamic>).map<DescriptionAndImageEntry>(
          (x) => DescriptionAndImageEntry.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory AnswerOptionsRaw.fromJson(String source) =>
      AnswerOptionsRaw.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "AnswerOptionsRaw: id= $id & description= $description";
  }
}

class OptionsRaw {
  AnswerOptionsRaw options;

  OptionsRaw({required this.options});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{'options': options};
  }

  String toJson() => json.encode(toMap());

  factory OptionsRaw.fromMap(Map<String, dynamic> map) {
    return OptionsRaw(options: map['options'] as AnswerOptionsRaw);
  }

  factory OptionsRaw.fromJson(String source) {
    return OptionsRaw.fromMap(json.decode(source) as Map<String, dynamic>);
  }
}
