import 'dart:convert';

import 'package:equatable/equatable.dart';

class Answer extends Equatable {
  final String id;
  final String description;
  const Answer({
    required this.id,
    required this.description,
  });

  Answer copyWith({
    String? id,
    String? description,
  }) {
    return Answer(
      id: id ?? this.id,
      description: description ?? this.description,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description,
    };
  }

  factory Answer.fromMap(Map<String, dynamic> map) {
    return Answer(
      id: map['id'] as String,
      description: map['description'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory Answer.fromJson(String source) =>
      Answer.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => 'Answer(id: $id, description: $description)';

/*
  @override
  bool operator ==(covariant Answer other) {
    if (identical(this, other)) return true;

    return other.id == id && other.description == description;
  }

  @override
  int get hashCode => id.hashCode ^ description.hashCode;
*/

  @override
  List<Object?> get props => [id, description];
}
