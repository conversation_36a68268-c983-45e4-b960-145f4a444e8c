import 'dart:convert';

import 'package:skillapp/core/common/entities/image.dart';

class DescriptionAndImageDataWrapper {
  final List<DescriptionAndImage> descAndImgDataList;
  const DescriptionAndImageDataWrapper({required this.descAndImgDataList});

  static const DescriptionAndImageDataWrapper empty =
      DescriptionAndImageDataWrapper(descAndImgDataList: []);

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'descAndImgDataList': descAndImgDataList.map((x) => x.toMap()).toList(),
    };
  }

  factory DescriptionAndImageDataWrapper.fromMap(Map<String, dynamic> map) {
    return DescriptionAndImageDataWrapper(
      descAndImgDataList: List<DescriptionAndImage>.from(
        (map['descAndImgDataList'] as List<dynamic>).map<DescriptionAndImage>(
          (x) => DescriptionAndImage.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory DescriptionAndImageDataWrapper.fromJson(String source) =>
      DescriptionAndImageDataWrapper.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "DescriptionAndImageDataWrapper[descAndImgDataList= $descAndImgDataList]";
  }
}

class DescriptionAndImage {
  final List<DescriptionAndImageEntry> descAndImgDataEntryList;
  const DescriptionAndImage({required this.descAndImgDataEntryList});

  static const DescriptionAndImage empty =
      DescriptionAndImage(descAndImgDataEntryList: []);

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'descAndImgDataEntryList':
          descAndImgDataEntryList.map((x) => x.toMap()).toList(),
    };
  }

  factory DescriptionAndImage.fromMap(Map<String, dynamic> map) {
    return DescriptionAndImage(
      descAndImgDataEntryList: List<DescriptionAndImageEntry>.from(
        (map['descAndImgDataEntryList'] as List<dynamic>)
            .map<DescriptionAndImageEntry>(
          (x) => DescriptionAndImageEntry.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory DescriptionAndImage.fromJson(String source) =>
      DescriptionAndImage.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "DescriptionAndImageData[descAndImgDataEntryList= $descAndImgDataEntryList]";
  }
}

class DescriptionAndImageEntry {
  final String type;
  final String description;
  final bool isAnimated;
  final List<ImageDetails> imageDataList;
  final int group;

  const DescriptionAndImageEntry(
      {required this.type,
      this.isAnimated = false,
      this.description = '',
      this.imageDataList = const [],
      this.group = -1});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'type': type,
      'isAnimated': isAnimated,
      'description': description,
      'imageDataList': imageDataList.map((x) => x.toMap()).toList(),
      'group': group,
    };
  }

  factory DescriptionAndImageEntry.fromMap(Map<String, dynamic> map) {
    return DescriptionAndImageEntry(
      type: map['type'] as String,
      description: map['description'] ?? '',
      isAnimated: map.containsKey('isAnimated')
          ? map['isAnimated'].toString() == 'true'
          : false,
      imageDataList: map.containsKey('images')
          ? (List<ImageDetails>.from(
              (map['images'] as List<dynamic>).map<ImageDetails>(
                (x) => ImageDetails.fromMap(x as Map<String, dynamic>),
              ),
            ))
          : [],
      group: map['group'] ?? -1,
    );
  }

  String toJson() => json.encode(toMap());

  factory DescriptionAndImageEntry.fromJson(String source) =>
      DescriptionAndImageEntry.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "DescriptionAndImageDataEntry[type=$type, description= $description, imageDataListSize=${imageDataList.length}]";
  }

  String toDebugString() {
    return "DescriptionAndImageDataEntry[type=$type, isAnimated=$isAnimated, description= $description, imageDataList=$imageDataList]";
  }
}
