import 'package:equatable/equatable.dart';

class FlaggedQuestion extends Equatable {
  final String questionId;
  final String bundleId;
  final String flagId;
  final String shortDescriptionOrId;

  const FlaggedQuestion({
    required this.questionId,
    required this.bundleId,
    required this.flagId,
    required this.shortDescriptionOrId,
  });

  @override
  String toString() {
    return "FlaggedQuestion[ id=$questionId and flagid=$flagId and bundleid=$bundleId and shortdescription=$shortDescriptionOrId]";
  }

  @override
  List<Object?> get props =>
      [questionId, bundleId, flagId, shortDescriptionOrId];

  const FlaggedQuestion.empty()
      : questionId = '',
        bundleId = '',
        flagId = '',
        shortDescriptionOrId = '';

  bool get isEmpty => this == const FlaggedQuestion.empty();
}
