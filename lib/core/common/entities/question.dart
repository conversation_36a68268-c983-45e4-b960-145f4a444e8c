import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/description_image.dart';

class Question {
  final String id;
  final String bundleId;
  final String data;

  const Question({required this.data, required this.id, this.bundleId = ''});

  @override
  String toString() {
    return "QuestionData[id=$id, data=$data]";
  }
}

class QuestionAndAnswers extends Equatable {
  final String id;
  final DescriptionAndImageDataWrapper question;
  final AnswerOptions answerOptions;
  final String correctAnswer;
  final DescriptionAndImageDataWrapper explanation;
  final DescriptionAndImageDataWrapper helpTip;
  final String module;
  final String complexity;
  final String bundleId;
  final String shortDescription;

  String printMinimumData() {
    return "Question[id=$id, question=$question, options=$answerOptions, correctAnswer=$correctAnswer, shortDescription=$shortDescription]";
  }

  @override
  String toString() {
    return "Question[id=$id, shortDescription=$shortDescription, bundleId=$bundleId, question=$question, options=$answerOptions, correctAnswer=$correctAnswer]";
  }

  factory QuestionAndAnswers.emptyQuestion() {
    return const QuestionAndAnswers(
        id: '',
        question: DescriptionAndImageDataWrapper(descAndImgDataList: []),
        answerOptions: AnswerOptions(entries: []),
        correctAnswer: '');
  }

  const QuestionAndAnswers({
    required this.id,
    required this.question,
    required this.answerOptions,
    required this.correctAnswer,
    this.explanation = DescriptionAndImageDataWrapper.empty,
    this.helpTip = DescriptionAndImageDataWrapper.empty,
    this.module = '',
    this.complexity = '',
    this.bundleId = '',
    this.shortDescription = '',
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'question': question,
      'answerOptions': answerOptions,
      'correctAnswer': correctAnswer,
      'explanation': explanation,
      'helpTip': helpTip,
      'module': module,
      'complexity': complexity,
      'bundleId': bundleId,
    };
  }

  QuestionAndAnswers copyWith(
      {String? id,
      DescriptionAndImageDataWrapper? question,
      AnswerOptions? answerOptions,
      String? correctAnswer,
      DescriptionAndImageDataWrapper? explanation,
      DescriptionAndImageDataWrapper? helpTip,
      String? module,
      String? complexity,
      String? bundleId,
      String? shortDescription}) {
    return QuestionAndAnswers(
      id: id ?? this.id,
      shortDescription: shortDescription ?? this.shortDescription,
      question: question ?? this.question,
      answerOptions: answerOptions ?? this.answerOptions,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      explanation: explanation ?? this.explanation,
      helpTip: helpTip ?? this.helpTip,
      module: module ?? this.module,
      complexity: complexity ?? this.complexity,
      bundleId: bundleId ?? this.bundleId,
    );
  }

  factory QuestionAndAnswers.fromMap(Map<String, dynamic> map) {
    return QuestionAndAnswers(
      id: map['id'] as String,
      question: DescriptionAndImageDataWrapper.fromMap(map['question']),
      answerOptions: AnswerOptions.fromMap(map['answerOptions']),
      correctAnswer: map['correctAnswer'] as String,
      explanation: map['explanation'] as DescriptionAndImageDataWrapper,
      helpTip: map['helpTip'] as DescriptionAndImageDataWrapper,
      module: map['id'] as String,
      complexity: map['id'] as String,
      bundleId: map['bundleId'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory QuestionAndAnswers.fromJson(String source) =>
      QuestionAndAnswers.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  List<Object?> get props => [id, answerOptions];

  bool get isEmpty => this == QuestionAndAnswers.emptyQuestion();
  bool get isNotEmpty => this != QuestionAndAnswers.emptyQuestion();
}
