import 'package:flutter/material.dart';

class ScreenUtil {
  static double desktopBaseWidth = 1940;
  static double desktopBaseHeight = 1080;
  static double tabletBaseWidth = 1024;
  static double tabletBaseHeight = 768;
  static double mobileBaseWidth = 375;
  static double mobileBaseHeight = 812;

  static double getFem(BuildContext context) {
    // Get the current context here
    String deviceType = getDeviceType(context);

    if (deviceType == 'Desktop') {
      return MediaQuery.of(context).size.width / desktopBaseWidth;
    } else if (deviceType == 'Tablet') {
      return MediaQuery.of(context).size.width / tabletBaseWidth;
    } else {
      return MediaQuery.of(context).size.width / mobileBaseWidth;
    }
  }

  static double getHfem(BuildContext context) {
    String deviceType = getDeviceType(context);

    if (deviceType == 'Desktop') {
      return MediaQuery.of(context).size.height / desktopBaseHeight;
    } else if (deviceType == 'Tablet') {
      return MediaQuery.of(context).size.height / tabletBaseHeight;
    } else {
      return MediaQuery.of(context).size.height / mobileBaseHeight;
    }
  }

  static String getDeviceType(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 'Mobile';
    } else if (screenWidth >= 600 && screenWidth < 1200) {
      return 'Tablet';
    } else {
      return 'Desktop';
    }
  }
}
