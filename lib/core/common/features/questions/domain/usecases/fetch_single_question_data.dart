import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/domain/repos/question_repo.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';

class FetchSingleQuestionData
    implements
        FutureUsecaseWithParams<QuestionAndAnswers,
            FetchSingleQuestionDataParams> {
  final QuestionRepo _questionRepo;

  FetchSingleQuestionData({required QuestionRepo questionRepo})
      : _questionRepo = questionRepo;

  @override
  ResultFuture<QuestionAndAnswers> call(FetchSingleQuestionDataParams params) =>
      _questionRepo.fetchSingleQuestionData(
          params.questionId, params.bundleId, params.subjectId);
}

class FetchSingleQuestionDataParams extends Equatable {
  final String questionId;
  final String bundleId;
  final String subjectId;

  const FetchSingleQuestionDataParams(
      {required this.questionId,
      required this.bundleId,
      required this.subjectId});

  @override
  List<Object?> get props => [questionId];
}
