import 'dart:convert';

import 'package:equatable/equatable.dart';

class AnswerModel extends Equatable {
  final String id;
  final String description;
  const AnswerModel({
    required this.id,
    required this.description,
  });

  AnswerModel copyWith({
    String? id,
    String? description,
  }) {
    return AnswerModel(
      id: id ?? this.id,
      description: description ?? this.description,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description,
    };
  }

  factory AnswerModel.fromMap(Map<String, dynamic> map) {
    return AnswerModel(
      id: map['id'] as String,
      description: map['description'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory AnswerModel.fromJson(String source) =>
      AnswerModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => 'Answer(id: $id, description: $description)';

/*
  @override
  bool operator ==(covariant Answer other) {
    if (identical(this, other)) return true;

    return other.id == id && other.description == description;
  }

  @override
  int get hashCode => id.hashCode ^ description.hashCode;
*/

  @override
  List<Object?> get props => [id, description];
}
