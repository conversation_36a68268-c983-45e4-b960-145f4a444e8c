import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/enums/enum_master.dart';

class QuestionModel {
  final String id;
  final String bundleId;
  final String data;

  const QuestionModel(
      {required this.data, required this.id, this.bundleId = ''});

  @override
  String toString() {
    return "QuestionData[id=$id, data=$data]";
  }
}

class QuestionAndAnswersModel extends Equatable {
  final String id;
  final DescriptionAndImageDataWrapperModel question;
  final AnswerOptionsDataModel answerOptions;
  final String correctAnswer;
  final DescriptionAndImageDataWrapperModel explanation;
  final DescriptionAndImageDataWrapperModel helpTip;
  final String module;
  final String complexity;
  final String bundleId;
  final String shortDescription;

  String printMinimumData() {
    return "Question[id=$id, question=$question, options=$answerOptions, correctAnswer=$correctAnswer, shortDescription=$shortDescription]";
  }

  @override
  String toString() {
    return "Question[id=$id, shortDescription=$shortDescription, bundleId=$bundleId, question=$question, options=$answerOptions, correctAnswer=$correctAnswer]";
  }

  factory QuestionAndAnswersModel.emptyQuestion() {
    return const QuestionAndAnswersModel(
        id: '',
        question: DescriptionAndImageDataWrapperModel(descAndImgDataList: []),
        answerOptions: AnswerOptionsDataModel(entries: []),
        correctAnswer: '');
  }

  const QuestionAndAnswersModel({
    required this.id,
    required this.question,
    required this.answerOptions,
    required this.correctAnswer,
    this.explanation = DescriptionAndImageDataWrapperModel.empty,
    this.helpTip = DescriptionAndImageDataWrapperModel.empty,
    this.module = '',
    this.complexity = '',
    this.bundleId = '',
    this.shortDescription = '',
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'question': question,
      'answerOptions': answerOptions,
      'correctAnswer': correctAnswer,
      'explanation': explanation,
      'helpTip': helpTip,
      'module': module,
      'complexity': complexity,
      'bundleId': bundleId,
    };
  }

  QuestionAndAnswersModel copyWith(
      {String? id,
      DescriptionAndImageDataWrapperModel? question,
      AnswerOptionsDataModel? answerOptions,
      String? correctAnswer,
      DescriptionAndImageDataWrapperModel? explanation,
      DescriptionAndImageDataWrapperModel? helpTip,
      String? module,
      String? complexity,
      String? bundleId,
      String? shortDescription}) {
    return QuestionAndAnswersModel(
      id: id ?? this.id,
      shortDescription: shortDescription ?? this.shortDescription,
      question: question ?? this.question,
      answerOptions: answerOptions ?? this.answerOptions,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      explanation: explanation ?? this.explanation,
      helpTip: helpTip ?? this.helpTip,
      module: module ?? this.module,
      complexity: complexity ?? this.complexity,
      bundleId: bundleId ?? this.bundleId,
    );
  }

  factory QuestionAndAnswersModel.fromMap(Map<String, dynamic> map) {
    return QuestionAndAnswersModel(
      id: map['id'] as String,
      question: DescriptionAndImageDataWrapperModel.fromMap(map['question']),
      answerOptions: AnswerOptionsDataModel.fromMap(map['answerOptions']),
      correctAnswer: map['correctAnswer'] as String,
      explanation: map['explanation'] as DescriptionAndImageDataWrapperModel,
      helpTip: map['helpTip'] as DescriptionAndImageDataWrapperModel,
      module: map['id'] as String,
      complexity: map['id'] as String,
      bundleId: map['bundleId'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory QuestionAndAnswersModel.fromJson(String source) =>
      QuestionAndAnswersModel.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  List<Object?> get props => [id, answerOptions];
}

class AnswerOptionsDataModel extends Equatable {
  final List<OptionEntryModel> entries;
  final bool alignOptionsInRow;

  //const AnswerOptionsData(this.alignOptionsInRow, {required this.entries, this.alignOptionsInRow=false});

  const AnswerOptionsDataModel(
      {required this.entries, this.alignOptionsInRow = false});

  AnswerOptionsDataModel copyWith(
      {List<OptionEntryModel>? entries, bool? alignOptionsInRow}) {
    return AnswerOptionsDataModel(
        entries: entries ?? this.entries,
        alignOptionsInRow: alignOptionsInRow ?? this.alignOptionsInRow);
  }

  factory AnswerOptionsDataModel.fromMap(Map<String, dynamic> map) {
    return AnswerOptionsDataModel(
      alignOptionsInRow: map['alignOptionsInRow'] != null
          ? map['alignOptionsInRow'] == 'true'
          : false,
      entries: List<OptionEntryModel>.from(
        (map['entries'] as List<dynamic>).map<OptionEntryModel>(
          (x) => OptionEntryModel.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'entries': entries,
      'alignOptionsInRow': alignOptionsInRow.toString(),
    };
  }

  String toJson() => json.encode(toMap());

  factory AnswerOptionsDataModel.fromJson(String source) =>
      AnswerOptionsDataModel.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  List<Object?> get props => [entries];

  @override
  String toString() {
    return "AnswerOptionsData[entries = ${entries.toString()} and alignOptionsInRow = $alignOptionsInRow ]";
  }
}

class OptionEntryModel extends Equatable {
  final String id;
  final String displayId;
  final DescriptionAndImageDataWrapperModel descAndImgDataWrapper;
  final AnswerOptionStatuses status;

  const OptionEntryModel(
      {required this.id,
      required this.descAndImgDataWrapper,
      required this.displayId,
      this.status = AnswerOptionStatuses.notSelected});

  OptionEntryModel copyWith(
      {String? id,
      String? displayId,
      DescriptionAndImageDataWrapperModel? descAndImgDataWrapper,
      AnswerOptionStatuses? status}) {
    return OptionEntryModel(
        id: id ?? this.id,
        descAndImgDataWrapper:
            descAndImgDataWrapper ?? this.descAndImgDataWrapper,
        status: status ?? this.status,
        displayId: displayId ?? this.displayId);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'displayId': displayId,
      'descAndImgDataWrapper': descAndImgDataWrapper,
    };
  }

  factory OptionEntryModel.fromMap(Map<String, dynamic> map) {
    return OptionEntryModel(
      id: map['id'] as String,
      displayId: map['displayId'] as String,
      descAndImgDataWrapper:
          map['descAndImgDataWrapper'] as DescriptionAndImageDataWrapperModel,
    );
  }

  String toJson() => json.encode(toMap());

  factory OptionEntryModel.fromJson(String source) =>
      OptionEntryModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "OptionEntry[id=$id, displayId=$displayId, description=$descAndImgDataWrapper]";
  }

  @override
  List<Object?> get props => [id, status];
}

class DescriptionAndImageDataWrapperModel {
  final List<DescriptionAndImageModel> descAndImgDataList;
  const DescriptionAndImageDataWrapperModel({required this.descAndImgDataList});

  static const DescriptionAndImageDataWrapperModel empty =
      DescriptionAndImageDataWrapperModel(descAndImgDataList: []);

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'descAndImgDataList': descAndImgDataList.map((x) => x.toMap()).toList(),
    };
  }

  factory DescriptionAndImageDataWrapperModel.fromMap(
      Map<String, dynamic> map) {
    return DescriptionAndImageDataWrapperModel(
      descAndImgDataList: List<DescriptionAndImageModel>.from(
        (map['descAndImgDataList'] as List<dynamic>)
            .map<DescriptionAndImageModel>(
          (x) => DescriptionAndImageModel.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory DescriptionAndImageDataWrapperModel.fromJson(String source) =>
      DescriptionAndImageDataWrapperModel.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "DescriptionAndImageDataWrapper[descAndImgDataList= $descAndImgDataList]";
  }
}

class DescriptionAndImageModel {
  final List<DescriptionAndImageEntryModel> descAndImgDataEntryList;
  const DescriptionAndImageModel({required this.descAndImgDataEntryList});

  static const DescriptionAndImageModel empty =
      DescriptionAndImageModel(descAndImgDataEntryList: []);

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'descAndImgDataEntryList':
          descAndImgDataEntryList.map((x) => x.toMap()).toList(),
    };
  }

  factory DescriptionAndImageModel.fromMap(Map<String, dynamic> map) {
    return DescriptionAndImageModel(
      descAndImgDataEntryList: List<DescriptionAndImageEntryModel>.from(
        (map['descAndImgDataEntryList'] as List<dynamic>)
            .map<DescriptionAndImageEntryModel>(
          (x) =>
              DescriptionAndImageEntryModel.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory DescriptionAndImageModel.fromJson(String source) =>
      DescriptionAndImageModel.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "DescriptionAndImageData[descAndImgDataEntryList= $descAndImgDataEntryList]";
  }
}

class DescriptionAndImageEntryModel {
  final String type;
  final String description;
  final bool isAnimated;
  final List<ImageDetailsModel> imageDataList;
  final int group;

  const DescriptionAndImageEntryModel(
      {required this.type,
      this.isAnimated = false,
      this.description = '',
      this.imageDataList = const [],
      this.group = -1});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'type': type,
      'isAnimated': isAnimated,
      'description': description,
      'imageDataList': imageDataList.map((x) => x.toMap()).toList(),
      'group': group,
    };
  }

  factory DescriptionAndImageEntryModel.fromMap(Map<String, dynamic> map) {
    return DescriptionAndImageEntryModel(
      type: map['type'] as String,
      description: map['description'] ?? '',
      isAnimated: map.containsKey('isAnimated')
          ? map['isAnimated'].toString() == 'true'
          : false,
      imageDataList: map.containsKey('images')
          ? (List<ImageDetailsModel>.from(
              (map['images'] as List<dynamic>).map<ImageDetailsModel>(
                (x) => ImageDetailsModel.fromMap(x as Map<String, dynamic>),
              ),
            ))
          : [],
      group: map['group'] ?? -1,
    );
  }

  String toJson() => json.encode(toMap());

  factory DescriptionAndImageEntryModel.fromJson(String source) =>
      DescriptionAndImageEntryModel.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "DescriptionAndImageDataEntry[type=$type, description= $description, imageDataListSize=${imageDataList.length}]";
  }

  String toDebugString() {
    return "DescriptionAndImageDataEntry[type=$type, isAnimated=$isAnimated, description= $description, imageDataList=$imageDataList]";
  }
}

class ImageDetailsModel {
  final String path;
  final String toolTip;

  const ImageDetailsModel({required this.path, required this.toolTip});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{'path': path, 'toolTip': toolTip};
  }

  factory ImageDetailsModel.fromMap(Map<String, dynamic> map) {
    return ImageDetailsModel(
        path: map['path'] as String, toolTip: map['tooltip'] as String);
  }

  String toJson() => json.encode(toMap());

  factory ImageDetailsModel.fromJson(String source) =>
      ImageDetailsModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "ImageDetails[ path=$path, toolTip=$toolTip]";
  }
}

class AnswerOptionsRawModel {
  //This class is for conversion of data to latest models.

  final String id;
  final List<DescriptionAndImageEntryModel> description;

  AnswerOptionsRawModel({required this.id, required this.description});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description.map((x) => x.toMap()).toList(),
    };
  }

  factory AnswerOptionsRawModel.fromMap(Map<String, dynamic> map) {
    return AnswerOptionsRawModel(
      id: map['id'] as String,
      description: List<DescriptionAndImageEntryModel>.from(
        (map['description'] as List<dynamic>)
            .map<DescriptionAndImageEntryModel>(
          (x) =>
              DescriptionAndImageEntryModel.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory AnswerOptionsRawModel.fromJson(String source) =>
      AnswerOptionsRawModel.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return "AnswerOptionsRaw: id= $id & description= $description";
  }
}

class OptionsRawModel {
  AnswerOptionsRawModel options;

  OptionsRawModel({required this.options});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{'options': options};
  }

  String toJson() => json.encode(toMap());

  factory OptionsRawModel.fromMap(Map<String, dynamic> map) {
    return OptionsRawModel(options: map['options'] as AnswerOptionsRawModel);
  }

  factory OptionsRawModel.fromJson(String source) {
    return OptionsRawModel.fromMap(json.decode(source) as Map<String, dynamic>);
  }
}

/*
class FlaggedQuestionModel {
  final QuestionAndAnswersModel question;
  final String flagId;
  final String shortDescriptionOrId;

  FlaggedQuestionModel({
    required this.question,
    required this.flagId,
    required this.shortDescriptionOrId,
  });

  @override
  String toString() {
    return "FlaggedQuestion[ id=${question.id} and flagid=$flagId and bundleid=${question.bundleId}]";
  }
}
*/
