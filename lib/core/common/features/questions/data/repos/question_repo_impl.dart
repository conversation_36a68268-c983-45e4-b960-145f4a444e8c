import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/data/datasources/questions_remote_datasource.dart';
import 'package:skillapp/core/common/features/questions/data/repos/util/question_repo_util.dart';
import 'package:skillapp/core/common/features/questions/domain/repos/question_repo.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';

class QuestionRepoImpl implements QuestionRepo {
  final QuestionRemoteDataSource _questionBankRemoteDataSource;
  //TODO-Remove this and refer directly using import.
  final QuestionDataUtil _questionDataUtil;

  QuestionRepoImpl(
      {required QuestionRemoteDataSource questionBankRemoteDataSource,
      required QuestionDataUtil questionDataUtil})
      : _questionBankRemoteDataSource = questionBankRemoteDataSource,
        _questionDataUtil = questionDataUtil;

  @override
  ResultFuture<QuestionAndAnswers> fetchSingleQuestionData(
      String questionId, String bundleId, String subjectId) async {
    try {
      final result = await _questionBankRemoteDataSource
          .fetchSingleQuestionData(questionId, bundleId, subjectId);
      return Right(_questionDataUtil.convertQuestionModelToEntity(result));
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }
}
