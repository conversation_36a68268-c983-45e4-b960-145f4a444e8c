import 'dart:convert';

import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/data/models/question.dart';

class QuestionDataUtil {
  QuestionAndAnswers convertQuestionModelToEntity(QuestionModel questionData) {
    String data = questionData.data;
    Map dataMap = json.decode(data) as Map<String, dynamic>;
    List<DescriptionAndImageEntry> questionList =
        mapToDescriptionAndImageEntryList(dataMap['question']);
    List<DescriptionAndImageEntry> explanationList =
        mapToDescriptionAndImageEntryList(dataMap['explanation']);
    List<DescriptionAndImageEntry> helptipList =
        mapToDescriptionAndImageEntryList(dataMap['helptip']);
    List optionsRawList =
        dataMap['options'].map((e) => AnswerOptionsRaw.fromMap(e)).toList();

    AnswerOptions answerOptions = AnswerOptions(
        entries: optionsRawList
            .cast<AnswerOptionsRaw>()
            .map((e) => OptionEntry(
                id: e.id,
                displayId: e.id,
                descAndImgDataWrapper:
                    wrapInDescriptionImageData(e.description)))
            .toList()
            .cast<OptionEntry>(),
        alignOptionsInRow: dataMap.containsKey('alignOptionsInRow')
            ? dataMap['alignOptionsInRow'] == 'true'
            : false);

    return QuestionAndAnswers(
      id: questionData.id,
      shortDescription:
          dataMap['shortDescription'] ?? 'Not Available', //TODO-Correct this!
      question: wrapInDescriptionImageData(questionList),
      correctAnswer: dataMap['correctAnswer'],
      explanation: wrapInDescriptionImageData(explanationList),
      helpTip: wrapInDescriptionImageData(helptipList),
      module: dataMap['module'] ?? '',
      complexity: dataMap['complexity'] ?? '',
      bundleId: questionData.bundleId,
      answerOptions: answerOptions,
    );
  }

  //List<DescriptionAndImageEntry> mapToDescriptionAndImageEntryList(
  //Map<String, dynamic> dataMap) {
  List<DescriptionAndImageEntry> mapToDescriptionAndImageEntryList(
      List inputDataList) {
    return inputDataList
        .map((e) => DescriptionAndImageEntry.fromMap(e))
        .toList()
        .cast<DescriptionAndImageEntry>();
  }

  DescriptionAndImageDataWrapper wrapInDescriptionImageData(
      List<DescriptionAndImageEntry> descAndImgDataEntryList) {
    List<DescriptionAndImage> descAndImgDataList = List.empty(growable: true);
    int previousGroup = -100;
    DescriptionAndImage descAndImagData = DescriptionAndImage(
        descAndImgDataEntryList: List.empty(growable: true));

    for (DescriptionAndImageEntry currentEntry in descAndImgDataEntryList) {
      int currentGroup = currentEntry.group;
      if (previousGroup == -100) {
        descAndImagData.descAndImgDataEntryList.add(currentEntry);
      } else if (currentGroup == -1 || currentGroup != previousGroup) {
        descAndImgDataList.add(descAndImagData);
        descAndImagData =
            DescriptionAndImage(descAndImgDataEntryList: [currentEntry]);
      } else if (currentGroup == previousGroup) {
        descAndImagData.descAndImgDataEntryList.add(currentEntry);
      } else {
        print("AW:THIS SHOULD NEVER BE PRINTED!!!");
      }
      previousGroup = currentEntry.group;
    }
    //For the last entry
    if (descAndImagData.descAndImgDataEntryList.isNotEmpty) {
      descAndImgDataList.add(descAndImagData);
    }

    return DescriptionAndImageDataWrapper(
        descAndImgDataList: descAndImgDataList);
  }
}
