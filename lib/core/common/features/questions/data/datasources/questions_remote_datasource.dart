import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:skillapp/core/common/features/questions/data/models/question.dart';
import 'package:skillapp/core/errors/exceptions.dart';

abstract class QuestionRemoteDataSource {
  Future<QuestionModel> fetchSingleQuestionData(
      String questionId, String bundleId, String subject);
}

class QuestionRemoteDataSourceImpl implements QuestionRemoteDataSource {
  final FirebaseFirestore _firestore;

  QuestionRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore,
        super();

  @override
  Future<QuestionModel> fetchSingleQuestionData(
      String questionId, String bundleId, String subject) async {
    //String profileId = authRepository.currentUser.currentProfile.id;
    try {
      print("Fetching question data for questionId= $questionId");
      DocumentSnapshot<Map<String, dynamic>> doc = await _firestore
          .collection('subjects/$subject/questions')
          .doc(questionId)
          .get();

      return QuestionModel(
          id: questionId, data: doc['data'], bundleId: bundleId);
    } catch (e) {
      throw ServerException(
        message: 'Error while fetching the data for questionId= $questionId',
        statusCode: '500',
      );
    }
  }
}
