/*
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';

class LocalUserModel1 extends LocalUser {
  const LocalUserModel1(
      {required super.id, required super.email, required super.currentProfile});

  const LocalUserModel1.empty()
      : this(
          id: '',
          email: '',
          currentProfile: const UserProfileModel1.empty(),
        );

  LocalUserModel1.fromMap(Map<String, dynamic> map)
      : super(
            id: map['id'],
            email: map['email'],
            currentProfile: map['currentProfile'] != null
                ? UserProfileModel1.fromMap(map['currentProfile'])
                : const UserProfileModel1.empty());
  DataMap toMap() => {
        'id': id,
        'email': email,
        'currentProfile': UserProfileModel1.toMap(currentProfile),
      };
  @override
  List<Object?> get props => [id, email];
}

class UserProfileModel1 extends UserProfile {
  const UserProfileModel1(
      {required super.id, required super.name, required super.standard});

  const UserProfileModel1.empty()
      : this(
          id: '',
          name: '',
          standard: 0,
        );

  UserProfileModel1.fromMap(Map<String, dynamic> map)
      : super(
            id: map['id'], name: map['name'], standard: map['standard'] as int);

  static DataMap toMap(currentProfile) => {
        'id': currentProfile.id,
        'name': currentProfile.name,
        'standard': currentProfile.standard,
      };
  @override
  List<Object?> get props => [id];
}

*/