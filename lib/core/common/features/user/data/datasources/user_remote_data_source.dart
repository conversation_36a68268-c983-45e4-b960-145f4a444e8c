import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/errors/exceptions.dart';

abstract class UserRemoteDataSource {
  const UserRemoteDataSource();

  Future<LocalUser> getCurrentUser();
}

class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  const UserRemoteDataSourceImpl({
    required FirebaseFirestore firebaseFirestore,
    required FirebaseAuth firebaseAuth,
  })  : _firebaseFirestore = firebaseFirestore,
        _firebaseAuth = firebaseAuth;

  final FirebaseFirestore _firebaseFirestore;
  final FirebaseAuth _firebaseAuth;
  @override
  Future<LocalUser> getCurrentUser() async {
    final user = _firebaseAuth.currentUser;
    if (user == null || user.email == null) {
      throw const ServerException(
        message: 'User is not authenticated',
        statusCode: '401',
      );
    }

    try {
      // Fetch subscriptionType from Firestore
      final userDoc =
          await _firebaseFirestore.collection('users').doc(user.email!).get();
      final data = userDoc.data();
      final String subscriptionType =
          data != null && data['subscriptionType'] != null
              ? data['subscriptionType'] as String
              : 'free';

      Set<UserProfile> userProfileList =
          await fetchUserProfileDetails(user.email!);

      //TODO-Add code to fetch the current profile from the local storage.
      //If not present then select default profile.
      //As of now, fetching one profile as default.

      final UserProfile currentProfile = userProfileList.elementAt(0);

      //TODO-Add code to persist this data into SharedPreference
      //This will avoid firestore call everytime to get the profile.

      return LocalUser(
        id: user.uid,
        email: user.email!,
        currentProfile: currentProfile,
        subscriptionType: subscriptionType,
      );
    } catch (e, s) {
      print('UserRemoteDataSourceImpl:getCurrentUser:Error:$e');
      print('UserRemoteDataSourceImpl:getCurrentUser:StackTrace:$s');
      throw const ServerException(
        message: "Error in getting user data",
        statusCode: '500',
      );
    }
  }

  Future<Set<UserProfile>> fetchUserProfileDetails(String email) async {
    QuerySnapshot querySnapshot =
        await _firebaseFirestore.collection('users/$email/profiles').get();

    Set<UserProfile> userProfileList = querySnapshot.docs.map((profile) {
      Map<String, dynamic> data = profile.data() as Map<String, dynamic>;
      print('fetchUserProfileDetails:$data');
      return UserProfile(
          id: profile.id,
          name: data['name'],
          standard: (data['standard'] as num).toInt(),
          level: data['level'] != null
              ? (data['level'] as num).toInt()
              : (data['standard'] as num).toInt());
    }).toSet();

    return Future.value(userProfileList);
  }
}

extension on User {
  LocalUser get toUser {
    return LocalUser(
      id: uid,
      email: email ?? '',
      currentProfile: const UserProfile.empty(),
      subscriptionType: '',
    );
  }
}
