import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/features/user/data/datasources/user_remote_data_source.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/features/user/domain/repos/user_repo.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';

class UserRepoImpl implements UserRepo {
  final UserRemoteDataSource _userRemoteDataSource;
  final CacheContext _cacheContext;

  UserRepoImpl({
    required UserRemoteDataSource userRemoteDataSource,
    required CacheContext cacheContext,
  })  : _userRemoteDataSource = userRemoteDataSource,
        _cacheContext = cacheContext;

  @override
  ResultFuture<LocalUser> getCurrentUser() async {
    if (!_cacheContext.isCurrentUserDataAvailable()) {
      final result = await _userRemoteDataSource.getCurrentUser();
      if (result is Failure) {
        return Left(result as ServerFailure);
      } else {
        _cacheContext.setCurrentUser(result);
      }
    }
    return Right(_cacheContext.getCurrentUser());
  }

//TODO- This is not referenced any where currently!
  @override
  ResultFuture<bool> setCurrentUser() async {
    final result = await _userRemoteDataSource.getCurrentUser();
    if (result is Failure) {
      return Left(result as ServerFailure);
    } else {
      _cacheContext.setCurrentUser(result);
    }

    return const Right(true);
  }
}
