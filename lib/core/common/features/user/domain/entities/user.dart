import 'package:equatable/equatable.dart';

class LocalUser extends Equatable {
  const LocalUser({
    required this.id,
    required this.email,
    required this.currentProfile,
    this.subscriptionType = '',
  });

  final String id;
  final String email;
  final UserProfile currentProfile;
  final String subscriptionType;

  static const LocalUser empty = LocalUser(
      id: '',
      email: '',
      currentProfile: UserProfile.empty(),
      subscriptionType: '');

  bool get isEmpty => this == LocalUser.empty;
  bool get isNotEmpty => this != LocalUser.empty;

  @override
  List<Object?> get props => [id, email, currentProfile.id, subscriptionType];
}

class UserProfile extends Equatable {
  const UserProfile(
      {required this.id,
      required this.name,
      required this.standard,
      required this.level});

  final String id;
  final String name;
  final int standard;
  final int level;

  const UserProfile.empty()
      : this(
          id: '',
          name: '',
          standard: 0,
          level: 0,
        );

  bool get isEmpty => this == const UserProfile.empty();
  bool get isNotEmpty => this != const UserProfile.empty();

  @override
  List<Object?> get props => [id];
}
/*
class UserProfileDetails extends Equatable {
  final LocalUser user;
  final UserProfile currentProfile;

  const UserProfileDetails.empty()
      : this(user: LocalUser.empty, currentProfile: UserProfile.empty());

  bool get isEmpty => this == UserProfileDetails.empty();
  bool get isNotEmpty => this != UserProfileDetails.empty();

  const UserProfileDetails({required this.user, required this.currentProfile});

  @override
  List<Object?> get props => [user, currentProfile];
}*/
