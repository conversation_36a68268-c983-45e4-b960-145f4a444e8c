import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/common/features/user/domain/repos/user_repo.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';

class GetCurrentProfile extends FutureUsecaseWithoutParams<LocalUser> {
  final UserRepo _userRepo;

  GetCurrentProfile({required UserRepo userRepo}) : _userRepo = userRepo;

  @override
  ResultFuture<LocalUser> call() async {
    try {
      final result = await _userRepo.getCurrentUser();

      if (result is Right) {
        return result;
      } else {
        return const Right(LocalUser.empty);
      }
    } catch (e, s) {
      debugPrint(e.toString());
      debugPrintStack(stackTrace: s);
      return const Right(LocalUser.empty);
    }
  }
}
