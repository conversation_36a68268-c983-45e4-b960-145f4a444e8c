import 'package:skillapp/core/common/features/user/domain/repos/user_repo.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';

class SetCurrentUserProfile implements FutureUsecaseWithoutParams<bool> {
  final UserRepo _userRepo;

  SetCurrentUserProfile({required UserRepo userRepo}) : _userRepo = userRepo;

  //TODO- This is not referenced any where currently!
  @override
  ResultFuture<bool> call() => _userRepo.setCurrentUser();
}
