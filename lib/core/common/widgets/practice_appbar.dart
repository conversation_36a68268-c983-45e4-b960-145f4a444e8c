import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:provider/provider.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/streaks/presentation/providers/current_day_streak_status_provider.dart';

enum MenuItem { savedQuestions, questionList }

class AppBarTemplate extends StatefulWidget implements PreferredSizeWidget {
  final String appBarTitle;
  final String actionType;
  const AppBarTemplate(
      {super.key, required this.appBarTitle, required this.actionType});

  @override
  State<AppBarTemplate> createState() => _AppBarTemplateState();

  @override
  Size get preferredSize => const Size.fromHeight(56);
  // Size.fromHeight(kToolbarHeight);
}

class _AppBarTemplateState extends State<AppBarTemplate> {
  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;

    return AppBar(
        backgroundColor: const Color(0xFFFFFFFF), // #FFFFFF
        shadowColor: const Color(0XFFF5F5F5),
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(24), // 24px
            bottomRight: Radius.circular(24), // 24px
          ),
        ),
        title: Text(
          widget.appBarTitle,
          style: kPracticeAppbarTs,
        ),
        leadingWidth: 40 * ffem,
        leading: IconButton(
          icon: SvgPicture.asset(
            'assets/images/backbtn_appbar.svg',
            width: 24,
            height: 24,
          ),
          onPressed: () {
            context.pop();
          },
        ),
        /*   actions: <Widget>[
        CircularPercentIndicator(
          radius: 16.0,
          lineWidth: 4.0,
          percent: 0.60,
          center: Text('3/5',
              style: TextStyle(
                color: Color(0xFF121212),
                fontSize: 10,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                height: 0,
              )),
          backgroundColor: kPrimaryColor,    
          progressColor: Color(0xFF6BC100),
        )
      ],*/
        actions: widget.actionType == 'NA'
            ? <Widget>[]
            : appBarActionWidget(context, false));
  }

  List<Widget> appBarActionWidget(BuildContext context, bool completed) {
    return <Widget>[
      Consumer<CurrentDayStreakStatusProvider>
      (builder: (_, provider, __) {
        if (provider.currentDayStreakStatus != null) {
        
           bool completedCount=provider.currentDayStreakStatus!.completedCount >= provider.currentDayStreakStatus!.targetCount;

          return completedCount?
                 CircularPercentIndicator(
              radius: 16.0,
              lineWidth: 4.0,
              percent: 1.0,
              center:
                  Image.asset('assets/images/practice/progress_completion.png'),
              // backgroundColor: kPrimaryColor,
              progressColor: kAppBarProgressCompletionBorderColor,
            )
          : /*Consumer<CurrentDayStreakStatusProvider>(
              builder: (_, provider, __) {
                //TODO - We should add a null check on 'provider.currentDayStreakStatus' and if null then don't show the widget.

                print(
                    "Current streakdaystatus${provider.currentDayStreakStatus}");*/
                CircularPercentIndicator(
                        radius: 16.0,
                        lineWidth: 4.0,
                        percent: 0.60,
                        center: Text(
                            '${provider.currentDayStreakStatus?.completedCount ?? 0}/${provider.currentDayStreakStatus?.targetCount ?? 0}',
                            style: const TextStyle(
                              color: Color(0xFF121212),
                              fontSize: 10,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              height: 0,
                            )),
                        backgroundColor: kPrimaryColor,
                        progressColor: kAppBarProgressColor,
                      );
                    
              
           // );
        } else {
          return Container();
        }
      }),
    
    /*  completed
          ? CircularPercentIndicator(
              radius: 16.0,
              lineWidth: 4.0,
              percent: 1.0,
              center:
                  Image.asset('assets/images/practice/progress_completion.png'),
              // backgroundColor: kPrimaryColor,
              progressColor: kAppBarProgressCompletionBorderColor,
            )
          : Consumer<CurrentDayStreakStatusProvider>(
              builder: (_, provider, __) {
                //TODO - We should add a null check on 'provider.currentDayStreakStatus' and if null then don't show the widget.

                print(
                    "Current streakdaystatus${provider.currentDayStreakStatus}");

                return provider.currentDayStreakStatus != null
                    ? CircularPercentIndicator(
                        radius: 16.0,
                        lineWidth: 4.0,
                        percent: 0.60,
                        center: Text(
                            '${provider.currentDayStreakStatus?.completedCount ?? 0}/${provider.currentDayStreakStatus?.targetCount ?? 0}',
                            style: const TextStyle(
                              color: Color(0xFF121212),
                              fontSize: 10,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              height: 0,
                            )),
                        backgroundColor: kPrimaryColor,
                        progressColor: kAppBarProgressColor,
                      )
                    : Container();
              },
            ),*/
      /* CircularPercentIndicator(
        radius: 16.0,
        lineWidth: 4.0,
        percent: 0.60,
        center: const Text('3/5',
            style: TextStyle(
              color: Color(0xFF121212),
              fontSize: 10,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0,
            )),
        backgroundColor: kPrimaryColor,
        progressColor: kAppBarProgressColor,
      ),*/
      PopupMenuButton<String>(
        elevation: 1,
        surfaceTintColor: kSecondaryColor,
        offset: Offset.fromDirection(1, 60),
        onSelected: (String choice) {
          String subject = widget.appBarTitle;
          //   context.go("/flaggedSubjects");
          if (MenuItem.savedQuestions.name == choice) {
            context.push('/flaggedQuestionList/$subject');
          } else if (MenuItem.questionList.name == choice) {
            context.push('/history');
          }
        },
        itemBuilder: (BuildContext context) {
          return <PopupMenuEntry<String>>[
            PopupMenuItem<String>(
              value: MenuItem.savedQuestions.name,
              textStyle: kPracticeAppBarMenuItemTs,
              child: const Center(child: Text('Saved Questions')),
            ),
            PopupMenuItem<String>(
              value: MenuItem.questionList.name,
              textStyle: kPracticeAppBarMenuItemTs,
              child: const Center(child: Text('Attempted Questions')),
            ),
          ];
        },
      )
    ];
  }
}
