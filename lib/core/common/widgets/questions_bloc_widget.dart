import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';


import 'question_widget.dart';

class QuestionBlocBuilder extends StatelessWidget {
  const QuestionBlocBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PracticeBloc, PracticeState>(
      builder: (context, state) {
        print("State at first-->$state");
        //if (!state.isError) {

         if(state is PracticeQuestionLoadingState){
            return const QuestionLoadingWidget();
         }
         else if (!state.isError) { 
          PracticeState practiceState = state;
          return BlocBuilder<PracticeBloc, PracticeState>(
            buildWhen: (previousState, currentState) {

              bool isBothIdSame= previousState.questionAndAnswer.id !=
                  currentState.questionAndAnswer.id;
              return isBothIdSame;
            },
            builder: (context, state) {
              print("AW:DisplayID= ${practiceState.displayId}");
              print(
                  "AW:Data List is = ${practiceState.questionAndAnswer.question.descAndImgDataList}");
              return QuestionWidget(
                descriptionAndImage: practiceState
                        .questionAndAnswer.question.descAndImgDataList.isEmpty
                    ? []
                    : practiceState
                        .questionAndAnswer.question.descAndImgDataList,displayId: practiceState.displayId,
              );
            },
          );
        } else {
          
          
          
          return QuestionErrorWidget(errorMessage: state.errorMessage,);

        }
      },
    );
  }
}
