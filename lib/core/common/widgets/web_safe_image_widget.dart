import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/data/firebaseUtil/fetch_image_url.dart';
import 'package:http/http.dart' as http;
import 'package:cached_network_image/cached_network_image.dart';

class WebSafeImageWidget extends StatelessWidget {
  final String imagePath;
  final double width;
  final double height;
  final String semanticLabelText;
  final bool isCoverImage;

  const WebSafeImageWidget({
    super.key,
    required this.imagePath,
    required this.width,
    required this.height,
    required this.semanticLabelText,
    required this.isCoverImage,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String?>(
      future: getImageUrl(imagePath),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            height: 100,
            width: 100,
            color: Colors.grey[200],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData || snapshot.data == null) {
          return _buildFallbackImage();
        }

        String imageUrl = snapshot.data!;
        print("🌐 Loading image URL: $imageUrl");

        // Use CachedNetworkImage for efficient caching
        return _buildCachedNetworkImage(imageUrl);
      },
    );
  }

  Widget _buildCachedNetworkImage(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      height: height != 0
          ? (height > 500 ? 500 : height)
          : null, // Maximum height constraint
      width: width != 0
          ? (width > 500 ? 500 : width)
          : null, // Maximum width constraint
      fit: isCoverImage ? BoxFit.fitHeight : BoxFit.contain,

      // Progress indicator during download (handles both loading and progress)
      progressIndicatorBuilder: (context, url, downloadProgress) => Container(
        height: 100,
        width: 100,
        color: Colors.grey[200],
        child: Center(
          child: CircularProgressIndicator(
            value: downloadProgress.progress,
            strokeWidth: 2.0,
          ),
        ),
      ),

      // Error widget with fallback strategy
      errorWidget: (context, url, error) {
        print("🔴 CachedNetworkImage failed for URL: $url");
        print("🔴 Error: $error");

        // For web, try fallback with HTTP-based loading
        if (kIsWeb) {
          print("🌐 Trying HTTP-based fallback for web...");
          return _buildHttpBasedFallback(imageUrl);
        }

        return _buildFallbackImage();
      },

      // Cache configuration for optimal performance
      cacheKey: imageUrl,
      maxHeightDiskCache: 1000,
      maxWidthDiskCache: 1000,
      memCacheHeight: 500,
      memCacheWidth: 500,

      // HTTP headers for better compatibility
      httpHeaders: const {
        'Accept': 'image/*',
        'User-Agent': 'Mozilla/5.0 (compatible; Flutter Web)',
      },
    );
  }

  Widget _buildHttpBasedFallback(String imageUrl) {
    return FutureBuilder<Uint8List?>(
      future: _loadImageBytes(imageUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            height: height != 0 ? height : 100,
            width: width != 0 ? width : 100,
            color: Colors.grey[200],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          print("🔴 HTTP fallback error: ${snapshot.error}");
          return _buildFallbackImage();
        }

        if (snapshot.hasData && snapshot.data != null) {
          print("✅ HTTP fallback successful");
          return Image.memory(
            snapshot.data!,
            height: height != 0 ? height : null,
            width: width != 0 ? width : null,
            fit: isCoverImage ? BoxFit.fitHeight : BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              print("🔴 Image.memory failed: $error");
              return _buildFallbackImage();
            },
          );
        }

        return _buildFallbackImage();
      },
    );
  }

  Future<Uint8List?> _loadImageBytes(String imageUrl) async {
    try {
      print("🌐 Fetching image bytes from: $imageUrl");

      final response = await http.get(
        Uri.parse(imageUrl),
        headers: {
          'Accept': 'image/*',
          'User-Agent': 'Mozilla/5.0 (compatible; Flutter Web)',
        },
      );

      print("🌐 HTTP Response status: ${response.statusCode}");

      if (response.statusCode == 200) {
        print("✅ Successfully fetched ${response.bodyBytes.length} bytes");
        return response.bodyBytes;
      } else {
        print(
            "🔴 HTTP Error: ${response.statusCode} - ${response.reasonPhrase}");
        return null;
      }
    } catch (e) {
      print("🔴 Exception while fetching image bytes: $e");
      return null;
    }
  }

  Widget _buildFallbackImage() {
    return Container(
      height: height != 0 ? height : 100,
      width: width != 0 ? width : 100,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        border: Border.all(color: Colors.grey[400]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported,
            color: Colors.grey[600],
            size: 40,
          ),
          const SizedBox(height: 8),
          Text(
            'Image unavailable',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
