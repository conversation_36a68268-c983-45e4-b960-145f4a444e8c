import 'package:flutter/material.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';

class ImageWidget extends StatelessWidget {
  final String image;
  final String toolTip;

  const ImageWidget({super.key, required this.image, required this.toolTip});

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    return Container(
        //  margin: EdgeInsets.fromLTRB(0 * fem, 10 * fem, 0 * fem, 0 * fem),
        margin: EdgeInsets.fromLTRB(0 * fem, 10, 0 * fem, 0 * fem),
        padding: EdgeInsets.fromLTRB(1 * fem, 0 * fem, 0 * fem, 0 * fem),
        width: double.infinity,
        decoration: BoxDecoration(
          color: const Color(0xffFFFFFF),
          // borderRadius: BorderRadius.circular(16 * fem),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Image.asset(
          image,
          height: 200,
          semanticLabel: toolTip,
        ));
  }
}
