import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class AnswerOptionsBlocBuilder extends StatelessWidget {
  const AnswerOptionsBlocBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PracticeBloc, PracticeState>(
      builder: (context, state) {
        if (state is PracticeQuestionLoadingState) {
          return const AnswerOptionsLoadingWidget();
        } else if (!state.isError) {
          return AnswerOptionsWidget(
              answerOptions: state.questionAndAnswer.answerOptions);
        } else {
          return Container();
        }
      },
    );
  }
}
