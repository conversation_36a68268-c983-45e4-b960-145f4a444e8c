import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';

class BottomActionButtonsWidget extends StatelessWidget {
  final String primaryText;
  final String secondaryText;
  final VoidCallback primaryCallBack;
  final VoidCallback secondaryCallBack;
  final bool isDualActions;

  const BottomActionButtonsWidget({
    super.key,
    required this.fem,
    required this.primaryText,
    required this.secondaryText,
    required this.primaryCallBack,
    required this.secondaryCallBack,
    required this.isDualActions,
  });

  final double fem;

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 70 * fem,
        decoration: const ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          shadows: [
            BoxShadow(
              color: Color(0x28000000),
              blurRadius: 24,
              offset: Offset(4, 0),
              spreadRadius: 0,
            )
          ],
        ),
        child: isDualActions
            ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      elevation: 0.0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(26),
                      ),
                      fixedSize: Size(161 * fem, double.infinity),
                    ),
                    onPressed: secondaryCallBack,
                    child: Text(secondaryText,
                        style: const TextStyle(
                          color: kPrimaryColor,
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                          height: 0,
                        )),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kPrimaryColor, // Background color
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(26),
                      ),
                      fixedSize:
                          Size(161 * fem, double.infinity), // Width and height
                    ),
                    onPressed:  primaryCallBack,
                    child: Text(primaryText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                          height: 0,
                        )),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.fromLTRB(
                          20 * fem, 0 * fem, 20 * fem, 0 * fem),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kPrimaryColor, // Background color
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(26),
                          ),
                          // fixedSize: Size(161 * fem, double.infinity), // Width and height
                        ),
                        onPressed: primaryCallBack,
                        child: Text(primaryText,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              height: 0,
                            )),
                      ),
                    ),
                  ),
                ],
              ));
  }
}
