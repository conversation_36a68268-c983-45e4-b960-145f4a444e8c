import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';

class DashboardDrawer extends StatefulWidget {
  const DashboardDrawer({super.key});

  @override
  State<DashboardDrawer> createState() => _DashboardDrawerState();
}

class _DashboardDrawerState extends State<DashboardDrawer> {
  final double coverHeight = 136;
  final double profileHeight = 88;

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    final topPos = coverHeight - profileHeight / 2;
    return SizedBox(
      width: 330 * fem,
      child: Drawer(
        elevation: 0.0,
        child: ListView(
          children: [
            Container(
              decoration: const BoxDecoration(
                color: kProfileHeaderBarColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: coverHeight,
                    padding: EdgeInsets.zero,
                    margin: EdgeInsets.only(bottom: (profileHeight / 2) + 5),
                    child: buildTop(topPos),
                  ),
                  profileNameSection(),
                  const SizedBox(
                    height: 15,
                    width: double.infinity,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      badgeBox(fem, '24', 'Tests'),
                      SizedBox(
                        width: 20 * fem,
                      ),
                      badgeBox(fem, '3', 'Badges')
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
            Container(
              padding:
                  EdgeInsets.fromLTRB(20 * fem, 12 * fem, 20 * fem, 12 * fem),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Column(
                children: [
                  menuItemWidget(
                      fem,
                      context,
                      'assets/images/dashboard/ios-books-application.png',
                      'Saved Questions',
                      '/flaggedSubjects'),
                  menuItemWidget(
                      fem,
                      context,
                      'assets/images/dashboard/ios-books-application.png',
                      'Attempted Questions',
                      '/history'),
                  menuItemWidget(
                      fem,
                      context,
                      'assets/images/dashboard/ios-books-application.png',
                      'Change Password',
                      '/changePassword'),
                  menuItemWidget(
                      fem,
                      context,
                      'assets/images/dashboard/ios-books-application.png',
                      'Streak Calendar',
                      '/streakCalendar'),
                  menuItemWidget(
                      fem,
                      context,
                      'assets/images/dashboard/ios-books-application.png',
                      'Level Map',
                      '/userLevelMap'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container menuItemWidget(double fem, BuildContext context, String imagePath,
      String menuText, String navigationPath) {
    return Container(
      padding: const EdgeInsets.all(0.0),
      decoration: const BoxDecoration(
          border: Border(
        bottom: BorderSide(
          color: kProfileMenuSeparationLine,
          width: 1.0,
        ),
      )),
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        leading: Image.asset(
          imagePath,
          height: 28 * fem,
          width: 28 * fem,
        ),
        title: Text(menuText, style: kProfileMenuItemtextTs),
        trailing: const IconTheme(
          data: IconThemeData(size: 15), // Adjust the icon size here
          child: Icon(Icons.arrow_forward_ios),
        ),
        onTap: () {
          //  context.push('/flaggedSubjects');
          //  context.push('/history');
          context.push(navigationPath);
        },
      ),
    );
  }

  Container badgeBox(double fem, String num, String type) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8 * fem),
      ),
      child: Row(
        children: [
          Container(
              padding:
                  EdgeInsets.fromLTRB(12 * fem, 14 * fem, 12 * fem, 14 * fem),
              child: SvgPicture.asset(
                "assets/images/award_star.svg",
                width: 28 * fem,
                height: 28 * fem,
              )),
          Column(
            children: [
              SizedBox(
                  height: 24 * fem,
                  width: 79 * fem,
                  child: Text(num, style: kProfileBadgeTs)),
              SizedBox(
                height: 18 * fem,
                width: 79 * fem,
                child: Text(type, style: kProfileBadgeTs),
              ),
            ],
          )
        ],
      ),
    );
  }

  Column profileNameSection() {
    return Column(
      children: [
        BlocBuilder<AppBloc, AppState>(builder: (context, state) {
          return Builder(builder: (context) {
            return Text(state.userProfile.currentProfile.name,
                style: kProfileNameTs);
          });
        }),
        const SizedBox(
          height: 8,
          width: double.infinity,
        ),
        GestureDetector(
          onTap: () => context.push('/editProfile'),
          child: const Text('View full profile', style: kProfileLinkTs),
        )
      ],
    );
  }

  Stack buildTop(double topPos) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        buildCoverImage(),
        Positioned(top: topPos, child: buildProfileImage()),
      ],
    );
  }

  Widget buildCoverImage() {
    return ImageDisplayWidget(
      imagePath: 'common/profileHeader.png',
      height: coverHeight,
      width: double.infinity,
      semanticLabelText: 'cover pic',
      isCoverImage: true,
    );
    /*Image.asset(
          "assets/images/profileHeader.png",
          width: double.infinity,
          height: coverHeight,
          fit: BoxFit.cover,
        )*/
  }

  Widget buildProfileImage() => CircleAvatar(
      radius: profileHeight / 2,
      backgroundColor: Colors.white,
      child: CircleAvatar(
        backgroundImage: const AssetImage('assets/images/profilePic.png'),
        radius: (profileHeight / 2) - 3,
      ));
}
