import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class WebAnswerOptionsViewWidget extends StatefulWidget {
  final OptionEntry optionEntry;
  final double distanceBetweenDisplayIdAndAnswer;

  const WebAnswerOptionsViewWidget(
      {super.key,
      required this.optionEntry,
      required this.distanceBetweenDisplayIdAndAnswer});

  @override
  State<WebAnswerOptionsViewWidget> createState() =>
      _WebAnswerOptionsViewState();
}

class _WebAnswerOptionsViewState extends State<WebAnswerOptionsViewWidget> {
  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    double ffem = fem * 0.97;

    var answerOption = widget.optionEntry;
    double rightMargin = widget.distanceBetweenDisplayIdAndAnswer;
    PracticeState practiceState = context.read<PracticeBloc>().state;

    return GestureDetector(
      onTap: () {
        //   final practiceState = context.read<PracticeBloc>().state;
        practiceState = context.read<PracticeBloc>().state;

        // Check if isSubmitted flag is true
        if ((practiceState.lastAttemptStatus == AttemptStatus.success ||
            practiceState.lastAttemptStatus == AttemptStatus.failure ||
            practiceState.lastAttemptStatus == AttemptStatus.lastAttemptDone)) {
          return;
        }

        BlocProvider.of<PracticeBloc>(context).add(
          SelectAnOption(selectedOption: answerOption.id),
        );
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(12 * fem, 0 * fem, 0 * fem, 0 * fem),
        //   padding: EdgeInsets.fromLTRB(12 * fem, 12 * fem, 10 * fem, 12 * fem),
        padding: EdgeInsets.fromLTRB(12 * fem, 12, 10 * fem, 12),

        decoration: BoxDecoration(
          color: getAnswerOptionBgColor(
              answerOption.status, practiceState.lastAttemptStatus),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: getAnswerOptionBorderColor(answerOption.status),
              offset: Offset(0 * fem, 0 * fem),
              // blurRadius: 6 * fem,
              blurRadius: 6,
            ),
          ],
        ),
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.start,
          //  crossAxisAlignment: CrossAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.fromLTRB(
                  0 * fem, 0 * fem, rightMargin * fem, 0 * fem),
              //  width: 32 * fem,
              width: 32,
              //  height: 32 * fem,
              height: 32,
              decoration: BoxDecoration(
                color: getAnswerOptionIdBorderColor(answerOption.status),
                // borderRadius: BorderRadius.circular(16 * fem),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: () {
                  switch (answerOption.status) {
                    case AnswerOptionStatuses.rightAnswer:
                      return Image.asset(
                        'assets/images/Tick.png',
                        // width: 45 * fem,
                        width: 45,
                        //  height: 45 * fem,
                        height: 45,
                      );
                    case AnswerOptionStatuses.wrongAnswer:
                      return const Text(
                        '',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          //  fontSize: 14 * ffem,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          //  height: 1.5 * ffem / fem,
                          height: 1.5,
                          color: Color(0xff575757),
                        ),
                      );
                    default:
                      return Text(
                        answerOption.displayId,
                        // textAlign: TextAlign.center,
                        style: TextStyle(
                          //  fontSize: 14 * ffem,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          //  height: 1.5 * ffem / fem,
                          height: 1.5,
                          color: getAnswerDisplayIdTextColor(
                              answerOption.status,
                              practiceState.lastAttemptStatus),
                        ),
                      );
                  }
                }(),
              ),
            ),
            Expanded(
              child: Container(
                  margin:
                      EdgeInsets.fromLTRB(0 * fem, 3 * fem, 0 * fem, 0 * fem),
                  child: answerOption
                              .descAndImgDataWrapper
                              .descAndImgDataList[0]
                              .descAndImgDataEntryList[0]
                              .type ==
                          'text'
                      ? Text(
                          answerOption
                              .descAndImgDataWrapper
                              .descAndImgDataList[0]
                              .descAndImgDataEntryList[0]
                              .description,
                          // textAlign: TextAlign.center,
                          style: TextStyle(
                            //  fontSize: 14 * ffem,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: getAnswerTextColor(answerOption.status,
                                practiceState.lastAttemptStatus),
                          ),
                        )
                      : answerOption.descAndImgDataWrapper.descAndImgDataList[0]
                                  .descAndImgDataEntryList[0].isAnimated ==
                              true
                          ? Center(
                              child: ImageAnimatedWidget(
                                image1: answerOption
                                    .descAndImgDataWrapper
                                    .descAndImgDataList[0]
                                    .descAndImgDataEntryList[0]
                                    .imageDataList[0]
                                    .path,
                                image2: answerOption
                                    .descAndImgDataWrapper
                                    .descAndImgDataList[0]
                                    .descAndImgDataEntryList[0]
                                    .imageDataList[1]
                                    .path,
                                toolTip: answerOption
                                    .descAndImgDataWrapper
                                    .descAndImgDataList[0]
                                    .descAndImgDataEntryList[0]
                                    .imageDataList[0]
                                    .toolTip,
                              ),
                            )
                          : Center(
                              child: ImageWidget(
                                image: answerOption
                                    .descAndImgDataWrapper
                                    .descAndImgDataList[0]
                                    .descAndImgDataEntryList[0]
                                    .imageDataList[0]
                                    .path,
                                toolTip: answerOption
                                    .descAndImgDataWrapper
                                    .descAndImgDataList[0]
                                    .descAndImgDataEntryList[0]
                                    .imageDataList[0]
                                    .toolTip,
                              ),
                            )),
            ),
          ],
        ),
      ),
    );
  }
}
