import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';

import 'package:skillapp/core/common/widgets/web/web_answer_options_view.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';
import 'package:skillapp/core/enums/enum_master.dart';

class WebAnswerOptionsWidget extends StatefulWidget {
  //final List<AnswerOptions> answerOptions;
  final AnswerOptions answerOptions;
  final DisplayType displayType;

  const WebAnswerOptionsWidget(
      {super.key, required this.answerOptions, required this.displayType});

  @override
  State<WebAnswerOptionsWidget> createState() => _WebAnswerOptionsState();
}

class _WebAnswerOptionsState extends State<WebAnswerOptionsWidget> {
  @override
  Widget build(BuildContext context) {
    // if (widget.answerOptions.alignOptionsInRow != true) {
    if (widget.displayType == DisplayType.list) {
      return ListView.builder(
        itemCount: widget.answerOptions.entries.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var answerOption = widget.answerOptions.entries[index];
          return WebAnswerOptionsViewWidget(
            optionEntry: answerOption,
            distanceBetweenDisplayIdAndAnswer: 30,
          );
        },
      );
    } else {
      return Column(
        children: [
          for (int i = 0; i < widget.answerOptions.entries.length; i += 2)
            IntrinsicHeight(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 32),
                child: Row(
                  children: [
                    // First item in the row
                    Expanded(
                      child: WebAnswerOptionsViewWidget(
                        optionEntry: widget.answerOptions.entries[i],
                        distanceBetweenDisplayIdAndAnswer: 10,
                      ),
                    ),
                    const SizedBox(width: 40), // crossAxisSpacing
                    // Second item in the row (if exists)
                    if (i + 1 < widget.answerOptions.entries.length)
                      Expanded(
                        child: WebAnswerOptionsViewWidget(
                          optionEntry: widget.answerOptions.entries[i + 1],
                          distanceBetweenDisplayIdAndAnswer: 10,
                        ),
                      )
                    else
                      const Expanded(
                          child:
                              SizedBox()), // Empty space for odd number of items
                  ],
                ),
              ),
            ),
        ],
      );
    }
  }
}

class WebAnswerOptionsLoadingWidget extends StatelessWidget {
  const WebAnswerOptionsLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          // Using mainAxisExtent instead of childAspectRatio for consistent height
          mainAxisExtent: 56, // Fixed height for each answer option
          mainAxisSpacing: 32,
          crossAxisSpacing: 40),
      itemCount: 5,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(left: 12),
          padding: const EdgeInsets.fromLTRB(12, 12, 10, 12),
          decoration: BoxDecoration(
            color: getAnswerOptionBgColor(
                AnswerOptionStatuses.notSelected, AttemptStatus.notAttempted),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: getAnswerOptionBorderColor(
                    AnswerOptionStatuses.notSelected),
                offset: const Offset(0, 0),
                blurRadius: 6,
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 30),
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: getAnswerOptionIdBorderColor(
                      AnswerOptionStatuses.notSelected),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: () {
                    return Text(
                      "",
                      // textAlign: TextAlign.center,
                      style: TextStyle(
                        //  fontSize: 14 * ffem,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        //  height: 1.5 * ffem / fem,
                        height: 1.5,
                        color: getAnswerDisplayIdTextColor(
                            AnswerOptionStatuses.notSelected,
                            AttemptStatus.notAttempted),
                      ),
                    );
                  }(),
                ),
              ),
              Expanded(
                  child: Container(
                margin: const EdgeInsets.only(top: 3),
                child: Text(
                  "",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: getAnswerTextColor(AnswerOptionStatuses.notSelected,
                        AttemptStatus.notAttempted),
                  ),
                ),
              ))
            ],
          ),
        );
      },
    );
  }
}
