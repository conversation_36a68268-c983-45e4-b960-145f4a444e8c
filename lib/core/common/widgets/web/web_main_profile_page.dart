import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/image_display_widget.dart';
import 'package:skillapp/core/common/widgets/web/web_dailytest_banner.dart';
import 'package:skillapp/core/common/widgets/web_safe_image_widget.dart';

import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';

class WebMainProfileWidget extends StatefulWidget {
  const WebMainProfileWidget({super.key});

  factory WebMainProfileWidget.routeBuilder(
      BuildContext context, GoRouterState state) {
    return const WebMainProfileWidget();
  }

  @override
  State<WebMainProfileWidget> createState() => _WebMainProfileWidgetState();
}

class _WebMainProfileWidgetState extends State<WebMainProfileWidget> {
  final double coverHeight = 136;
  final double profileHeight = 88;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    final topPos = coverHeight - profileHeight / 2;
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(40 * fem),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                // Removing this left and right padding of 32 to Give good view of image
                padding: EdgeInsets.fromLTRB(0 * fem, 0, 0 * fem, 0),
                decoration: ShapeDecoration(
                  color: kProfileHeaderBarColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x1E000000),
                      blurRadius: 24,
                      offset: Offset(0, 0),
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Column(
                  children: [
                    Container(
                      decoration: const BoxDecoration(
                          //    color: kProfileHeaderBarColor,
                          ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: coverHeight,
                            padding: EdgeInsets.zero,
                            margin: EdgeInsets.only(
                                bottom: (profileHeight / 2) + 5),
                            child: buildTop(topPos),
                          ),
                          profileNameSection(),
                          /*const SizedBox(
                            height: 32,
                          ),
                            Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(child: badgeBox(fem, '24', 'Tests')),
                              SizedBox(
                                width: 20 * fem,
                              ),
                              Expanded(child: badgeBox(fem, '3', 'Badges'))
                            ],
                          ),*/
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(
                          32 * fem, 12 * fem, 32 * fem, 12 * fem),
                      decoration: ShapeDecoration(
                        color: kAllBgColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        shadows: const [
                          BoxShadow(
                            color: Color(0x1E000000),
                            blurRadius: 16,
                            offset: Offset(0, 0),
                            spreadRadius: 0,
                          )
                        ],
                      ),
                      child: Column(
                        children: [
                          menuItemWidget(
                              fem,
                              context,
                              'assets/images/dashboard/ios-books-application.png',
                              'Saved Questions',
                              '/flaggedSubjects',
                              false),
                          menuItemWidget(
                              fem,
                              context,
                              'assets/images/dashboard/ios-books-application.png',
                              'Attempted Questions',
                              '/history',
                              false),
                          menuItemWidget(
                              fem,
                              context,
                              'assets/images/dashboard/ios-books-application.png',
                              'Change Password',
                              '/changePassword',
                              false),
                          menuItemWidget(
                              fem,
                              context,
                              'assets/images/dashboard/ios-books-application.png',
                              'Streak Calendar',
                              '/streakCalendar',
                              false),
                          menuItemWidget(
                              fem,
                              context,
                              'assets/images/dashboard/ios-books-application.png',
                              'Level Map',
                              '/userLevelMap',
                              true),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 40 * fem,
            ),
            const Expanded(child: WebDailyTestBanner()),
          ],
        ),
      ),
    );
  }

  Container menuItemWidget(double fem, BuildContext context, String imagePath,
      String menuText, String navigationPath, bool isLastRecord) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 12, 0, 12),
      decoration: !isLastRecord
          ? const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: kProfileMenuSeparationLine,
                  width: 1.0,
                ),
              ),
            )
          : null,
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        leading: Image.asset(
          imagePath,
          height: 28 * fem,
          width: 28 * fem,
        ),
        title: Text(
          menuText,
          style: const TextStyle(
            color: kPrimaryTextColor,
            fontSize: 16,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
            height: 0,
          ),

          //kProfileMenuItemtextTs
        ),
        trailing: const IconTheme(
          data: IconThemeData(size: 15), // Adjust the icon size here
          child: Icon(Icons.arrow_forward_ios),
        ),
        onTap: () {
          //  context.push('/flaggedSubjects');
          //  context.push('/history');
          context.push(navigationPath);
        },
      ),
    );
  }

  Container badgeBox(double fem, String num, String type) {
    return Container(
      decoration: ShapeDecoration(
        color: kAllBgColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        shadows: const [
          BoxShadow(
            color: Color(0x1E000000),
            blurRadius: 16,
            offset: Offset(0, 0),
            spreadRadius: 0,
          )
        ],
      ),
      child: Row(
        children: [
          Container(
              padding: EdgeInsets.fromLTRB(12 * fem, 14, 12 * fem, 14),
              child: SvgPicture.asset(
                "assets/images/award_star.svg",
                width: 28 * fem,
                height: 28 * fem,
              )),
          Column(
            children: [
              SizedBox(
                  height: 24,
                  width: 79 * fem,
                  child: Text(num, style: kProfileBadgeTs)),
              SizedBox(
                height: 18,
                width: 79 * fem,
                child: Text(type, style: kProfileBadgeTs),
              ),
            ],
          )
        ],
      ),
    );
  }

  Stack buildTop(double topPos) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        buildCoverImage(),
        Positioned(top: topPos, child: buildProfileImage()),
      ],
    );
  }

  Widget buildCoverImage() {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: SizedBox(
          height: coverHeight,
          width: double.infinity,
          child: Image.asset(
            'assets/images/profileHeader.png',
            width: double.infinity,
            height: coverHeight,
            fit: BoxFit.cover,
          )),

      /* child: WebSafeImageWidget(
          imagePath: 'common/profileHeader.png',
          height: coverHeight,
          width: double.infinity,
          semanticLabelText: 'cover pic',
          isCoverImage:
              false, // Set to false to use BoxFit.contain instead of BoxFit.fitHeight
        ),*/
    );
  }

  Column profileNameSection() {
    return Column(
      children: [
        BlocBuilder<AppBloc, AppState>(builder: (context, state) {
          return Column(
            children: [
              Container(
                padding: const EdgeInsets.only(
                    top: 4, left: 4, right: 12, bottom: 4),
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(width: 1, color: Color(0xFFFF7BBF)),
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: const ShapeDecoration(
                        image: DecorationImage(
                          // need to take this image from user badge
                          image:
                              NetworkImage("https://via.placeholder.com/20x20"),
                          fit: BoxFit.cover,
                        ),
                        shape: OvalBorder(),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // need to take this value from user badge
                    const Text(
                      'Knight warrior',
                      style: TextStyle(
                        color: Color(0xFF50409A),
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 0,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Text(state.userProfile.currentProfile.name,
                  style: kProfileNameTs),
              const SizedBox(
                height: 20,
              ),
              //standard is coming as zero, this needs to be mapped with the user profile year
              Text(
                '${state.userProfile.currentProfile.standard}th Grade',
                style: const TextStyle(
                  color: Color(0xFF575757),
                  fontSize: 16,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0,
                ),
              ),
            ],
          );
        }),
        const SizedBox(
          height: 20,
          width: double.infinity,
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: kPrimaryColor, // Background color
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(26),
            ),
            padding: const EdgeInsets.symmetric(
                horizontal: 20), // Padding for left and right
          ),
          onPressed: () => context.push('/editProfile'),
          child: const Text('Edit profile',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                height: 0,
              )),
        ),
      ],
    );
  }

  Widget buildProfileImage() => CircleAvatar(
      radius: profileHeight / 2,
      backgroundColor: Colors.white,
      child: CircleAvatar(
        backgroundImage: const AssetImage('assets/images/profilePic.png'),
        radius: (profileHeight / 2) - 3,
      ));
}
