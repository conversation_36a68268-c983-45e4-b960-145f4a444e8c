import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class WebFlagPopupWidget extends StatefulWidget {
  const WebFlagPopupWidget({super.key});

  @override
  State<WebFlagPopupWidget> createState() => _WebFlagPopUpState();
}

class _WebFlagPopUpState extends State<WebFlagPopupWidget> {
  FlaggedStatus? selectedFlag;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return BlocBuilder<PracticeBloc, PracticeState>(
      //  buildWhen: (previous, current) => previous.flagValue != current.flagValue,
      builder: (context, state) {
        if (!state.isError) {
          ColorFiltered coloredIcon = ColorFiltered(
              colorFilter: ColorFilter.mode(
                  getFlagColor(state.flagValue.name), BlendMode.srcIn),
              child: SvgPicture.asset(
                'assets/images/practice/flag.svg',
              ));

          return Container(
            width: 48,
            height: 48,
            margin: EdgeInsets.fromLTRB(16 * fem, 0, 0 * fem, 0),
            decoration: BoxDecoration(
                color: const Color(0xffefefef),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xff50409a),
                    offset: Offset(0 * fem, 0 * fem),
                    // blurRadius: 1 * fem,
                    blurRadius: 1,
                  ),
                ]),
            child: Padding(
              padding: const EdgeInsets.all(0.0),
              child: PopupMenuButton<FlaggedStatus>(
                color: kOnSurfaceTextColor,
                initialValue: selectedFlag,
                elevation: 0.2,
                position: PopupMenuPosition.under,
                offset: Offset.fromDirection(0, -8),
                icon: coloredIcon,
                // Callback that sets the selected popup menu item.
                onSelected: (FlaggedStatus item) {
                  print("AW:Selected item-->${item.name}");
                  context
                      .read<PracticeBloc>()
                      .add(FlagUnflagQuestion(selectedFlagId: item));
                },
                itemBuilder: (BuildContext context) {
                  List<PopupMenuItem<FlaggedStatus>> items = [];

                  items.add(flagMenuItemWidget(
                      fem, FlaggedStatus.veryhard, AppIcons.veryhard));
                  items.add(flagMenuItemWidget(
                      fem, FlaggedStatus.hard, AppIcons.hard));
                  items.add(flagMenuItemWidget(
                      fem, FlaggedStatus.moderate, AppIcons.moderate));
                  items.add(flagMenuItemWidget(
                      fem, FlaggedStatus.easy, AppIcons.easy));

                  if (state.flagValue != FlaggedStatus.notFlagged) {
                    items.add(PopupMenuItem<FlaggedStatus>(
                      value: FlaggedStatus.notFlagged,
                      child: Expanded(
                        child: Container(
                          padding: EdgeInsets.fromLTRB(
                              2 * fem, 0 * fem, 0 * fem, 0 * fem),
                          //height: 24 * fem,
                          width: 95 * fem,
                          decoration: BoxDecoration(
                            color: kOnSurfaceTextColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                                color: kDefaultFlagColor, width: 1.0),
                          ),
                          child: Row(
                            children: [
                              Container(
                                child: SvgPicture.asset(
                                    'assets/images/practice/flag.svg'),
                              ),
                              const SizedBox(width: 4.0),
                              const Text('Unflag'),
                            ],
                          ),
                        ),
                      ),
                    ));
                  }
                  return items;
                },
                //  ],
              ),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

  //flagstatus, imagepath
  PopupMenuItem<FlaggedStatus> flagMenuItemWidget(
      double fem, FlaggedStatus flaggedStatus, String imagePath) {
    return PopupMenuItem<FlaggedStatus>(
      value: flaggedStatus,
      child: Container(
        padding: EdgeInsets.fromLTRB(2 * fem, 0 * fem, 0 * fem, 0 * fem),
        //  height: 24 * fem,
        height: 24,
        width: UIParameters.getFlagWidth(flaggedStatus.name),
        decoration: BoxDecoration(
          color: getFlagBgColor(flaggedStatus.name), //kVeryHardBgColor,
          borderRadius: BorderRadius.circular(12),
          border:
              Border.all(color: getFlagColor(flaggedStatus.name), width: 1.0),
        ),
        child: Row(
          children: [
            Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24 * fem),
                  border: Border.all(
                      color: getFlagColor(flaggedStatus.name), width: 2.0),
                ),
                child: SvgPicture.asset(imagePath)),
            const SizedBox(width: 4.0),
            Text(UIParameters.getFlagText(flaggedStatus.name)),
          ],
        ),
      ),
    );
  }
}
