import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/web/web_description_image_display.dart';
import 'package:skillapp/core/common/widgets/web/web_flag_pop_widget.dart';
import 'package:skillapp/core/configs/configs.dart';

class WebQuestionWidget extends StatelessWidget {
  final List<DescriptionAndImage> descriptionAndImage;
  final int displayId;
  final bool? isDisplayQuestionId;

  const WebQuestionWidget(
      {super.key,
      required this.descriptionAndImage,
      required this.displayId,
      this.isDisplayQuestionId = true});

  @override
  Widget build(BuildContext context) {
    //  double baseWidth = 375;
    // double fem = MediaQuery.of(context).size.width / baseWidth;
    double fem = ScreenUtil.getFem(context);

    return Container(
        // margin: EdgeInsets.fromLTRB(40 * fem, 20 * fem, 16 * fem, 22 * fem),
        padding: EdgeInsets.fromLTRB(32 * fem, 32, 32 * fem, 32),
        // width: double.infinity,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x0A000000),
              blurRadius: 24,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: /*WebDescriptionAndImageFormatDisplay(
          descriptionAndImageDataList: descriptionAndImage),*/
            Row(
          // mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 3, 0, 3),
              child: (isDisplayQuestionId == true)
                  ? Text('$displayId.', style: kWebQuestionsTs)
                  : const SizedBox.shrink(),
            ),
            SizedBox(
              width: 5 * fem,
            ),
            Expanded(
              flex: 2,
              child: WebDescriptionAndImageFormatDisplay(
                  descriptionAndImageDataList: descriptionAndImage),
            ),
            SizedBox(
              width: 5 * fem,
            ),
            const WebFlagPopupWidget(),
          ],
        ));
  }
}

class WebQuestionLoadingWidget extends StatelessWidget {
  const WebQuestionLoadingWidget({super.key});
  final int numberOfContainers = 5;

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);

    return Container(
        padding: EdgeInsets.fromLTRB(32 * fem, 32, 32 * fem, 32),
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x0A000000),
              blurRadius: 24,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 5 * fem,
            ),
            Container(
              width: 20 * fem,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8 * fem),
                color: Colors.grey[200],
              ),
              child: const Text('', style: kWebQuestionsTs),
            ),
            SizedBox(
              width: 20 * fem,
            ),
            Container(
              width: 400 * fem,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8 * fem),
                color: Colors.grey[200],
              ),
              child: const Text('', style: kWebQuestionsTs),
            ),
            SizedBox(
              width: 5 * fem,
            ),
          ],
        ));
  }
}

class WebQuestionErrorWidget extends StatelessWidget {
  final String errorMessage;

  const WebQuestionErrorWidget({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 20 * fem, 16 * fem, 22 * fem),
      padding: EdgeInsets.fromLTRB(16 * fem, 10 * fem, 16 * fem, 5 * fem),
      width: double.infinity,
      // height: 180 * fem,
      decoration: BoxDecoration(
        color: const Color(0xffFFFFFF),
        borderRadius: BorderRadius.circular(16 * fem),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset('assets/images/logo/sideninja.png'),
          SizedBox(
            height: 20 * fem,
          ),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
              height: 0,
            ),
          ),
          SizedBox(
            height: 20 * fem,
          ),
        ],
      ),
    );
  }
}
