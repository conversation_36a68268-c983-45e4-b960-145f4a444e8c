import 'dart:developer';

import 'package:flutter/material.dart';

class RightSideSheet extends StatefulWidget {
  final Widget child;
  final double width;

  const RightSideSheet({
    super.key,
    required this.child,
    this.width = 300,
  });

  @override
  _RightSideSheetState createState() => _RightSideSheetState();
}

class _RightSideSheetState extends State<RightSideSheet> {
  bool _isOpen = false;

  void _toggleSheet() {
    setState(() {
      _isOpen = !_isOpen;
    });
  }

  @override
  Widget build(BuildContext context) {
    log("Inside Eight sheet widget");

    return Stack(
      children: [
        // Your main content goes here
        Positioned.fill(
          child: GestureDetector(
            onTap: _isOpen ? _toggleSheet : null,
            child: Container(
              color: _isOpen ? Colors.black54 : Colors.transparent,
            ),
          ),
        ),
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          top: 0,
          bottom: 0,
          right: _isOpen ? 0 : -widget.width,
          width: widget.width,
          child: Material(
            elevation: 8,
            child: SafeArea(
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: _toggleSheet,
                    ),
                  ),
                  Expanded(child: widget.child),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: 20,
          right: 20,
          child: ElevatedButton(
            onPressed: _toggleSheet,
            child: Text(_isOpen ? 'Close' : 'Open Sheet'),
          ),
        ),
      ],
    );
  }
}
