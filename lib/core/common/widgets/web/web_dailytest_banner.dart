import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class WebDailyTestBanner extends StatefulWidget {
  const WebDailyTestBanner({
    super.key,
  });

  @override
  State<WebDailyTestBanner> createState() => _WebDailyTestBannerState();
}

class _WebDailyTestBannerState extends State<WebDailyTestBanner> {
  @override
  void initState() {
    super.initState();
    // Check if daily test is attempted when banner loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final testsBloc = context.read<TestsBloc>();
      if (!testsBloc.isClosed) {
        testsBloc.add(CheckIsDailyTestAttemptedEvent());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      Container(
        width: double.infinity,
        height: 120,
        decoration: const ShapeDecoration(
          color: Color(0xFFA5E7C7),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
        ),
        child: Image.asset(
          'assets/images/practice/web/daily_test_img.png',
          //  'assets/images/practice/web/save.svg',
          fit: BoxFit.contain,
          width: 110,
          height: 110,
        ),
      ),
      Container(
        width: double.infinity,
        padding: const EdgeInsets.only(
          top: 20,
          left: 28,
          right: 28,
          bottom: 24,
        ),
        decoration: const ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            const Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: double.infinity,
                  child: Text(
                    'Daily test',
                    style: TextStyle(
                      color: Color(0xFF121212),
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                      height: 0,
                    ),
                  ),
                ),
                SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: Text(
                    'Take a new test every day to stay sharp and track your progress.',
                    style: TextStyle(
                      color: Color(0xFF666666),
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                      height: 0,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 28),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                BlocBuilder<TestsBloc, TestsStateInitial>(
                  builder: (context, state) {
                    // Determine if start button should be shown
                    bool showStartButton = true;
                    if (state is IsDailyTestAttemptedState) {
                      showStartButton = !state.isAttempted;
                    }

                    return showStartButton
                        ? BlocListener<TestsBloc, TestsStateInitial>(
                            listener: (context, state) {
                              if (state is NewTestFetchedState) {
                                // Navigate to daily test confirmation screen only on success
                                context.push('/daily-test-confirmation');
                              } else if (state is NewTestFetchErrorState) {
                                // Show error message in snackbar
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            child: GestureDetector(
                              onTap: () {
                                //  context.read<PracticeBloc>().add(NextButtonClicked());
                                final testsBloc = context.read<TestsBloc>();
                                if (!testsBloc.isClosed) {
                                  testsBloc.add(
                                    const FetchTodaysDailyTestEvent(),
                                  );
                                } else {
                                  // Handle the case where the bloc is closed
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                          'Unable to start test. Please try again.'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.only(
                                  top: 9,
                                  left: 24,
                                  right: 24,
                                  bottom: 10,
                                ),
                                decoration: ShapeDecoration(
                                  shape: RoundedRectangleBorder(
                                    side: const BorderSide(
                                        width: 1, color: kPrimaryColor),
                                    borderRadius: BorderRadius.circular(28),
                                  ),
                                ),
                                child: const Text(
                                  'Start',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Color(0xFF50409A),
                                    fontSize: 16,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w500,
                                    height: 0,
                                  ),
                                ),
                              ),
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.only(
                              top: 9,
                              left: 24,
                              right: 24,
                              bottom: 10,
                            ),
                            decoration: ShapeDecoration(
                              color: Colors.grey.shade200,
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                    width: 1, color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(28),
                              ),
                            ),
                            child: const Text(
                              'Completed',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                            ),
                          );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    ]);
  }
}
