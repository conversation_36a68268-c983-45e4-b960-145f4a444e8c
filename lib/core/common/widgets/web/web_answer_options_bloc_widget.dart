import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/common/widgets/web/web_answer_options.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class WebAnswerOptionsBlocBuilder extends StatelessWidget {
  const WebAnswerOptionsBlocBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PracticeBloc, PracticeState>(
      builder: (context, state) {
        if (state is PracticeQuestionLoadingState) {
          return const WebAnswerOptionsLoadingWidget();
        } else if (!state.isError) {
          return WebAnswerOptionsWidget(
            answerOptions: state.questionAndAnswer.answerOptions,
            displayType: DisplayType.grid,
          );
        } else {
          return Container();
        }
      },
    );
  }
}
