import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/common/widgets/web/web_question_widget.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';

class WebQuestionBlocBuilder extends StatelessWidget {
  final bool? isDisplayQuestionId;

  const WebQuestionBlocBuilder({super.key, this.isDisplayQuestionId = true});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PracticeBloc, PracticeState>(
      builder: (context, state) {
        if (state is PracticeQuestionLoadingState) {
          return const WebQuestionLoadingWidget();
        } else if (!state.isError) {
          PracticeState practiceState = state;
          return BlocBuilder<PracticeBloc, PracticeState>(
            buildWhen: (previousState, currentState) {
              bool isBothIdSame = previousState.questionAndAnswer.id !=
                  currentState.questionAndAnswer.id;
              return isBothIdSame;
            },
            builder: (context, state) {
              return WebQuestionWidget(
                descriptionAndImage: practiceState
                        .questionAndAnswer.question.descAndImgDataList.isEmpty
                    ? []
                    : practiceState
                        .questionAndAnswer.question.descAndImgDataList,
                displayId: practiceState.displayId,
                isDisplayQuestionId: isDisplayQuestionId,
              );
            },
          );
        } else {
          return WebQuestionErrorWidget(
            errorMessage: state.errorMessage,
          );
        }
      },
    );
  }
}
