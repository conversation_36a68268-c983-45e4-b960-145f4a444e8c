import 'package:flutter/material.dart';
import 'package:skillapp/data/firebaseUtil/fetch_image_url.dart';

class ImageDisplayWidget extends StatelessWidget {
  final String imagePath;
  final double width;
  final double height;
  final String semanticLabelText;
  final bool isCoverImage;

  const ImageDisplayWidget(
      {super.key,
      required this.imagePath,
      required this.width,
      required this.height,
      required this.semanticLabelText,
      required this.isCoverImage});

  @override
  Widget build(BuildContext context) {
    /* String imageUrl = generateImageUrl(imagePath);
   
    return Image.network(
      imageUrl,
      height: height != 0 ? height : null,
      width: width != 0 ? width : null,
      semanticLabel: semanticLabelText,
      fit: isCoverImage ? BoxFit.fitWidth : null,
      errorBuilder:
          (BuildContext context, Object error, StackTrace? stackTrace) {
        // Handle the error by displaying a placeholder or an error image
        return Image.asset(
          'assets/images/logo/sideninja.png',
          height: height != 0 ? height : null,
          width: width != 0 ? width : null,
          fit: isCoverImage ? BoxFit.fitWidth : null,
        );
      },
    );
    */

    return FutureBuilder(
      future: getImageUrl(imagePath),
      builder: (context, snapshot) {
        try {
          print("Connection state is");
          print(snapshot.connectionState);
          print("Connection state ends");
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasError) {
              return Text("Error: ${snapshot.error}");
            }
            if (snapshot.data == null) {
              return const Text("Error: URL is null");
            }
            String imageUrl = snapshot.data as String;
            print("Final image URL is $imageUrl");

            return Image.network(
              imageUrl,
              height: height != 0 ? height : null,
              width: width != 0 ? width : null,
              semanticLabel: semanticLabelText,
              fit: isCoverImage ? BoxFit.cover : null,
              // Add headers to help with CORS issues
              headers: const {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET',
                'Access-Control-Allow-Headers': 'Content-Type',
              },
              loadingBuilder: (BuildContext context, Widget child,
                  ImageChunkEvent? loadingProgress) {
                if (loadingProgress == null) {
                  return child; // Image has loaded successfully
                }
                return Container(
                  height: height != 0 ? height : 100,
                  width: width != 0 ? width : 100,
                  color: Colors.grey[200],
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  ),
                );
              },
              errorBuilder:
                  (BuildContext context, Object error, StackTrace? stackTrace) {
                // Enhanced error handling for web-specific issues like [object ProgressEvent]
                print('🔴 Image loading error: $error');
                print('🔴 Error type: ${error.runtimeType}');
                print('🔴 Image URL: $imageUrl');
                print('🔴 Stack trace: $stackTrace');

                // Return fallback asset image
                return Image.asset(
                  'assets/images/logo/sideninja.png',
                  height: height != 0 ? height : null,
                  width: width != 0 ? width : null,
                  fit: isCoverImage ? BoxFit.cover : null,
                  errorBuilder: (context, assetError, assetStackTrace) {
                    // If even the asset image fails, show a placeholder
                    return Container(
                      height: height != 0 ? height : 100,
                      width: width != 0 ? width : 100,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        border: Border.all(color: Colors.grey[400]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: Colors.grey[600],
                            size: 40,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Image unavailable',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            );
          } else {
            return const CircularProgressIndicator();
          }
        } catch (e, stackTrace) {
          print("Exception caught: $e");
          print("Stack trace: $stackTrace");
          return const Text("Error: An unexpected error occurred");
        }
      },
    );
  }
}
