import 'package:flutter/cupertino.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/common/widgets/question_image.dart';
import 'package:skillapp/core/common/widgets/question_image_animated.dart';
import 'package:skillapp/core/configs/configs.dart';

class DescriptionAndImageFormatDisplay extends StatelessWidget {
  final List<DescriptionAndImage> descriptionAndImageDataList;
  const DescriptionAndImageFormatDisplay(
      {super.key, required this.descriptionAndImageDataList});

  @override
  Widget build(BuildContext context) {
    double baseWidth = UIParameters.baseWidth;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: descriptionAndImageDataList.map((data) {
        List<DescriptionAndImageEntry> descList = data.descAndImgDataEntryList;

        if (descList.isNotEmpty && descList.length > 1) {
          return GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
            ),
            shrinkWrap: true,
            itemCount: descList.length,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              DescriptionAndImageEntry dataEntry = descList[index];
              if (dataEntry.type == 'image' && dataEntry.isAnimated == true) {
                return Center(
                  child: ImageAnimatedWidget(
                    image1: dataEntry.imageDataList[0].path,
                    image2: dataEntry.imageDataList[1].path,
                    toolTip: dataEntry.imageDataList[0].toolTip,
                  ),
                );
              } else if (dataEntry.type == 'image') {
                return Center(
                  child: ImageWidget(
                    image: dataEntry.imageDataList[0].path,
                    toolTip: dataEntry.imageDataList[0].toolTip,
                  ),
                );
              } else {
                return Container();
              }
            },
          );
        } else if (descList.isNotEmpty) {
          DescriptionAndImageEntry dataEntry = descList[0];
          if (dataEntry.type == 'text') {
            return Padding(
              padding: EdgeInsets.fromLTRB(0 * fem, 3 * fem, 0 * fem, 3 * fem),
              child: Text(dataEntry.description, style: kQuestionsTs),
            );
          } else if (dataEntry.type == 'image' &&
              dataEntry.isAnimated == true) {
            return Center(
              child: ImageAnimatedWidget(
                image1: dataEntry.imageDataList[0].path,
                image2: dataEntry.imageDataList[1].path,
                toolTip: dataEntry.imageDataList[0].toolTip,
              ),
            );
          } else if (dataEntry.type == 'image') {
            return Center(
              child: ImageWidget(
                image: dataEntry.imageDataList[0].path,
                toolTip: dataEntry.imageDataList[0].toolTip,
              ),
            );
          } else {
            return Container();
          }
        } else {
          return Container();
        }
      }).toList(),
    );
  }
}
