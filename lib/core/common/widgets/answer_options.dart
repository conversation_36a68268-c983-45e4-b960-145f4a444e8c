import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';

class AnswerOptionsWidget extends StatefulWidget {
  //final List<AnswerOptions> answerOptions;
  final AnswerOptions answerOptions;

  const AnswerOptionsWidget({super.key, required this.answerOptions});

  @override
  State<AnswerOptionsWidget> createState() => _AnswerOptionsState();
}

class _AnswerOptionsState extends State<AnswerOptionsWidget> {
  @override
  Widget build(BuildContext context) {
    //double baseWidth = 375;
    // double fem = MediaQuery.of(context).size.width / baseWidth;
    // double ffem = fem * 0.97;

    if (widget.answerOptions.alignOptionsInRow != true) {
      return ListView.builder(
        itemCount: widget.answerOptions.entries.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var answerOption = widget.answerOptions.entries[index];
          return AnswerOptionsViewWidget(
            optionEntry: answerOption,
            distanceBetweenDisplayIdAndAnswer: 30,
          );
        },
      );
    } else {
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount:
              widget.answerOptions.alignOptionsInRow == true ? 2 : 1,
        ),
        itemCount: widget.answerOptions.entries.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var answerOption = widget.answerOptions.entries[index];

          return AnswerOptionsViewWidget(
            optionEntry: answerOption,
            distanceBetweenDisplayIdAndAnswer: 3,
          );
        },
      );
    }
  }
}

class AnswerOptionsLoadingWidget extends StatelessWidget {
  const AnswerOptionsLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    return Column(
      children: [
        for (var i = 0; i < 4; i++)
          Container(
            margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 16 * fem),
            padding:
                EdgeInsets.fromLTRB(12 * fem, 12 * fem, 103 * fem, 12 * fem),
            width: double.infinity,
            height: 56 * fem,
            decoration: BoxDecoration(
              color: const Color(0xffffffff),
              borderRadius: BorderRadius.circular(8 * fem),
              boxShadow: [
                BoxShadow(
                  color: const Color(0x1e000000),
                  offset: Offset(0 * fem, 0 * fem),
                  blurRadius: 6 * fem,
                ),
              ],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin:
                      EdgeInsets.fromLTRB(0 * fem, 0 * fem, 59 * fem, 0 * fem),
                  width: 32 * fem,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: const Color(0xffefefef),
                    borderRadius: BorderRadius.circular(16 * fem),
                  ),
                  child: Center(
                    child: Text(
                      '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14 * ffem,
                        fontWeight: FontWeight.w500,
                        height: 1.5 * ffem / fem,
                        color: const Color(0xff575757),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      EdgeInsets.fromLTRB(0 * fem, 3 * fem, 0 * fem, 0 * fem),
                  child: Text(
                    '',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14 * ffem,
                      fontWeight: FontWeight.w600,
                      height: 1.5 * ffem / fem,
                      color: const Color(0xff121212),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
