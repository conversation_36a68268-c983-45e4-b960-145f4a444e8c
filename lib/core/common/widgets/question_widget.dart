import 'package:flutter/material.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';

class QuestionWidget extends StatelessWidget {
  final List<DescriptionAndImage> descriptionAndImage;
  final int displayId;

  const QuestionWidget(
      {super.key, required this.descriptionAndImage, required this.displayId});

  @override
  Widget build(BuildContext context) {
    //  double baseWidth = 375;
    // double fem = MediaQuery.of(context).size.width / baseWidth;
    double fem = ScreenUtil.getFem(context);

    return Container(
        margin: EdgeInsets.fromLTRB(16 * fem, 20 * fem, 16 * fem, 22 * fem),
        padding: EdgeInsets.fromLTRB(16 * fem, 10 * fem, 16 * fem, 5 * fem),
        width: double.infinity,
        decoration: BoxDecoration(
          color: const Color(0xffFFFFFF),
          borderRadius: BorderRadius.circular(16 * fem),
        ),
        child: DescriptionAndImageFormatDisplay(
            descriptionAndImageDataList: descriptionAndImage));
  }
}

class QuestionLoadingWidget extends StatelessWidget {
  const QuestionLoadingWidget({super.key});
  final int numberOfContainers = 5;

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 20 * fem, 16 * fem, 22 * fem),
      padding: EdgeInsets.fromLTRB(16 * fem, 10 * fem, 16 * fem, 5 * fem),
      width: double.infinity,
      height: 180 * fem,
      decoration: BoxDecoration(
        color: const Color(0xffFFFFFF),
        borderRadius: BorderRadius.circular(16 * fem),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (int i = 0; i < numberOfContainers; i++)
            Column(
              children: [
                Container(
                  width: double.infinity,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                ),
                const SizedBox(height: 10),
              ],
            ),
        ],
      ),
    );
  }
}

class QuestionErrorWidget extends StatelessWidget {
  final String errorMessage;

  const QuestionErrorWidget({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    return Container(
      margin: EdgeInsets.fromLTRB(16 * fem, 20 * fem, 16 * fem, 22 * fem),
      padding: EdgeInsets.fromLTRB(16 * fem, 10 * fem, 16 * fem, 5 * fem),
      width: double.infinity,
      // height: 180 * fem,
      decoration: BoxDecoration(
        color: const Color(0xffFFFFFF),
        borderRadius: BorderRadius.circular(16 * fem),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset('assets/images/logo/sideninja.png'),
          SizedBox(
            height: 20 * fem,
          ),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
              height: 0,
            ),
          ),
          SizedBox(
            height: 20 * fem,
          ),
        ],
      ),
    );
  }
}
