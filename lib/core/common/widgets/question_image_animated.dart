import 'package:flutter/material.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/common/widgets/web_safe_image_widget.dart';

class ImageAnimatedWidget extends StatefulWidget {
  final String image1;
  final String image2;
  final String toolTip;

  const ImageAnimatedWidget(
      {super.key,
      required this.image1,
      required this.image2,
      required this.toolTip});

  @override
  State<ImageAnimatedWidget> createState() => _ImageAnimatedState();
}

class _ImageAnimatedState extends State<ImageAnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // double baseWidth = 375;
    //  double fem = MediaQuery.of(context).size.width / baseWidth;
    double fem = ScreenUtil.getFem(context);
    // double ffem = fem * 0.97;
    return AnimatedBuilder(
      animation: _animation,
      builder: (BuildContext context, Widget? child) {
        return Opacity(
            opacity: _animation.value,
            child: WebSafeImageWidget(
              imagePath: _animation.value == 1 ? widget.image2 : widget.image1,
              width: 0,
              height: 200,
              semanticLabelText: widget.toolTip,
              isCoverImage: false,
            )

            /*   ImageDisplayWidget(  
                imagePath:
                    _animation.value == 1 ? widget.image2 : widget.image1,
                width: 0,
                //  height: 200 * fem,
                height: 200,
                semanticLabelText: widget.toolTip,
                isCoverImage: false)*/
            /*Image.asset(
                _animation.value == 1
                    ? widget.image2
                    : widget.image1,
                height: 200,
                semanticLabel: widget.toolTip,
              ),*/
            );
      },
    );
    //);
  }
}
