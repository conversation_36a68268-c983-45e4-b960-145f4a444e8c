enum FlaggedStatus {
  easy,
  moderate,
  hard,
  veryhard,
  notFlagged,
  notAvailable,
}

FlaggedStatus parseFlaggedStatus(String flagValue) {
  print("Fetching FlaggedStatus for $flagValue");
  return FlaggedStatus.values.firstWhere((e) => e.name == flagValue,
      orElse: () => throw Exception("Invalid FlaggedStatus $flagValue"));
}

AttemptStatus parseAttemptStatus(String attemptStatus) {
  print("Fetching AttemptStatus for $attemptStatus");
  return AttemptStatus.values.firstWhere((e) => e.name == attemptStatus,
      orElse: () => throw Exception("Invalid AttemptStatus $attemptStatus"));
}

enum AttemptStatus {
  success,
  failure,
  notAttempted,
  lastAttemptDone,
  retryingAfterFailure,
  notAvailable,
}

enum PracticeQuestionFlows {
  generalPractice,
  flaggedQuestionsFlow,
  historyFlow,
  notAvailable
}

enum AnswerOptionStatuses {
  notSelected,
  selected,
  wrongAnswer,
  rightAnswer,
}

enum DisplayType {
  list,
  grid,
}
