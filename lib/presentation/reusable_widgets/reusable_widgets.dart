import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skillapp/core/common/entities/description_image.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/auth/presentation/cubits/login_cubit.dart';

Image logoWidget(String imageName) {
  return Image.asset(
    imageName,
    fit: BoxFit.fitWidth,
    width: 240,
    height: 240,
  );
}

TextField reusabletextField(BuildContext context, String text, IconData icon,
    bool isPasswordType, TextEditingController controller) {
  print("AW::reusabletextField:MainFunction");

  return TextField(
    controller: controller,
    obscureText: isPasswordType,
    enableSuggestions: !isPasswordType,
    autocorrect: !isPasswordType,
    cursorColor: Colors.white,
    style: TextStyle(color: Colors.white.withOpacity(0.9)),
    decoration: InputDecoration(
        prefixIcon: Icon(
          icon,
          color: Colors.white70,
        ),
        labelText: text,
        labelStyle: TextStyle(color: Colors.white.withOpacity(0.9)),
        filled: true,
        floatingLabelBehavior: FloatingLabelBehavior.never,
        fillColor: Colors.white.withOpacity(0.3),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30.0),
            borderSide: const BorderSide(width: 0, style: BorderStyle.none))),
    keyboardType: isPasswordType
        ? TextInputType.visiblePassword
        : TextInputType.emailAddress,
    onChanged: (email) {
      context.read<LoginCubit>().emailChanged(email);
    },
  );
}

TextField reusablePasswordField(BuildContext context, String text,
    IconData icon, bool isPasswordType, TextEditingController controller) {
  print("AW::reusablePasswordField:MainFunction");

  return TextField(
    controller: controller,
    obscureText: isPasswordType,
    enableSuggestions: !isPasswordType,
    autocorrect: !isPasswordType,
    cursorColor: Colors.white,
    style: TextStyle(color: Colors.white.withOpacity(0.9)),
    decoration: InputDecoration(
        prefixIcon: Icon(
          icon,
          color: Colors.white70,
        ),
        labelText: text,
        labelStyle: TextStyle(color: Colors.white.withOpacity(0.9)),
        filled: true,
        floatingLabelBehavior: FloatingLabelBehavior.never,
        fillColor: Colors.white.withOpacity(0.3),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30.0),
            borderSide: const BorderSide(width: 0, style: BorderStyle.none))),
    keyboardType: isPasswordType
        ? TextInputType.visiblePassword
        : TextInputType.emailAddress,
    onChanged: (password) {
      context.read<LoginCubit>().passwordChanged(password);
    },
  );
}


Column getDescriptionFormat(
    List<DescriptionAndImage> descriptionAndImageDataList,
    BuildContext context) {
  double baseWidth = UIParameters.baseWidth;
  double fem = MediaQuery.of(context).size.width / baseWidth;

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisSize: MainAxisSize.min,
    children: descriptionAndImageDataList.map((data) {
      int count = 0;

      List<DescriptionAndImageEntry> descList = data.descAndImgDataEntryList;

      if (descList.isNotEmpty && descList.length > 1) {
        return GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
          ),
          shrinkWrap: true,
          itemCount: descList.length,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (BuildContext context, int index) {
            DescriptionAndImageEntry dataEntry = descList[index];
            if (dataEntry.type == 'image' && dataEntry.isAnimated == true) {
              return Center(
                child: ImageAnimatedWidget(
                  image1: dataEntry.imageDataList[0].path,
                  image2: dataEntry.imageDataList[1].path,
                  toolTip: dataEntry.imageDataList[0].toolTip,
                ),
              );
            } else if (dataEntry.type == 'image') {
              return Center(
                child: ImageWidget(
                  image: dataEntry.imageDataList[0].path,
                  toolTip: dataEntry.imageDataList[0].toolTip,
                ),
              );
            } else {
              return Container();
            }
          },
        );
      } else if (descList.isNotEmpty) {
        DescriptionAndImageEntry dataEntry = descList[0];
        if (dataEntry.type == 'text') {
          return Padding(
            padding: EdgeInsets.fromLTRB(0 * fem, 3 * fem, 0 * fem, 3 * fem),
            child: Text(dataEntry.description, style: kQuestionsTs),
          );
        } else if (dataEntry.type == 'image' && dataEntry.isAnimated == true) {
          return Center(
            child: ImageAnimatedWidget(
              image1: dataEntry.imageDataList[0].path,
              image2: dataEntry.imageDataList[1].path,
              toolTip: dataEntry.imageDataList[0].toolTip,
            ),
          );
        } else if (dataEntry.type == 'image') {
          return Center(
            child: ImageWidget(
              image: dataEntry.imageDataList[0].path,
              toolTip: dataEntry.imageDataList[0].toolTip,
            ),
          );
        } else {
          return Container();
        }
      } else {
        return Container();
      }
    }).toList(),
  );
}

/*
Container loginWithGoogleButton(BuildContext context) {
  return Container(
    width: MediaQuery.of(context).size.width,
    height: 50,
    margin: const EdgeInsets.fromLTRB(0, 10, 0, 20),
    decoration: BoxDecoration(borderRadius: BorderRadius.circular(90)),
    child: ElevatedButton(
      onPressed: () async {
        await FirebaseServices.signInWithGoogle();
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const HomeScreen()));
      },
      child: Text(
        "Sign in with Google",
        style: const TextStyle(
          color: Colors.black87,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
      style: ButtonStyle(
          backgroundColor: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.pressed)) {
              return Colors.black26;
            }
            return Colors.white;
          }),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)))),
    ),
  );
}*/
