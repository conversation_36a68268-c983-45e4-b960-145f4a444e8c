import 'package:device_preview/device_preview.dart';
import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:skillapp/core/common/providers/user_provider.dart';
import 'package:skillapp/core/services/injection_container.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/core/services/router.dart';
import 'package:skillapp/src/auth/presentation/blocs/app_bloc.dart';
import 'package:skillapp/src/streaks/presentation/providers/current_day_streak_status_provider.dart';
import 'package:skillapp/src/subscription/utils/stripe_initializer.dart';
import 'package:skillapp/src/subscription/presentation/bloc/subscription_bloc.dart';

import 'firebase_options.dart';
import 'presentation/observer/observer.dart';
//import 'logic/observer/app_bloc_observer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  HydratedBloc.storage = await HydratedStorage.build(
      storageDirectory: kIsWeb
          ? HydratedStorage.webStorageDirectory
          : await getApplicationDocumentsDirectory());
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await init();

  // Initialize Stripe SDK
  if (kIsWeb) {
    await StripeInitializer.initialize();
  }

  FlutterError.onError = (details) {
    log("AW:onError:${details.exceptionAsString()}", stackTrace: details.stack);
    log("AW:onError:${details.exception}");
    details.stack?.toString().split('\n').forEach((element) {
      log("AW:onError:Stack Element: ${element}");
    });
    log("onError:###INSIDE EXCEPTION####");
  };

  Bloc.observer = AppBlocObserver();

  if (!kIsWeb) {
    runApp(const App());
  } else {
    runApp(DevicePreview(
        enabled: kDebugMode &&
            defaultTargetPlatform != TargetPlatform.iOS &&
            defaultTargetPlatform != TargetPlatform.android,
        builder: (_) => const App()));
  }
}

class App extends StatelessWidget {
  const App({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        BlocProvider(
          create: (_) => sl<AppBloc>(),
        ),
        BlocProvider(
          create: (_) => sl<SubscriptionBloc>(),
        ),
        ChangeNotifierProvider(
          create: (_) => sl<CurrentDayStreakStatusProvider>(),
        ),
        ChangeNotifierProvider(
          create: (_) => sl<UserProvider>(),
        ),
      ],
      child: const AppView(),
    );
  }
}

class AppView extends StatefulWidget {
  const AppView({super.key});

  @override
  State<StatefulWidget> createState() => _AppViewState();
}

class _AppViewState extends State<AppView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    print("AW:AppView:build:context= $context");

    if (!kIsWeb) {
      return MaterialApp.router(
        //TODO: Explore the use of below three configurations
        //routeInformationProvider: skillAppRouter().routeInformationProvider,
        //routeInformationParser: skillAppRouter().routeInformationParser,
        //routerDelegate: skillAppRouter().routerDelegate,
        routerConfig: skillAppRouter(context),
        title: 'Selective Ninja',
        theme: const LightTheme().toThemeData(),
        debugShowCheckedModeBanner: false,
        //  builder: DevicePreview.appBuilder,
        //  locale: DevicePreview.locale(context),
      );
    } else {
      //TODO: Explore the use of below three configurations.x`
      return MaterialApp.router(
          routerConfig: skillAppRouter(context),
          title: 'Selective Ninja',
          theme: const LightTheme().toThemeData(),
          debugShowCheckedModeBanner: false,
          builder: DevicePreview.appBuilder,
          locale: DevicePreview.locale(context));
    }
  }
}
