import 'package:firebase_storage/firebase_storage.dart';

Future<String?> getImageUrl(String imagePath) async {
  try {
    print("imagePath1= $imagePath");

    if (!imagePath.contains("common")) {
      imagePath = "maths/$imagePath";
    }

    print("imagePath1 after= $imagePath");

    Reference ref = FirebaseStorage.instance.ref().child(imagePath);
    String url = await ref.getDownloadURL();
    // String url="https://firebasestorage.googleapis.com/v0/b/skillapp-eea7c.appspot.com/o/maths%2Fpizza_1.png?alt=media&token=cc1b1e95-ac90-40bf-a588-e955983dc83e";
    print("imagePath1 URL is $url");
    return url;
  } catch (e) {
    // print("Error fetching image: $e");
    return null;
  }
}

String generateImageUrl(String imagepath) {
  print("JB: Image path is $imagepath");
  imagepath = "maths/$imagepath";
  print("JB: Modified imagepath is $imagepath");
  String urlEncodedImageUrl = Uri.encodeComponent(imagepath);

  String url =
      "https://firebasestorage.googleapis.com/v0/b/skillapp-eea7c.appspot.com/o/$urlEncodedImageUrl?alt=media&token=cc1b1e95-ac90-40bf-a588-e955983dc83e";

  print(url);
  return url;
}

void main() {
  String text = Uri.encodeComponent('common/profileHeader.png');
  print("URI encoded is $text");
}
