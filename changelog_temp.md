# Changelog

This file contains a record of significant changes made to the SkillApp project.

## [Unreleased]

### Added

- Created this CHANGELOG.md file to track changes to the project
- Added EnhancedNavigationRail with user profile section and improved styling to match design
- Added test widgets section to the WebDashboardWidget with Daily Test and Full Mock Test cards
- Created reusable TestCardWidget to eliminate code duplication for similar UI components
- Added subject cards row with Reading, Maths, Thinking Skills, and Writing cards
- Created reusable SubjectCardWidget for displaying subject-specific cards
- Added breadcrumb navigation to WebHeaderWidget for improved page navigation, showing the path from Home to current page
- Added Past Tests module with a dedicated page showing test history in a table format with expandable accordion rows
- Added Test Instructions screen to display test rules and information before starting a test
- Added Test Confirmation screen to confirm user has read instructions before starting the test
- Implemented complete test flow from dashboard to instructions to confirmation to test screen with loading indicators and error handling
- Created reusable TestLayoutWidget for consistent header styling across all test-related screens

### Changed

- **Navigation Rail**: Redesigned the NavigationRailWidget to use a full-width purple background with rounded corners for selected items, replacing the default NavigationRail implementation
- **Enhanced Navigation Rail**: Created a new implementation with user profile display and updated menu items while keeping the original implementation
- **GridView Widgets**: Fixed responsive design issues in WebDailyPraticeGridview and WebAnswerOptionsWidget by replacing childAspectRatio with mainAxisExtent for consistent sizing across different screen resolutions
- **WebDailyPraticeGridview**: Updated to have a fixed height of 350px with a white background and shadow to match the LearningInfo widget
- **PracticeBloc**: Fixed "emit was called after an event handler completed normally" error in \_loadQuestionEventHandler by properly awaiting asynchronous operations and adding safety checks

### Fixed

- Fixed issue with GridView's childAspectRatio causing inconsistent sizing when screen resolution changes
- Fixed async error in PracticeBloc where emit was being called after event handler completed
- Fixed LearningInfo widget to properly fetch and display XP points, coins, and streak data on page refresh by moving event dispatch from build method to didChangeDependencies
- Fixed "Cannot add new events after calling close" error in TestsBloc by using BlocProvider.value and late initialization to ensure the bloc stays alive throughout the navigation flow
- Fixed test_screen.dart to properly handle complex question data structures and display questions correctly
- Cleaned up unused imports and variables in multiple files

## [Future Improvements]

- Address TODOs in PracticeBloc
- Replace print statements with proper logging
- Fix bloc package dependency in pubspec.yaml
