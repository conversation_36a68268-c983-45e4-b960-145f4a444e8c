
Store test data:

    tests > subjects["maths","thinking", "reading", "writing"] > levels [1,2,3,4]> test_type [daily, sectional, full] > test_id [unique_generated] > question_ids [foreign_key to questions table]

                                                                                 

    tests_repo > daliy > levels > date [ex: 30112024, 01122024] > test_id
               > full  > levels > full_mock_test_id > subjects > test_id
               > sectional > level > subject > test_id

               test_configuration
               type: full_mock_test
               subjects [order, max_allowed_time, break_time_after, subject_id]

**Fetching next sectional or full mock test for a user is based on the sequence of last respective test user has taken.


 User level

    user_id > attempted_tests > type [daily, sectional, full] 

        daily > test_id:subject_id,status[inprogress,completed],start_time,end_time,score 

                        question_ids:selected_answer,time,question_attempt_count,is_correct **Attempt time will be cumulative time of all attempts. [**This will be same structure for all test types.]

        sectional > test_id:subject_id,status[inprogress,completed],start_time,end_time,score,attempt_id (unique_generated_id) [Repli]cating last attempt data for quick fetch]

                            attempts:status[inprogress,completed],start_time,end_time,score,attempt_id

                                    question_ids **refer to structure in daily test

        full > full_test_id:status[inprogress,completed],start_time,end_time,attempt_id (unique_generated_id)
                            attempts:attempt_id:status[inprogress,completed],start_time,end_time > test_id:status[inprogress,completed],start_time,end_time,score,attempt_id,subject_id.
                                                                                                            > question_ids **refer to structure in daily test

<<check this next!!>>

