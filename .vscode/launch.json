{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "skillapp",
            "request": "launch",
            "type": "dart",
            "args": [
                "--web-port",
                "61668",
                "--dart-define=FLUTTER_WEB_USE_SKIA=true"
            ],
            "toolArgs": [
                "--dds-port=0",
                "--disable-service-auth-codes"
            ]
        },
        {
            "name": "skillapp (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "skillapp (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}