```mermaid
flowchart TD
    A[main.dart - main()] --> B[runApp]
    B --> C{kIsWeb?}
    C -->|Yes| D[DevicePreview]
    C -->|No| E[App]
    D --> E
    
    E[App - StatelessWidget] --> F[MultiRepositoryProvider]
    F --> G[BlocProvider - AppBloc]
    F --> H[ChangeNotifierProvider - CurrentDayStreakStatusProvider]
    F --> I[ChangeNotifierProvider - UserProvider]
    F --> J[AppView - StatefulWidget]
    
    J --> K{kIsWeb?}
    K -->|Yes| L[MaterialApp.router with DevicePreview]
    K -->|No| M[MaterialApp.router]
    
    L --> N[skillAppRouter]
    M --> N
    
    N --> O[GoRouter]
    O --> P[Various Routes]
    
    P --> Q[HomeScreen]
    P --> R[LandingScreen]
    P --> S[PracticeScreen]
    P --> T[EditProfile]
    P --> U[Other Screens...]
    
    Q[HomeScreen] --> V[AdaptiveLayout]
    V --> W{Device Type?}
    W -->|Mobile| X[HomeScreenMobile]
    W -->|Tablet| Y[HomeScreenTablet]
    W -->|Desktop| Z[HomeScreenDesktop]
    
    X --> AA[Scaffold]
    AA --> AB[AppBar]
    AA --> AC[DashboardDrawer]
    AA --> AD[SingleChildScrollView]
    AD --> AE[Column]
    AE --> AF[UserNameWidget]
    AE --> AG[LearningProgressNinjaWidget]
    AE --> AH[DailyPraticeGridView]
    AE --> AI[TestScroll]
    
    Z --> AJ[WebMainContentAreaWidget]
    AJ --> AK[Row]
    AK --> AL[WebNavigationRail]
    AK --> AM[Expanded Container]
    AM --> AN[Column]
    AN --> AO[WebHeaderWidget]
    AN --> AP[Expanded Container - Content]
    
    subgraph "Responsive Layout Pattern"
        V1[AdaptiveLayout] --> V2[ResponsiveWidget]
        V2 --> V3{Screen Width?}
        V3 -->|< 600px| V4[Mobile Layout]
        V3 -->|600-1200px| V5[Tablet Layout]
        V3 -->|≥ 1200px| V6[Desktop Layout]
    end
```
