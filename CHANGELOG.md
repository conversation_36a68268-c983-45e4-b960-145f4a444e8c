# Changelog

This file contains a record of significant changes made to the SkillApp project.

## [Unreleased]

### 2023-11-25

#### Added

- Implemented TestFinishDrawer with a clean, modern design for test submission confirmation
- Added visual confirmation step before test submission with checklist questions

#### Changed

- Improved answer selection handling by relying entirely on BLoC state instead of local widget state
- Removed redundant setState calls and local state variables for better state management
- Updated test submission flow to use a drawer instead of a dialog for better user experience
- Refactored TestScreen to follow BLoC pattern more consistently
- Optimized UI updates by creating a dedicated AnswerOptionsWidget that only rebuilds when the selected answer changes
- Implemented targeted rebuilds using buildWhen to improve performance

#### Fixed

- Fixed issue where selected answers weren't properly reflected in the UI when navigating between questions
- Fixed state synchronization issues between the UI and the TestsBloc
- Fixed AnswerOptionsWidget to properly update when navigating between questions by making it directly aware of the current question

### 2023-11-20

#### Added

- Created timer implementation plan document outlining the steps to add a functional countdown timer to the test screen
- Implemented a dedicated TestTimerWidget that provides a reliable countdown timer for test screens
- Added timer functionality that starts when the test begins, counts down from a configurable duration (default 30 minutes), and tracks total time taken when the test is submitted
- Timer changes color to orange when less than 10 minutes remain and red when less than 5 minutes remain
- Test is automatically submitted when the timer reaches zero

#### Changed

- Updated test navigation to use TestsBloc events (FetchNextQuestionEvent and FetchPreviousQuestionEvent) for navigating between questions
- Refactored TestScreen to use BlocBuilder pattern for reactive UI updates, eliminating local state variables
- Improved question state management to ensure UI stays in sync with the current question in TestsBloc
- Added flag functionality using FlagAQuestionEvent
- **TestContentArea**: Updated to have independent scrolling for question and answer sections with a vertical divider between them
- **TestFooter**: Modified to be constrained to the same width as the content area and fixed at the bottom of the screen
- **Selected Answer Options**: Updated styling to use white background with blue accents for better visual feedback

#### Fixed

- Fixed BlocProvider issue in TestScreen by ensuring TestsBloc is properly provided in the router configuration for both web and mobile platforms

- Fixed issue with TestFooter extending beyond the content width by using proper constraints and Material widget
- Fixed scrolling behavior in TestContentArea to allow independent scrolling of question and answer sections
- Fixed visual inconsistency between content area and footer by ensuring both have the same width constraints

## [Previous Updates]

### 2023-11-15

#### Added

- Created this CHANGELOG.md file to track changes to the project
- Added EnhancedNavigationRail with user profile section and improved styling to match design
- Added test widgets section to the WebDashboardWidget with Daily Test and Full Mock Test cards
- Created reusable TestCardWidget to eliminate code duplication for similar UI components
- Added subject cards row with Reading, Maths, Thinking Skills, and Writing cards
- Created reusable SubjectCardWidget for displaying subject-specific cards
- Added breadcrumb navigation to WebHeaderWidget for improved page navigation, showing the path from Home to current page
- Added Past Tests module with a dedicated page showing test history in a table format with expandable accordion rows

#### Changed

- **Navigation Rail**: Redesigned the NavigationRailWidget to use a full-width purple background with rounded corners for selected items, replacing the default NavigationRail implementation
- **Enhanced Navigation Rail**: Created a new implementation with user profile display and updated menu items while keeping the original implementation
- **GridView Widgets**: Fixed responsive design issues in WebDailyPraticeGridview and WebAnswerOptionsWidget by replacing childAspectRatio with mainAxisExtent for consistent sizing across different screen resolutions
- **WebDailyPraticeGridview**: Updated to have a fixed height of 350px with a white background and shadow to match the LearningInfo widget

#### Fixed

- Fixed issue with GridView's childAspectRatio causing inconsistent sizing when screen resolution changes
- Fixed async error in PracticeBloc where emit was being called after event handler completed
- Fixed LearningInfo widget to properly fetch and display XP points, coins, and streak data on page refresh by moving event dispatch from build method to didChangeDependencies
- Cleaned up unused imports and variables in multiple files

### 2023-11-16

#### Added

- Added Test Instructions screen to display test rules and information before starting a test
- Added Test Confirmation screen to confirm user has read instructions before starting the test
- Implemented complete test flow from dashboard to instructions to confirmation to test screen with loading indicators and error handling
- Created reusable TestLayoutWidget for consistent header styling across all test-related screens

#### Changed

- **PracticeBloc**: Fixed "emit was called after an event handler completed normally" error in \_loadQuestionEventHandler by properly awaiting asynchronous operations and adding safety checks

#### Fixed

- Fixed "Cannot add new events after calling close" error in TestsBloc by using BlocProvider.value and late initialization to ensure the bloc stays alive throughout the navigation flow
- Fixed navigation error when hitting back button in test screens by handling null state.extra parameters and redirecting to home
- Fixed test_screen.dart to properly handle complex question data structures and display questions correctly

### 2023-11-17

#### Changed

- **Test Duration**: Updated test duration to be dynamically fetched from TestsConfigCache instead of hardcoded values, providing more accurate test timing based on configuration
- **Test Flow**: Moved TestsBloc listener from WebDashboard to TestInstructionsScreen for improved user experience and better separation of concerns
- **TestInstructionsScreen**: Simplified implementation by converting to StatelessWidget with direct BlocBuilder pattern, removing redundant state management
- **TestsBloc Registration**: Changed TestsBloc registration from lazy singleton to singleton to ensure the same instance is used throughout the app
- **Router Configuration**: Updated router to use BlocProvider.value with the singleton TestsBloc instance for all test-related screens
- **HomeScreenDesktop**: Removed redundant TestsBloc initialization in HomeScreenDesktop since it's now a singleton in the injection container
- **TestScreen**: Removed redundant FetchNewSectionalTestEvent dispatch from TestScreen since the test is already fetched by the time the user reaches this screen
- **BlocProvider Decision**: Decided to keep both BlocProvider.value instances (in HomeScreenDesktop and router) to maintain consistent context-based dependency injection, better testability, and proper separation of concerns, despite the slight redundancy

#### Fixed

- Fixed back button not working in test instructions screen by changing navigation method from context.go() to context.push()
- Fixed loading indicator getting stuck when navigating back from test screens by replacing the loading widget with direct navigation
- Fixed circular loader appearing when clicking back button in test instructions screen by using direct navigation to home instead of context.pop()
- Fixed state preservation issues between test screens by using a global TestsBloc instance

#### Architecture Decisions

- **Multiple BlocProviders with Singleton**: After analysis, decided to keep both BlocProvider.value instances (in HomeScreenDesktop and router) even though TestsBloc is a singleton. This maintains consistent context-based dependency access patterns, preserves proper dependency injection principles, and keeps the code more testable by avoiding direct service locator dependencies in widgets.

## [Future Improvements]

- Address TODOs in PracticeBloc
- Replace print statements with proper logging
- Fix bloc package dependency in pubspec.yaml
- Consider providing TestsBloc at the app root level to eliminate the need for multiple providers while maintaining context-based dependency injection
