# Dashboard Summary Loading Fix

## Issue Description

The `LearningInfo` widget in `web_learning_info.dart` was not receiving updated data when:
1. The page was refreshed
2. The user navigated back to this page from another page (like the test submission page)

The event was being dispatched (as confirmed by debug prints in the bloc method), but the widget was not receiving the updated state.

## Root Cause Analysis

After analyzing the code, two main issues were identified:

### 1. Flag Variable Preventing Event Dispatch

```dart
@override
void didChangeDependencies() {
  super.didChangeDependencies();
  if (!_isEventDispatched) {
    final dashboardBloc = context.read<DashboardSummaryBloc>();
    if (!dashboardBloc.isClosed) {
      dashboardBloc.add(GetDashboardSummary());
    }
    _isEventDispatched = true; // Ensure the event is dispatched only once
  }
}
```

The `_isEventDispatched` flag was set to `true` after the first event dispatch and never reset. This meant that when navigating back to the page, `didChangeDependencies()` was called, but the event was not dispatched again because `_isEventDispatched` was still `true`.

### 2. BlocBuilder Not Responding to State Changes

The widget was using a `BlocBuilder` to listen for state changes:

```dart
BlocBuilder<DashboardSummaryBloc, DashboardSummaryState>(
  builder: (context, state) {
    if (state is DashboardSummaryLoading) {
      return Container();
    }
    if (state is DashboardSummaryLoaded) {
      // Display data
    } else {
      return Container();
    }
  }
)
```

If the bloc emitted the same state instance (or a state that was considered equal by `==`), the builder wouldn't be called again.

## Solution Implemented

Two changes were made to fix the issue:

### 1. Removed the `_isEventDispatched` Flag Validation

```dart
@override
void didChangeDependencies() {
  super.didChangeDependencies();
  final dashboardBloc = context.read<DashboardSummaryBloc>();
  if (!dashboardBloc.isClosed) {
    dashboardBloc.add(GetDashboardSummary());
  }
}
```

This ensures that the event is dispatched every time `didChangeDependencies()` is called, which happens when the widget is first built and when it's rebuilt after navigation.

### 2. Changed BlocBuilder to BlocConsumer

```dart
BlocConsumer<DashboardSummaryBloc, DashboardSummaryState>(
  listener: (context, state) {
    // This will be called whenever the state changes
    // We don't need to do anything here, just ensuring the widget rebuilds
  },
  builder: (context, state) {
    // Same builder logic as before
  }
)
```

Using a `BlocConsumer` instead of a `BlocBuilder` ensures that the widget is notified of state changes even if the state is considered equal by `==`. The listener is called for every state change, which can help ensure the widget rebuilds properly.

## Benefits of This Fix

1. **Consistent Data Loading**: The dashboard summary data will now be loaded every time the user navigates to the page
2. **Improved User Experience**: Users will always see the most up-to-date information
3. **Reliable State Updates**: The BlocConsumer ensures the widget responds to all state changes

## Testing

The fix was tested by:
1. Navigating to the dashboard page
2. Verifying that the dashboard summary data is loaded
3. Navigating to another page (e.g., test submission page)
4. Navigating back to the dashboard page
5. Verifying that the dashboard summary data is loaded again with the latest information
