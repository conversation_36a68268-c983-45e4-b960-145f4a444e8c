# Test History Implementation in Past Test Table

## Overview

This document describes the implementation of the Test History section in the expanded content of the Past Test Table. The Test History section displays a list of previous attempts for a specific test, showing the date, score, status, and a link to view the summary for each attempt.

## Implementation Details

The Test History section was added to the `_buildExpandedContent` method in the `PastTestTable` widget. This method is called when a user expands a row in the past tests table by clicking the dropdown icon.

### UI Structure

The Test History section consists of:

1. **Section Header**: A title "Test History" with appropriate styling
2. **History Items**: A list of previous test attempts, each showing:
   - Date (e.g., "12/02/2024")
   - Score percentage (e.g., "20%")
   - Status badge (e.g., "Failed", "Completed")
   - "View Summary" button with an arrow icon

### Code Structure

The implementation includes:

1. **Section in `_buildExpandedContent`**:
   ```dart
   // Test History section
   const Text(
     'Test History',
     style: TextStyle(
       fontSize: 16,
       fontWeight: FontWeight.bold,
       color: kPrimaryColor,
     ),
   ),
   const SizedBox(height: 12),
   
   // Test history items
   _buildTestHistoryItem('12/02/2024', 20, TestStatus.failed),
   const SizedBox(height: 8),
   _buildTestHistoryItem('12/02/2024', 20, TestStatus.failed),
   ```

2. **New Helper Method `_buildTestHistoryItem`**:
   ```dart
   Widget _buildTestHistoryItem(String date, int score, TestStatus status) {
     return Container(
       padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
       decoration: BoxDecoration(
         color: Colors.white,
         borderRadius: BorderRadius.circular(8),
         boxShadow: const [
           BoxShadow(
             color: Color.fromRGBO(0, 0, 0, 0.05),
             blurRadius: 4,
             offset: Offset(0, 2),
           ),
         ],
       ),
       child: Row(
         children: [
           // Date
           Expanded(
             flex: 2,
             child: Text(
               date,
               style: const TextStyle(
                 fontSize: 14,
                 fontWeight: FontWeight.w500,
               ),
             ),
           ),
           
           // Score
           Expanded(
             flex: 1,
             child: Text(
               '$score%',
               style: const TextStyle(
                 fontSize: 14,
                 fontWeight: FontWeight.w500,
                 color: Colors.red,
               ),
             ),
           ),
           
           // Status
           Expanded(
             flex: 1,
             child: Container(
               padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
               decoration: BoxDecoration(
                 color: status == TestStatus.failed
                     ? const Color.fromRGBO(255, 0, 0, 0.1)
                     : status == TestStatus.completed
                         ? const Color.fromRGBO(0, 128, 0, 0.1)
                         : const Color.fromRGBO(255, 165, 0, 0.1),
                 borderRadius: BorderRadius.circular(16),
               ),
               child: Text(
                 _getStatusText(status),
                 textAlign: TextAlign.center,
                 style: TextStyle(
                   fontSize: 12,
                   color: status == TestStatus.failed
                       ? Colors.red
                       : status == TestStatus.completed
                           ? Colors.green
                           : Colors.orange,
                   fontWeight: FontWeight.w500,
                 ),
               ),
             ),
           ),
           
           // View Summary button
           TextButton.icon(
             onPressed: () {
               // View summary action
             },
             icon: const Icon(Icons.arrow_forward, size: 16),
             label: const Text('View Summary'),
             style: TextButton.styleFrom(
               foregroundColor: kPrimaryColor,
               padding: const EdgeInsets.symmetric(horizontal: 8),
             ),
           ),
         ],
       ),
     );
   }
   ```

### Visual Design

The Test History items are designed with:
- White background with subtle shadow
- Rounded corners (8px radius)
- Clear spacing between items (8px)
- Status badges with color-coded backgrounds (light red for failed, light green for completed, light orange for pending)
- Color-coded status text (red for failed, green for completed, orange for pending)
- "View Summary" button with forward arrow icon

## Future Enhancements

1. **Dynamic Data**: Replace the hardcoded test history items with actual data from the test history repository
2. **Pagination**: Add pagination if the test has many previous attempts
3. **Sorting**: Allow sorting by date, score, or status
4. **Filtering**: Add options to filter history by status or date range
5. **Action Handling**: Implement the "View Summary" button functionality to navigate to the detailed test summary page
