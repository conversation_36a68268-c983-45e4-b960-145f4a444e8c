# Test Progress Panel Overlay Implementation

This document outlines the implementation of the Test Progress Panel as an overlay within the TestScreen, rather than as a separate page.

## Overview

The Test Progress Panel is implemented as an overlay that slides in from the right side of the screen when the user clicks the "View progress summary" button in the TestScreenHeader. The panel shows a grid of questions with their status (answered, unanswered, current, flagged) and allows the user to navigate to a specific question.

## Implementation Details

### 1. TestScreen Structure

The TestScreen is structured as a Stack with two main components:
1. The main content (Column with header, content, and footer)
2. The progress panel overlay (conditionally shown when TestProgressState is active)

```dart
return Stack(
  children: [
    // Main content
    Column(
      children: [
        // Header
        TestScreenHeader(...),
        
        // Content area with footer
        Expanded(
          child: Stack(...),
        ),
      ],
    ),
    
    // Progress summary panel
    if (state is TestProgressState)
      Positioned(
        right: 0,
        top: 0,
        bottom: 0,
        child: SlideInPanel(...),
      ),
  ],
);
```

### 2. State Management

The TestScreen uses a BlocConsumer to:
1. Listen for TestProgressState events and show the panel
2. Build the UI based on the current state

```dart
BlocConsumer<TestsBloc, TestsStateInitial>(
  listener: (context, state) {
    // Handle TestProgressState to show the progress panel
    if (state is TestProgressState) {
      setState(() {
        _isProgressPanelOpen = true;
      });
    }
  },
  builder: (context, state) {
    // Build UI based on state
    // ...
  },
)
```

### 3. SlideInPanel Widget

The SlideInPanel widget handles the animation for sliding the panel in from the right side of the screen:
- Uses AnimationController for smooth animations
- Slides in from the right when opened
- Slides out to the right when closed

### 4. TestProgressSummaryModal Widget

The TestProgressSummaryModal widget displays the grid of questions with their status:
- Shows a grid of questions with color-coded status indicators
- Provides a legend explaining the colors
- Allows the user to click on a question to navigate to it
- Has a close button to dismiss the panel

### 5. Navigation Between Questions

When a question is selected in the panel:
1. The panel finds the order of the selected question
2. It compares it with the current question order
3. It dispatches the appropriate event (FetchPreviousQuestionEvent or FetchNextQuestionEvent)
4. It closes the panel

```dart
onQuestionSelected: (questionId) {
  // Find the question order
  final questionToNavigate = state
      .testWithQuestionsData.testQuestionEntrySet
      .firstWhere((q) => q.questionId == questionId);

  final currentOrder = state.currentQuestion.order;
  final targetOrder = questionToNavigate.order;

  if (targetOrder < currentOrder) {
    // Navigate backward
    context.read<TestsBloc>().add(
          FetchPreviousQuestionEvent(
            currentOrder: currentOrder,
          ),
        );
  } else if (targetOrder > currentOrder) {
    // Navigate forward
    context.read<TestsBloc>().add(
          FetchNextQuestionEvent(
            currentOrder: currentOrder,
          ),
        );
  }

  // Close the panel
  setState(() {
    _isProgressPanelOpen = false;
  });
}
```

## Benefits of This Approach

1. **Seamless User Experience**: The user stays on the same screen, maintaining context
2. **Efficient Navigation**: The user can quickly navigate between questions without leaving the test screen
3. **Visual Consistency**: The panel appears as an overlay, maintaining the visual hierarchy
4. **State Preservation**: The test state is preserved as the user navigates between questions

## Future Improvements

1. **Direct Navigation**: Implement a NavigateToQuestionEvent to directly navigate to a specific question
2. **Filtering**: Add options to filter questions by status (e.g., show only unanswered questions)
3. **Keyboard Navigation**: Add keyboard shortcuts for navigating between questions
4. **Responsive Design**: Improve the panel's responsiveness on different screen sizes
