# Test Progress Summary Implementation

This document outlines the implementation plan for the Test Progress Summary modal panel that appears from the right side of the screen when the fullscreen toggle button is clicked.

## Overview

The feature will display a modal panel showing all questions in the test with visual indicators for their status (answered, unanswered, current, flagged). Users can see their progress at a glance and potentially navigate to specific questions.

## Component Structure

1. **TestProgressSummaryModal** - The main modal component
2. **QuestionStatusIndicator** - Individual question circles with status colors
3. **StatusLegend** - The legend showing what each color means

## Implementation Steps

### 1. Create the Modal Component

#### TestProgressSummaryModal Widget
- Create a new stateless widget `TestProgressSummaryModal`
- Accept parameters:
  - `totalQuestions`: Total number of questions in the test
  - `currentQuestionIndex`: The index of the current question
  - `answeredQuestions`: List of indices of answered questions
  - `flaggedQuestions`: List of indices of flagged questions
  - `onQuestionSelected`: Callback when a question is selected
  - `onClose`: Callback when the modal is closed

### 2. Update TestScreenHeader

- Modify the `TestScreenHeader` widget to include a button for opening the progress summary
- Update the `onFullscreenToggle` callback to dispatch the `ViewTestProgressEvent` to the TestsBloc

### 3. Create the Modal Display Logic

- Use a custom implementation to slide in from the right
- Set the width to 20% of the screen width
- Include a close button (X) in the top right corner
- Implement the "Back" button at the bottom

### 4. Implement Question Status Grid

- Create a grid layout for the question indicators
- Each indicator should be a circle with:
  - Number inside (question number)
  - Color based on status:
    - Green: Answered
    - Gray: Unanswered
    - Purple: Current
    - Yellow: Flagged
- Make indicators tappable to navigate to that question

### 5. Implement Status Legend

- Create a row of status indicators with labels:
  - "Answered" (Green)
  - "Unanswered" (Gray)
  - "Current" (Purple)
  - "Flagged" (Yellow)

### 6. Connect to TestsBloc

- Use BlocListener to listen for state changes related to the `ViewTestProgressEvent`
- When the appropriate state is emitted, show the modal panel
- Extract the necessary data from the state:
  - Total questions from `state.testWithQuestionsData.testQuestionEntrySet.length`
  - Current question from `state.currentQuestion.order`
  - Answered questions by filtering questions with non-empty `selectedAnswer`
  - Flagged questions from `state.flaggedQuestions`

### 7. Implement Navigation Logic

- When a question indicator is tapped:
  - Close the modal
  - Dispatch a navigation event to the TestsBloc (possibly a new `NavigateToQuestionEvent` or using existing events)
  - The TestsBloc should handle the navigation logic to move to the selected question

## Detailed UI Specifications

### Modal Panel
- Width: 20% of screen width
- Background: White
- Border radius: 8px on left side
- Shadow: Subtle shadow for depth
- Padding: 24px

### Header
- Title: "Progress Summary"
- Font: Bold, 18px
- Close button: X icon in top right

### Status Legend
- Position: Below header
- Layout: Row with evenly spaced items
- Each item: Color dot + label
- Font size: 14px

### Question Grid
- Layout: Grid with 3-4 columns (adjusted for narrower panel)
- Spacing: 12px between items
- Each indicator:
  - Size: 40px diameter circle
  - Border radius: 20px (fully rounded)
  - Text: Question number in center
  - Font: 14px, white or black depending on background color
  - States:
    - Answered: Green background (#4CAF50)
    - Unanswered: Light gray background (#E0E0E0)
    - Current: Purple background (#673AB7)
    - Flagged: Yellow background (#FFC107)

### Back Button
- Position: Bottom center
- Style: Outlined button
- Text: "Back"
- Padding: 12px horizontal, 8px vertical
- Action: Close the modal

## Technical Considerations

1. **Integration with TestsBloc**: Properly integrate with the existing `ViewTestProgressEvent` and related state handling
2. **Narrow Layout**: Adjust the layout to work well with the narrower 20% width
3. **Performance**: The grid should handle a large number of questions efficiently
4. **Responsiveness**: The layout should adapt to different screen sizes
5. **State Management**: Use BlocBuilder/BlocListener to react to changes in question states
6. **Animation**: Implement smooth slide-in/out animations for the modal
7. **Accessibility**: Ensure all elements are properly labeled for screen readers
