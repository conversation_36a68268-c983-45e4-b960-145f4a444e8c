# Answer Selection Fix for Test Progress Navigation

## Issue Description

When navigating between questions using the Test Progress Drawer, there was an issue with answer selections:

1. If a user selected an answer for a question (e.g., option A, B, C, D, or E)
2. Then opened the progress drawer panel
3. And selected a different question from the drawer

The answer selected for the first question would incorrectly appear as selected for all subsequent questions navigated to via the progress drawer.

## Root Cause

The root cause of the issue was that the `_selectedAnswerId` variable in the TestScreen widget was not being updated when navigating to a new question using the `ViewSelectedQuestionEvent`. This variable is used to track the currently selected answer in the UI.

When navigating between questions, the TestsBloc correctly maintained the state of which answers had been selected for each question, but the UI wasn't properly reflecting this state because `_selectedAnswerId` wasn't being synchronized with the current question's selected answer.

## Solution

The solution was to update the TestScreen to properly synchronize the `_selectedAnswerId` with the current question's selected answer whenever a new question is displayed.

This was implemented by:

1. Changing the `BlocBuilder` to a `BlocConsumer` to add a listener
2. Adding a listener that updates `_selectedAnswerId` whenever a new `TestsDataState` is emitted

```dart
body: BlocConsumer<TestsBloc, TestsStateInitial>(
  listener: (context, state) {
    // Update _selectedAnswerId when a new question is displayed
    if (state is TestsDataState) {
      // Set _selectedAnswerId to the selected answer for the current question (if any)
      setState(() {
        _selectedAnswerId = state.currentQuestion.selectedAnswer;
      });
    }
  },
  builder: (context, state) {
    // Existing builder code...
  },
),
```

## Benefits of This Fix

1. **Preserved Answer Selections**: Questions that have been answered retain their selected answers when navigating back to them
2. **Correct UI State**: Questions that haven't been answered don't show any selection
3. **Consistent User Experience**: The UI correctly reflects the state of each question as the user navigates between them

## Testing

The fix was tested by:
1. Selecting an answer for a question
2. Opening the progress drawer
3. Navigating to a different question
4. Verifying that the new question doesn't show the answer from the previous question
5. Navigating back to the first question
6. Verifying that the first question still shows the originally selected answer

This ensures that the user's progress is correctly preserved while navigating through the test.
