# Stripe Subscription Integration Documentation

This document provides a comprehensive guide to the Stripe subscription integration implemented in the SkillApp. It includes step-by-step instructions, explanations for each file, and a sequence diagram to illustrate the flow.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Sequence Diagram](#sequence-diagram)
3. [Implementation Details](#implementation-details)
   - [Domain Layer](#domain-layer)
   - [Data Layer](#data-layer)
   - [Presentation Layer](#presentation-layer)
4. [Integration Steps](#integration-steps)
5. [Testing and Troubleshooting](#testing-and-troubleshooting)

## Architecture Overview

The implementation follows the clean architecture pattern with three main layers:

- **Domain Layer**: Contains business logic, entities, repository interfaces, and use cases
- **Data Layer**: Contains data models, repository implementations, and data sources
- **Presentation Layer**: Contains UI components, BLoC for state management, and user interactions

This separation of concerns ensures that the business logic is independent of the UI and data sources, making the code more maintainable and testable.

## Sequence Diagram

```
┌─────────────┐    ┌───────────────┐    ┌────────────────────┐    ┌─────────────────┐    ┌───────────┐    ┌────────┐
│ User (UI)   │    │SubscriptionBloc│    │SubscriptionUseCase│    │SubscriptionRepo │    │RemoteData │    │ Stripe │
└──────┬──────┘    └───────┬───────┘    └─────────┬──────────┘    └────────┬────────┘    └─────┬─────┘    └───┬────┘
       │                   │                      │                         │                   │              │
       │ Select Plan       │                      │                         │                   │              │
       │───────────────────>                      │                         │                   │              │
       │                   │                      │                         │                   │              │
       │                   │ CreateCheckoutSession│                         │                   │              │
       │                   │─────────────────────>│                         │                   │              │
       │                   │                      │                         │                   │              │
       │                   │                      │ createCheckoutSession() │                   │              │
       │                   │                      │────────────────────────>│                   │              │
       │                   │                      │                         │                   │              │
       │                   │                      │                         │ createCheckout()  │              │
       │                   │                      │                         │──────────────────>│              │
       │                   │                      │                         │                   │              │
       │                   │                      │                         │                   │ Create      │
       │                   │                      │                         │                   │ Session     │
       │                   │                      │                         │                   │─────────────>
       │                   │                      │                         │                   │              │
       │                   │                      │                         │                   │ Session URL │
       │                   │                      │                         │                   │<─────────────
       │                   │                      │                         │                   │              │
       │                   │                      │                         │ Checkout URL      │              │
       │                   │                      │                         │<──────────────────│              │
       │                   │                      │                         │                   │              │
       │                   │                      │ Checkout URL            │                   │              │
       │                   │                      │<────────────────────────│                   │              │
       │                   │                      │                         │                   │              │
       │                   │ CheckoutSessionCreated                         │                   │              │
       │                   │<─────────────────────│                         │                   │              │
       │                   │                      │                         │                   │              │
       │ Redirect to       │                      │                         │                   │              │
       │ Stripe Checkout   │                      │                         │                   │              │
       │<───────────────────                      │                         │                   │              │
       │                   │                      │                         │                   │              │
       │ Complete Payment  │                      │                         │                   │              │
       │ on Stripe Page    │                      │                         │                   │              │
       │───────────────────────────────────────────────────────────────────────────────────────────────────────>
       │                   │                      │                         │                   │              │
       │ Webhook Notification (Payment Success)   │                         │                   │              │
       │<───────────────────────────────────────────────────────────────────────────────────────────────────────
       │                   │                      │                         │                   │              │
       │ Return to App     │                      │                         │                   │              │
       │ (Success Page)    │                      │                         │                   │              │
       │                   │                      │                         │                   │              │
```

## Implementation Details

### Domain Layer

#### 1. Entities (`lib/src/subscription/domain/entities/`)

**subscription_plan.dart**
```dart
class SubscriptionPlan {
  final String id;
  final String name;
  final double price;
  final String interval;
  final List<String> features;
  final bool isActive;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.price,
    required this.interval,
    required this.features,
    this.isActive = false,
  });
}
```
- **Purpose**: Defines the core business object representing a subscription plan
- **Explanation**: Contains all the essential properties of a subscription plan without any framework dependencies

**subscription.dart**
```dart
class Subscription {
  final String id;
  final String customerId;
  final String planId;
  final DateTime startDate;
  final DateTime? endDate;
  final String status;

  Subscription({
    required this.id,
    required this.customerId,
    required this.planId,
    required this.startDate,
    this.endDate,
    required this.status,
  });
}
```
- **Purpose**: Defines the core business object representing a user's subscription
- **Explanation**: Contains all the essential properties of a subscription without any framework dependencies

**payment_method.dart**
```dart
class PaymentMethod {
  final String id;
  final String type;
  final String last4;
  final String brand;
  final int expiryMonth;
  final int expiryYear;

  PaymentMethod({
    required this.id,
    required this.type,
    required this.last4,
    required this.brand,
    required this.expiryMonth,
    required this.expiryYear,
  });
}
```
- **Purpose**: Defines the core business object representing a payment method
- **Explanation**: Contains all the essential properties of a payment method without any framework dependencies

#### 2. Repository Interfaces (`lib/src/subscription/domain/repositories/`)

**subscription_repository.dart**
```dart
abstract class SubscriptionRepository {
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans();
  Future<Either<Failure, Subscription>> getCurrentSubscription();
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods();
  Future<Either<Failure, PaymentMethod>> addPaymentMethod(String paymentMethodId);
  Future<Either<Failure, Subscription>> createSubscription(String planId, String paymentMethodId);
  Future<Either<Failure, Subscription>> cancelSubscription(String subscriptionId);
  Future<Either<Failure, String>> createPaymentIntent(String planId);
  Future<Either<Failure, String>> createCheckoutSession(String planId);
}
```
- **Purpose**: Defines the contract for the repository implementation
- **Explanation**: Uses the Either type from dartz to handle success and failure cases, ensuring proper error handling throughout the application

#### 3. Use Cases (`lib/src/subscription/domain/usecases/`)

**get_subscription_plans.dart**
```dart
class GetSubscriptionPlans implements UseCase<List<SubscriptionPlan>, NoParams> {
  final SubscriptionRepository repository;

  GetSubscriptionPlans(this.repository);

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> call(NoParams params) {
    return repository.getSubscriptionPlans();
  }
}
```
- **Purpose**: Implements the use case for retrieving subscription plans
- **Explanation**: Follows the single responsibility principle by handling only one specific business operation

**create_checkout_session.dart**
```dart
class CreateCheckoutSessionParams extends Equatable {
  final String planId;

  const CreateCheckoutSessionParams({required this.planId});

  @override
  List<Object> get props => [planId];
}

class CreateCheckoutSession implements UseCase<String, CreateCheckoutSessionParams> {
  final SubscriptionRepository repository;

  CreateCheckoutSession(this.repository);

  @override
  Future<Either<Failure, String>> call(CreateCheckoutSessionParams params) {
    return repository.createCheckoutSession(params.planId);
  }
}
```
- **Purpose**: Implements the use case for creating a Stripe checkout session
- **Explanation**: Takes a planId parameter and returns a checkout URL for redirecting the user to Stripe's payment page

### Data Layer

#### 1. Models (`lib/src/subscription/data/models/`)

**subscription_plan_model.dart**
```dart
class SubscriptionPlanModel extends SubscriptionPlan {
  SubscriptionPlanModel({
    required String id,
    required String name,
    required double price,
    required String interval,
    required List<String> features,
    bool isActive = false,
  }) : super(
          id: id,
          name: name,
          price: price,
          interval: interval,
          features: features,
          isActive: isActive,
        );

  factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanModel(
      id: json['id'],
      name: json['name'],
      price: (json['price'] as num).toDouble(),
      interval: json['interval'],
      features: List<String>.from(json['features']),
      isActive: json['is_active'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'interval': interval,
      'features': features,
      'is_active': isActive,
    };
  }
}
```
- **Purpose**: Extends the domain entity to add data layer functionality
- **Explanation**: Adds JSON serialization/deserialization capabilities to the entity

#### 2. Data Sources (`lib/src/subscription/data/datasources/`)

**subscription_remote_data_source.dart**
```dart
abstract class SubscriptionRemoteDataSource {
  Future<List<SubscriptionPlanModel>> getSubscriptionPlans();
  Future<SubscriptionModel> getCurrentSubscription();
  Future<List<PaymentMethodModel>> getPaymentMethods();
  Future<PaymentMethodModel> addPaymentMethod(String paymentMethodId);
  Future<SubscriptionModel> createSubscription(String planId, String paymentMethodId);
  Future<SubscriptionModel> cancelSubscription(String subscriptionId);
  Future<String> createPaymentIntent(String planId);
  Future<String> createCheckoutSession(String planId);
}

class SubscriptionRemoteDataSourceImpl implements SubscriptionRemoteDataSource {
  final http.Client client;
  final String baseUrl;
  
  SubscriptionRemoteDataSourceImpl({
    required this.client,
    required this.baseUrl,
  });

  @override
  Future<String> createCheckoutSession(String planId) async {
    try {
      final response = await client.post(
        Uri.parse('$baseUrl/checkout-sessions'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'plan_id': planId}),
      );

      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['checkout_url'];
      } else {
        throw ServerException();
      }
    } catch (e) {
      throw ServerException();
    }
  }
  
  // Other method implementations...
}
```
- **Purpose**: Handles communication with external data sources (API)
- **Explanation**: Uses HTTP client to make API calls to the backend server, which communicates with Stripe

#### 3. Services (`lib/src/subscription/data/services/`)

**stripe_payment_service.dart**
```dart
class StripePaymentService {
  Future<void> initStripe(String publishableKey) async {
    Stripe.publishableKey = publishableKey;
    await Stripe.instance.applySettings();
  }
  
  Future<void> redirectToCheckout({
    required String checkoutSessionUrl,
    required BuildContext context,
  }) async {
    try {
      final Uri url = Uri.parse(checkoutSessionUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw PaymentException(message: 'Could not launch checkout page');
      }
    } catch (e) {
      throw PaymentException(message: 'Failed to redirect to checkout: ${e.toString()}');
    }
  }
  
  // Other methods...
}
```
- **Purpose**: Provides Stripe-specific functionality
- **Explanation**: Handles initialization of the Stripe SDK and redirection to the Stripe Checkout page

#### 4. Repository Implementation (`lib/src/subscription/data/repositories/`)

**subscription_repository_impl.dart**
```dart
class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  SubscriptionRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, String>> createCheckoutSession(String planId) async {
    if (await networkInfo.isConnected) {
      try {
        final checkoutUrl = await remoteDataSource.createCheckoutSession(planId);
        return Right(checkoutUrl);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }
  
  // Other method implementations...
}
```
- **Purpose**: Implements the repository interface defined in the domain layer
- **Explanation**: Handles network connectivity checks and error handling, converting exceptions to failures

### Presentation Layer

#### 1. BLoC (`lib/src/subscription/presentation/bloc/`)

**subscription_event.dart**
```dart
abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object> get props => [];
}

class LoadSubscriptionPlans extends SubscriptionEvent {}

class CreateCheckoutSessionEvent extends SubscriptionEvent {
  final String planId;

  const CreateCheckoutSessionEvent(this.planId);

  @override
  List<Object> get props => [planId];
}

// Other events...
```
- **Purpose**: Defines events that can be dispatched to the BLoC
- **Explanation**: Each event represents a user action or system event that can trigger state changes

**subscription_state.dart**
```dart
abstract class SubscriptionState extends Equatable {
  const SubscriptionState();
  
  @override
  List<Object?> get props => [];
}

class SubscriptionInitial extends SubscriptionState {}

class SubscriptionPlansLoaded extends SubscriptionState {
  final List<SubscriptionPlan> plans;
  
  const SubscriptionPlansLoaded(this.plans);
  
  @override
  List<Object> get props => [plans];
}

class CreatingCheckoutSession extends SubscriptionState {}

class CheckoutSessionCreated extends SubscriptionState {
  final String checkoutUrl;
  
  const CheckoutSessionCreated(this.checkoutUrl);
  
  @override
  List<Object> get props => [checkoutUrl];
}

// Other states...
```
- **Purpose**: Defines possible states of the subscription feature
- **Explanation**: Each state represents a snapshot of the UI at a specific point in time

**subscription_bloc.dart**
```dart
class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final GetSubscriptionPlans getSubscriptionPlans;
  final CreateCheckoutSession createCheckoutSession;
  // Other use cases...

  SubscriptionBloc({
    required this.getSubscriptionPlans,
    required this.createCheckoutSession,
    // Other use cases...
  }) : super(SubscriptionInitial()) {
    on<LoadSubscriptionPlans>(_onLoadSubscriptionPlans);
    on<CreateCheckoutSessionEvent>(_onCreateCheckoutSession);
    // Other event handlers...
  }

  Future<void> _onCreateCheckoutSession(
    CreateCheckoutSessionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CreatingCheckoutSession());
    final result = await createCheckoutSession(
      CreateCheckoutSessionParams(planId: event.planId),
    );
    result.fold(
      (failure) =>
          emit(CreateCheckoutSessionError('Failed to create checkout session')),
      (checkoutUrl) => emit(CheckoutSessionCreated(checkoutUrl)),
    );
  }
  
  // Other event handlers...
}
```
- **Purpose**: Manages state transitions based on events
- **Explanation**: Uses use cases to perform business logic and emits new states based on the results

#### 2. UI (`lib/src/subscription/presentation/views/`)

**subscription_plan_page.dart**
```dart
class SubscriptionPlanPage extends StatefulWidget {
  const SubscriptionPlanPage({super.key});

  factory SubscriptionPlanPage.routeBuilder(_, __) {
    return const SubscriptionPlanPage();
  }
  
  @override
  State<SubscriptionPlanPage> createState() => _SubscriptionPlanPageState();
}

class _SubscriptionPlanPageState extends State<SubscriptionPlanPage> {
  @override
  void initState() {
    super.initState();
    context.read<SubscriptionBloc>().add(LoadSubscriptionPlans());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SubscriptionBloc, SubscriptionState>(
      builder: (context, state) {
        if (state is SubscriptionPlansLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is SubscriptionPlansLoaded) {
          return _buildContent(context, state.plans);
        } else {
          return _buildContent(context, []);
        }
      },
    );
  }

  void _handlePremiumPlanSelection() {
    final stripeService = GetIt.instance<StripePaymentService>();
    
    late final StreamSubscription<SubscriptionState> subscription;
    
    subscription = context.read<SubscriptionBloc>().stream.listen((state) {
      if (!mounted) {
        subscription.cancel();
        return;
      }
      
      if (state is CheckoutSessionCreated) {
        stripeService
            .redirectToCheckout(
          checkoutSessionUrl: state.checkoutUrl,
          context: context,
        )
            .catchError((error) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${error.toString()}')),
            );
          }
        });
        subscription.cancel();
      } else if (state is CreateCheckoutSessionError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
        subscription.cancel();
      }
    });
    
    context.read<SubscriptionBloc>().add(
      const CreateCheckoutSessionEvent('premium_monthly'),
    );
  }
  
  // Other methods...
}
```
- **Purpose**: Displays subscription plans and handles user interactions
- **Explanation**: Uses BLoC to manage state and handle events, redirecting to Stripe Checkout when a premium plan is selected

#### 3. Dependency Injection (`lib/src/subscription/di/`)

**subscription_injection_container.dart**
```dart
final sl = GetIt.instance;

Future<void> initSubscriptionDependencies() async {
  // BLoC
  sl.registerFactory(
    () => SubscriptionBloc(
      getSubscriptionPlans: sl(),
      getCurrentSubscription: sl(),
      createSubscription: sl(),
      cancelSubscription: sl(),
      createPaymentIntent: sl(),
      createCheckoutSession: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetSubscriptionPlans(sl()));
  sl.registerLazySingleton(() => CreateCheckoutSession(sl()));
  // Other use cases...

  // Repository
  sl.registerLazySingleton<SubscriptionRepository>(
    () => SubscriptionRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<SubscriptionRemoteDataSource>(
    () => SubscriptionRemoteDataSourceImpl(
      client: sl(),
      baseUrl: 'https://your-backend-api.com/api', // Replace with your actual API URL
    ),
  );

  // Services
  sl.registerLazySingleton(() => StripePaymentService());
}
```
- **Purpose**: Sets up dependency injection for the subscription feature
- **Explanation**: Uses GetIt to register all dependencies and their implementations

## Integration Steps

Follow these steps to integrate Stripe subscriptions into your Flutter app:

### 1. Set Up Stripe Account

1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Get your API keys from the Stripe Dashboard
3. Set up webhook endpoints for handling subscription events

### 2. Backend Setup

1. Create API endpoints for:
   - Creating checkout sessions
   - Handling webhook events
   - Managing subscriptions
   - Retrieving subscription plans

2. Implement webhook handlers for:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### 3. Flutter App Integration

1. Add dependencies to `pubspec.yaml`:
   ```yaml
   dependencies:
     flutter_stripe: ^9.5.0+1
     url_launcher: ^6.2.1
     http: ^1.1.0
   ```

2. Initialize Stripe in `main.dart`:
   ```dart
   void main() async {
     // Initialize Stripe SDK
     Stripe.publishableKey = 'pk_test_your_publishable_key';
     await Stripe.instance.applySettings();
     
     // Other initialization code...
     runApp(MyApp());
   }
   ```

3. Implement the domain layer:
   - Create entities
   - Define repository interfaces
   - Implement use cases

4. Implement the data layer:
   - Create models
   - Implement repository
   - Create remote data source

5. Implement the presentation layer:
   - Create BLoC
   - Implement UI components
   - Set up dependency injection

6. Test the integration:
   - Test subscription plan display
   - Test checkout flow
   - Test webhook handling

## Testing and Troubleshooting

### Testing

1. **Unit Tests**:
   - Test use cases
   - Test repository implementation
   - Test BLoC

2. **Integration Tests**:
   - Test API communication
   - Test Stripe SDK integration

3. **End-to-End Tests**:
   - Test complete subscription flow
   - Test webhook handling

### Common Issues and Solutions

1. **Stripe Checkout Not Opening**:
   - Check if the URL is valid
   - Ensure `url_launcher` is properly configured
   - Verify that the checkout session was created successfully

2. **Webhook Events Not Being Processed**:
   - Check webhook endpoint configuration in Stripe Dashboard
   - Verify that your server is accessible from the internet
   - Check webhook signature verification

3. **Subscription Not Being Created**:
   - Check API response for errors
   - Verify that the payment was successful
   - Check customer and payment method IDs

4. **UI Not Updating After Subscription**:
   - Ensure BLoC events are being dispatched
   - Check that the UI is listening to state changes
   - Verify that the repository is returning the correct data

### Debugging Tips

1. Use Stripe's test mode for development
2. Use Stripe CLI to test webhooks locally
3. Check Stripe Dashboard for event logs
4. Use Stripe's API logs for debugging API calls
5. Implement proper error handling and logging throughout the app

## Conclusion

This implementation provides a clean, maintainable, and scalable solution for integrating Stripe subscriptions into a Flutter app. By following the clean architecture pattern, we've ensured that the business logic is independent of the UI and data sources, making the code more testable and maintainable.

The use of Stripe Checkout simplifies the payment process by leveraging Stripe's pre-built UI components, which are secure, compliant, and well-tested. This approach also reduces the burden of PCI compliance on the app.

By implementing proper error handling and state management, we've ensured that the user experience is smooth and reliable, even in the face of network issues or other errors.
