# Test Timer Implementation Documentation

This document explains the implementation of the countdown timer feature for the test screens in the SkillApp application.

## Overview

The timer functionality allows tests to have a configurable time limit (default: 30 minutes). The timer counts down from the specified duration to zero, and when it reaches zero, the test is automatically submitted. The timer also provides visual feedback by changing color as the remaining time decreases.

## Implementation Approach

We chose to implement the timer as a dedicated StatefulWidget (`TestTimerWidget`) rather than managing it through the TestsBloc. This approach has several advantages:

1. **Separation of Concerns**: The timer logic is contained in a single widget, making it easier to understand and maintain.
2. **Reliability**: Using <PERSON>lut<PERSON>'s built-in state management (setState) ensures the timer display is always in sync with the actual time.
3. **Simplicity**: The timer widget can be easily reused across different screens without complex state management.

## Components

### 1. TestTimerWidget

The `TestTimerWidget` is a StatefulWidget that handles the timer functionality:

```dart
class TestTimerWidget extends StatefulWidget {
  final int durationInMinutes;
  final Function() onTimerComplete;
  final Function(Duration)? onTimerTick;
  
  const TestTimerWidget({
    super.key,
    required this.durationInMinutes,
    required this.onTimerComplete,
    this.onTimerTick,
  });
  
  @override
  TestTimerWidgetState createState() => TestTimerWidgetState();
}
```

#### Key Properties:

- `durationInMinutes`: The total duration of the timer in minutes
- `onTimerComplete`: Callback function that is called when the timer reaches zero
- `onTimerTick`: Optional callback function that is called every second with the remaining time

### 2. TestTimerWidgetState

The state class for the TestTimerWidget handles the actual timer logic:

```dart
class TestTimerWidgetState extends State<TestTimerWidget> {
  Timer? _timer;
  late Duration _remainingTime;
  
  @override
  void initState() {
    super.initState();
    _remainingTime = Duration(minutes: widget.durationInMinutes);
    _startTimer();
  }
  
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime.inSeconds > 0) {
          _remainingTime = _remainingTime - const Duration(seconds: 1);
          if (widget.onTimerTick != null) {
            widget.onTimerTick!(_remainingTime);
          }
        } else {
          _timer?.cancel();
          widget.onTimerComplete();
        }
      });
    });
  }
  
  // Additional methods...
}
```

#### Key Methods:

- `initState()`: Initializes the timer with the specified duration
- `_startTimer()`: Starts a periodic timer that decrements the remaining time every second
- `stopTimer()`: Stops the timer
- `getRemainingTime()`: Returns the current remaining time
- `dispose()`: Ensures the timer is cancelled when the widget is disposed

### 3. Timer Display

The timer is displayed with different formats based on the remaining time:

```dart
@override
Widget build(BuildContext context) {
  final hours = _remainingTime.inHours;
  final minutes = _remainingTime.inMinutes.remainder(60);
  final seconds = _remainingTime.inSeconds.remainder(60);
  
  String timerText;
  Color timerColor = kPrimaryColor;
  
  if (hours > 0) {
    timerText = "${hours.toString().padLeft(2, '0')} hr ${minutes.toString().padLeft(2, '0')} Min";
  } else {
    timerText = "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }
  
  // Change color based on remaining time
  if (minutes < 5) {
    timerColor = Colors.red;
  } else if (minutes < 10) {
    timerColor = Colors.orange;
  }
  
  // UI component...
}
```

#### Display Features:

- For durations > 1 hour: Shows "HH hr MM Min" format
- For durations < 1 hour: Shows "MM:SS" format
- Color changes to orange when less than 10 minutes remain
- Color changes to red when less than 5 minutes remain

## Integration with Test Screen

The timer is integrated into the test screen through the `TestScreenHeader` widget:

```dart
// Timer
TestTimerWidget(
  durationInMinutes: 30, // Get from config
  onTimerComplete: () {
    // Auto-submit the test
    final testsBloc = BlocProvider.of<TestsBloc>(context);
    testsBloc.add(SubmitSectionalTestEvent());
  },
),
```

When the timer completes, it automatically triggers the test submission process by dispatching a `SubmitSectionalTestEvent` to the TestsBloc.

## Time Tracking for Test Submission

When a test is submitted (either manually or automatically when the timer expires), the total time taken is calculated and included in the submission:

```dart
// In TestsBloc._submitSectionalTestEventHandler
// Calculate total time taken
int timeTakenInMinutes = 0;
if (currentState.testStartTime != null) {
  const totalDuration = 30; // Default test duration in minutes
  final elapsedSeconds =
      DateTime.now().difference(currentState.testStartTime!).inSeconds;
  timeTakenInMinutes = elapsedSeconds ~/ 60; // Convert to minutes
  
  // If time taken is more than the total duration, cap it
  if (timeTakenInMinutes > totalDuration) {
    timeTakenInMinutes = totalDuration;
  }
}

// Include time taken in the submission result
emit(SubmitTestSuccessState(
  // Other fields...
  timeTakenInMinutes: timeTakenInMinutes,
));
```

## Benefits of This Implementation

1. **Reliability**: The timer continues to run correctly even if the UI rebuilds or the app is temporarily in the background.
2. **Visual Feedback**: The timer provides clear visual feedback about the remaining time.
3. **Automatic Submission**: Tests are automatically submitted when the time expires.
4. **Time Tracking**: The total time taken is recorded and can be used for analytics or reporting.
5. **Configurability**: The timer duration can be easily configured for different types of tests.

## Future Improvements

1. **Persistence**: Add the ability to persist the timer state if the app is closed and reopened.
2. **Pause/Resume**: Add functionality to pause and resume the timer for certain test scenarios.
3. **Configuration**: Retrieve the timer duration from the test configuration instead of using a hardcoded value.
4. **Accessibility**: Improve the timer display for accessibility, including screen readers.
5. **Sound Alerts**: Add optional sound alerts when the timer is about to expire.
