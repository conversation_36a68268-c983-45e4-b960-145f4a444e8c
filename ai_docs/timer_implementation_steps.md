# Step-by-Step Timer Implementation Guide

This document provides a detailed, step-by-step guide for implementing the countdown timer functionality in the SkillApp test screens.

## Step 1: Create the TestTimerWidget

First, we created a dedicated StatefulWidget to handle the timer functionality:

```dart
// lib/src/test/presentation/widgets/test_timer_widget.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';

class TestTimerWidget extends StatefulWidget {
  final int durationInMinutes;
  final Function() onTimerComplete;
  final Function(Duration)? onTimerTick;
  
  const TestTimerWidget({
    super.key,
    required this.durationInMinutes,
    required this.onTimerComplete,
    this.onTimerTick,
  });
  
  @override
  TestTimerWidgetState createState() => TestTimerWidgetState();
}
```

This widget takes three parameters:
- `durationInMinutes`: The total duration of the timer
- `onTimerComplete`: A callback function that is called when the timer reaches zero
- `onTimerTick`: An optional callback that is called every second with the remaining time

## Step 2: Implement the Timer Logic

Next, we implemented the state class for the TestTimerWidget to handle the actual timer logic:

```dart
class TestTimerWidgetState extends State<TestTimerWidget> {
  Timer? _timer;
  late Duration _remainingTime;
  
  @override
  void initState() {
    super.initState();
    _remainingTime = Duration(minutes: widget.durationInMinutes);
    _startTimer();
  }
  
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime.inSeconds > 0) {
          _remainingTime = _remainingTime - const Duration(seconds: 1);
          if (widget.onTimerTick != null) {
            widget.onTimerTick!(_remainingTime);
          }
        } else {
          _timer?.cancel();
          widget.onTimerComplete();
        }
      });
    });
  }
  
  void stopTimer() {
    _timer?.cancel();
  }
  
  Duration getRemainingTime() {
    return _remainingTime;
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
```

Key aspects of this implementation:
1. We initialize the timer in `initState()` with the specified duration
2. We use `Timer.periodic()` to create a timer that fires every second
3. Each second, we decrement the remaining time and update the UI using `setState()`
4. When the timer reaches zero, we call the `onTimerComplete` callback
5. We ensure the timer is properly cancelled in the `dispose()` method

## Step 3: Create the Timer Display

We then implemented the `build()` method to display the timer with appropriate formatting:

```dart
@override
Widget build(BuildContext context) {
  final hours = _remainingTime.inHours;
  final minutes = _remainingTime.inMinutes.remainder(60);
  final seconds = _remainingTime.inSeconds.remainder(60);
  
  String timerText;
  Color timerColor = kPrimaryColor;
  
  if (hours > 0) {
    timerText = "${hours.toString().padLeft(2, '0')} hr ${minutes.toString().padLeft(2, '0')} Min";
  } else {
    timerText = "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }
  
  // Change color based on remaining time
  if (minutes < 5) {
    timerColor = Colors.red;
  } else if (minutes < 10) {
    timerColor = Colors.orange;
  }
  
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(20),
      border: Border.all(color: Colors.grey.shade200),
    ),
    child: Row(
      children: [
        Icon(
          Icons.timer,
          color: timerColor,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          timerText,
          style: TextStyle(
            fontFamily: 'Poppins',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: timerColor == Colors.red ? Colors.red : Colors.black87,
          ),
        ),
      ],
    ),
  );
}
```

This implementation:
1. Formats the time differently based on whether hours are present
2. Changes the timer color to orange when less than 10 minutes remain
3. Changes the timer color to red when less than 5 minutes remain
4. Displays the timer in a container with a border and icon

## Step 4: Update the TestScreenHeader

We updated the TestScreenHeader to use our new TestTimerWidget:

```dart
// lib/src/test/presentation/widgets/test_screen_header.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';
import 'package:skillapp/src/test/presentation/widgets/test_timer_widget.dart';

// In the build method:
// Timer
TestTimerWidget(
  durationInMinutes: 30, // Get from config
  onTimerComplete: () {
    // Auto-submit the test
    final testsBloc = BlocProvider.of<TestsBloc>(context);
    testsBloc.add(SubmitSectionalTestEvent());
  },
),
```

This integration:
1. Adds the TestTimerWidget to the TestScreenHeader
2. Sets the timer duration to 30 minutes (configurable)
3. Configures the timer to auto-submit the test when it reaches zero

## Step 5: Update the TestsBloc to Track Time Taken

Finally, we updated the TestsBloc to calculate and record the total time taken when a test is submitted:

```dart
// In TestsBloc._submitSectionalTestEventHandler
// Calculate total time taken
int timeTakenInMinutes = 0;
if (currentState.testStartTime != null) {
  const totalDuration = 30; // Default test duration in minutes
  final elapsedSeconds =
      DateTime.now().difference(currentState.testStartTime!).inSeconds;
  timeTakenInMinutes = elapsedSeconds ~/ 60; // Convert to minutes
  
  // If time taken is more than the total duration, cap it
  if (timeTakenInMinutes > totalDuration) {
    timeTakenInMinutes = totalDuration;
  }
}

// Include time taken in the submission result
emit(SubmitTestSuccessState(
  // Other fields...
  timeTakenInMinutes: timeTakenInMinutes,
));
```

This implementation:
1. Calculates the total time taken based on when the test started
2. Caps the time taken at the maximum test duration
3. Includes the time taken in the test submission result

## Testing the Implementation

To test the timer implementation:

1. Run the app and navigate to a test screen
2. Verify that the timer starts automatically and counts down from 30 minutes
3. Check that the timer changes color to orange when less than 10 minutes remain
4. Check that the timer changes color to red when less than 5 minutes remain
5. Verify that the test is automatically submitted when the timer reaches zero
6. Check that the time taken is correctly recorded when the test is submitted

## Troubleshooting Common Issues

If the timer is not working correctly, check the following:

1. **Timer not starting**: Ensure the TestTimerWidget is being properly initialized and the _startTimer() method is being called.
2. **Timer not updating**: Check that setState() is being called correctly in the timer callback.
3. **Timer not stopping**: Verify that the timer is being cancelled in the dispose() method and when the test is submitted.
4. **Time taken not recorded**: Ensure the testStartTime is being set correctly when the test starts.

## Conclusion

This step-by-step implementation provides a reliable, self-contained timer widget that can be easily integrated into the test screens. The approach separates the timer logic from the rest of the application, making it easier to maintain and extend in the future.
