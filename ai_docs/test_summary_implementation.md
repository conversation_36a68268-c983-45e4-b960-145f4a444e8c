# Test Summary Implementation

This document outlines the implementation of the Test Summary feature, which allows users to view detailed information about their past test attempts.

## Overview

The Test Summary page displays comprehensive information about a user's test performance, including:

1. Overall score and percentile
2. Time spent on the test
3. Breakdown of correct, incorrect, partially correct, and unanswered questions
4. Time spent per subject compared to ideal time
5. Comparison with past performance

## Implementation Details

### 1. TestSummaryPage

The `TestSummaryPage` is a new page that displays detailed information about a specific test attempt. It is implemented in `lib/src/past_tests/presentation/views/test_summary_page.dart`.

Key features:
- Tab navigation for different analysis views (Summary, Subject-wise Analysis, etc.)
- Summary cards showing overall performance metrics
- Pie chart showing the breakdown of question responses
- Time breakdown table showing time spent per subject
- Comparison charts showing performance against previous tests

### 2. Navigation

The Test Summary page is accessed by clicking the "View Summary" button in the test history items within the expanded content of the Past Test Table.

Navigation flow:
1. User clicks on a test row in the Past Tests screen to expand it
2. In the expanded content, user clicks "View Summary" on a test history item
3. User is navigated to the Test Summary page for that specific test

### 3. Router Configuration

A new route has been added to the router configuration in `lib/core/services/router.main.dart`:

```dart
GoRoute(
  path: '/test-summary/:testId',
  pageBuilder: (context, state) {
    final String testId = state.pathParameters['testId'] ?? '';
    return NoTransitionPage(
      child: TestSummaryPage(testId: testId),
    );
  },
),
```

This route accepts a `testId` parameter that is used to load the specific test data.

### 4. Dependencies

The implementation uses the following dependencies:
- `fl_chart` for rendering charts (pie chart, line chart, bar chart)
- `go_router` for navigation

## Future Enhancements

1. **Data Integration**: Currently, the page displays static data. Future implementation should fetch real test data based on the `testId`.

2. **Additional Analysis**: Add more detailed analysis tabs as shown in the design:
   - Question Analysis
   - Progress & Insights
   - How You Can Improve

3. **Interactive Charts**: Make charts interactive with tooltips and drill-down capabilities.

4. **PDF Export**: Add functionality to export the summary as a PDF for offline viewing or sharing.

5. **Responsive Design**: Ensure the page works well on different screen sizes, particularly on mobile devices.

## Usage

To navigate to the Test Summary page programmatically:

```dart
context.go('/test-summary/$testId');
```

Where `$testId` is the ID of the test to be displayed.
