# Using setState with BLo<PERSON> Pattern

This document explains why and when to use `setState` in conjunction with the BLoC pattern, specifically in the context of the SkillApp test screens.

## Overview

While the BLoC pattern encourages moving state management out of UI components and into dedicated BLoC classes, there are still valid use cases for local state management with `setState`. Understanding when to use each approach is key to building maintainable and responsive Flutter applications.

## The setState Call in TestScreen

In our TestScreen implementation, we use the BLoC pattern for most state management, but we still have a few `setState` calls:

```dart
// In the onAnswerSelected callback
onAnswerSelected: (answerId) {
  setState(() {
    _selectedAnswerId = answerId;
  });

  // Submit the answer to the BLoC
  context.read<TestsBloc>().add(
        AnswerAQuestionEvent(
          questionId: currentQuestion.questionId,
          selectedAnswer: answerId,
        ),
      );
}
```

## Why This setState Call Is Required

This particular `setState` call updates the `_selectedAnswerId` variable when a user selects an answer option. This is necessary for two reasons:

1. **Immediate Visual Feedback**: When a user selects an answer, the UI needs to immediately reflect this selection by highlighting the chosen option. The `setState` call triggers a rebuild of the widget tree, which updates the visual state of the answer options.

2. **Local UI State Management**: The `_selectedAnswerId` is a piece of UI state that is specific to this screen and doesn't necessarily need to be part of the global application state managed by the TestsBloc.

## Why Not Use the TestsBloc for This?

You might wonder why we don't manage the selected answer entirely through the TestsBloc. There are a few reasons:

1. **Separation of Concerns**: The TestsBloc is responsible for managing the test data and navigation between questions, while the UI component (TestScreen) is responsible for managing the visual state of the current question.

2. **Responsiveness**: Using `setState` for immediate UI updates provides a more responsive user experience. If we were to dispatch an event to the TestsBloc and wait for the state to update, there might be a slight delay in the UI update.

3. **Optimization**: Not every piece of UI state needs to go through the global state management system. For ephemeral (temporary) UI state like the currently selected answer on the screen, local state management with `setState` is often more efficient.

## The Complete Flow

Here's the complete flow of what happens when a user selects an answer:

1. User taps on an answer option
2. The `onAnswerSelected` callback is triggered
3. `setState` is called to update `_selectedAnswerId`, which causes the UI to rebuild and show the selected answer
4. An `AnswerAQuestionEvent` is dispatched to the TestsBloc to record the answer in the global state
5. When the user navigates to another question and comes back, the selected answer is retrieved from the TestsBloc state

## Best Practices for Combining setState and BLoC

### When to Use setState

Use `setState` for:

1. **Ephemeral UI State**: State that is specific to a single widget and doesn't need to be shared with other parts of the application.
2. **Immediate Visual Feedback**: When you need to update the UI immediately in response to user interaction.
3. **Simple UI Animations**: For simple animations or transitions that don't affect the application's business logic.

### When to Use BLoC

Use BLoC for:

1. **Application State**: State that needs to be shared across multiple widgets or screens.
2. **Business Logic**: Complex business logic that should be separated from the UI.
3. **Persistent State**: State that needs to persist across widget rebuilds or screen navigations.
4. **State That Affects Multiple UI Components**: When a state change in one part of the UI should trigger updates in other parts.

## Syncing Local and Global State

In our implementation, we sync the local state (`_selectedAnswerId`) with the global state (TestsBloc) in two ways:

1. **From Local to Global**: When a user selects an answer, we update the local state with `setState` and dispatch an event to update the global state.

```dart
setState(() {
  _selectedAnswerId = answerId;
});

context.read<TestsBloc>().add(
      AnswerAQuestionEvent(
        questionId: currentQuestion.questionId,
        selectedAnswer: answerId,
      ),
    );
```

2. **From Global to Local**: When the TestScreen builds or when a new question is loaded, we update the local state based on the global state.

```dart
// Update the selected answer ID if available
if (currentQuestion.selectedAnswer.isNotEmpty) {
  _selectedAnswerId = currentQuestion.selectedAnswer;
}
```

## Conclusion

Using `setState` in conjunction with the BLoC pattern is not an anti-pattern when done correctly. It's about choosing the right tool for the job:

- Use BLoC for managing application state and business logic
- Use `setState` for managing ephemeral UI state and providing immediate visual feedback

By understanding when to use each approach, you can build Flutter applications that are both maintainable and responsive.
