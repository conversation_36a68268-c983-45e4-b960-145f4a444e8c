# Test Navigation Implementation

This document explains the implementation of the test navigation functionality in the SkillApp application, specifically how the Next/Previous buttons in the TestFooter are connected to the TestsBloc to fetch and display questions.

## Overview

The test navigation system allows users to move between questions in a test using the Next and Previous buttons in the TestFooter. When a user clicks these buttons, the application dispatches events to the TestsBloc, which then updates the state with the new question. This approach ensures that the UI stays in sync with the application state and provides a more maintainable architecture.

## Implementation Details

### 1. TestFooter Widget

The `TestFooter` widget provides the navigation buttons:

```dart
class TestFooter extends StatelessWidget {
  final VoidCallback? onBack;
  final VoidCallback? onFlag;
  final VoidCallback? onNext;
  final bool isLastQuestion;

  const TestFooter({
    super.key,
    this.onBack,
    this.onFlag,
    this.onNext,
    this.isLastQuestion = false,
  });

  // Widget implementation...
}
```

The widget takes callback functions for the Back, Flag, and Next buttons, which are called when the respective buttons are pressed.

### 2. TestsBloc Events

The TestsBloc defines events for navigating between questions:

```dart
class FetchNextQuestionEvent extends TestsEvent {
  final int currentOrder;
  const FetchNextQuestionEvent({
    required this.currentOrder,
  });
  @override
  List<Object> get props => [currentOrder];
}

class FetchPreviousQuestionEvent extends TestsEvent {
  final int currentOrder;
  const FetchPreviousQuestionEvent({
    required this.currentOrder,
  });
  @override
  List<Object> get props => [currentOrder];
}
```

These events take the current question order as a parameter, which is used to determine the next or previous question.

### 3. TestsBloc Event Handlers

The TestsBloc handles these events and updates the state with the new question:

```dart
FutureOr<void> _fetchNextQuestionEventHandler(
    FetchNextQuestionEvent event, Emitter<TestsStateInitial> emit) async {
  TestsDataState currentState = state as TestsDataState;

  if (event.currentOrder <
      currentState.testWithQuestionsData.testQuestionEntrySet.length) {
    final nextQuestion = currentState
        .testWithQuestionsData.testQuestionEntrySet
        .firstWhere((element) => element.order == event.currentOrder + 1);
    emit(TestsDataState(
        testAttemptId: currentState.testAttemptId,
        test: currentState.test,
        status: currentState.status,
        testWithQuestionsData: currentState.testWithQuestionsData,
        currentQuestion: nextQuestion,
        flaggedQuestions: currentState.flaggedQuestions,
        isFirstQuestion: nextQuestion.order == 1,
        isLastQuestion: nextQuestion.order ==
            currentState.testWithQuestionsData.testQuestionEntrySet.length));
  } else {
    emit(AtLastQuestionState(
      testDataState: currentState,
    ));
  }
}

FutureOr<void> _fetchPreviousQuestionEventHandler(
    FetchPreviousQuestionEvent event, Emitter<TestsStateInitial> emit) async {
  TestsDataState currentState = state as TestsDataState;

  if (event.currentOrder > 1) {
    final prevQuestion = currentState
        .testWithQuestionsData.testQuestionEntrySet
        .firstWhere((element) => element.order == event.currentOrder - 1);
    emit(TestsDataState(
      testAttemptId: currentState.testAttemptId,
      test: currentState.test,
      status: currentState.status,
      testWithQuestionsData: currentState.testWithQuestionsData,
      currentQuestion: prevQuestion,
      flaggedQuestions: currentState.flaggedQuestions,
      isLastQuestion: prevQuestion.order ==
          currentState.testWithQuestionsData.testQuestionEntrySet.length,
      isFirstQuestion: prevQuestion.order == 1,
    ));
  } else {
    emit(AtFirstQuestionState(
      testDataState: currentState,
    ));
  }
}
```

These handlers find the next or previous question based on the current question's order and emit a new state with the updated question.

### 4. TestScreen Integration

The TestScreen connects the TestFooter callbacks to the TestsBloc events:

```dart
TestFooter(
  onBack: () {
    if (_currentQuestionIndex > 0) {
      // Dispatch FetchPreviousQuestionEvent to get the previous question
      context.read<TestsBloc>().add(
            FetchPreviousQuestionEvent(
              currentOrder: state.currentQuestion.order,
            ),
          );
      
      // Update the local index to match
      setState(() {
        _currentQuestionIndex--;
        _selectedAnswerId = null;
      });
    } else {
      // Show dialog to confirm exit
      _showExitConfirmationDialog(context);
    }
  },
  onFlag: () {
    // Dispatch FlagAQuestionEvent to flag the current question
    context.read<TestsBloc>().add(
          FlagAQuestionEvent(
            questionId: state.currentQuestion.questionId,
          ),
        );
  },
  onNext: () {
    if (_currentQuestionIndex < totalQuestions - 1) {
      // Dispatch FetchNextQuestionEvent to get the next question
      context.read<TestsBloc>().add(
            FetchNextQuestionEvent(
              currentOrder: state.currentQuestion.order,
            ),
          );
      
      // Update the local index to match
      setState(() {
        _currentQuestionIndex++;
        _selectedAnswerId = null;
      });
    } else {
      // Show dialog to confirm submission
      _showSubmitConfirmationDialog(context);
    }
  },
  isLastQuestion: _currentQuestionIndex == totalQuestions - 1,
),
```

When the user clicks the Next or Back button, the TestScreen dispatches the appropriate event to the TestsBloc and updates its local state to match.

### 5. Synchronizing UI with State

To ensure the UI stays in sync with the TestsBloc state, the TestScreen updates its local index when the state changes:

```dart
Widget _buildTestContent(BuildContext context, TestsDataState state) {
  // Get the current question directly from the state
  final currentQuestion = state.currentQuestion;
  final questionData = currentQuestion.questionData;

  // Update the local index to match the current question from the state
  // Only update if the index doesn't match to avoid infinite loops
  if (_currentQuestionIndex != currentQuestion.order - 1) {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      if (mounted) {
        setState(() {
          _currentQuestionIndex = currentQuestion.order - 1; // order is 1-based, index is 0-based
        });
      }
    });
  }

  // Build the test content...
}
```

This ensures that the UI always reflects the current question in the TestsBloc state, even if the state is updated from elsewhere.

## Benefits of This Approach

1. **Separation of Concerns**: The UI components (TestScreen, TestFooter) are responsible for handling user interactions, while the TestsBloc is responsible for managing the application state.

2. **Maintainability**: Changes to the navigation logic can be made in one place (the TestsBloc) without affecting the UI components.

3. **Testability**: The navigation logic can be tested independently of the UI components.

4. **Consistency**: The UI always reflects the current state of the application, even if the state is updated from multiple places.

## Future Improvements

1. **Caching**: Implement caching of question data to improve performance when navigating between questions.

2. **Prefetching**: Prefetch the next question when the user is on the current question to reduce loading time.

3. **Navigation History**: Implement a navigation history to allow users to jump to previously visited questions.

4. **Keyboard Navigation**: Add support for keyboard navigation (e.g., using arrow keys to navigate between questions).

5. **Accessibility**: Improve accessibility by adding screen reader support and keyboard shortcuts.
