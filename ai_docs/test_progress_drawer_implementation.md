# Test Progress Drawer Implementation

This document outlines the implementation of the Test Progress Panel as a drawer in the TestScreen, similar to the WebHintAndSolutionDrawer in the Practice screen.

## Overview

The Test Progress Panel is implemented as an end drawer in the Scaffold that slides in from the right side of the screen when the user clicks the "View progress summary" button in the TestScreenHeader. The drawer shows a grid of questions with their status (answered, unanswered, current, flagged) and allows the user to navigate to a specific question.

## Implementation Details

### 1. TestProgressDrawer Widget

We created a new `TestProgressDrawer` widget that displays the test progress summary:

```dart
class TestProgressDrawer extends StatelessWidget {
  const TestProgressDrawer({
    super.key,
    required this.testProgressSummaryList,
    required this.onQuestionSelected,
    required GlobalKey<ScaffoldState> scaffoldKey,
  }) : _scaffoldKey = scaffoldKey;

  final List<TestProgressSummary> testProgressSummaryList;
  final Function(String) onQuestionSelected;
  final GlobalKey<ScaffoldState> _scaffoldKey;

  @override
  Widget build(BuildContext context) {
    // Implementation...
  }
}
```

The drawer has three main sections:

1. **Header**: Title and close button
2. **Content**: Status legend and grid of questions with status indicators
3. **Footer**: Back button to close the drawer

### 2. TestScreen Integration

We updated the TestScreen to use a Scaffold key and add the drawer:

```dart
class _TestScreenState extends State<TestScreen> {
  // Scaffold key to control the drawer
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // ...

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      endDrawer: BlocBuilder<TestsBloc, TestsStateInitial>(
        builder: (context, state) {
          if (state is TestProgressState) {
            return TestProgressDrawer(
              testProgressSummaryList: state.testProgressSummaryList,
              onQuestionSelected: (questionId) {
                // Navigation logic...
                _scaffoldKey.currentState!.closeEndDrawer();
              },
              scaffoldKey: _scaffoldKey,
            );
          }
          return Container(); // Empty drawer when not in progress state
        },
      ),
      body: BlocConsumer<TestsBloc, TestsStateInitial>(
        listener: (context, state) {
          if (state is TestProgressState) {
            // Open the drawer after a short delay
            Future.delayed(const Duration(milliseconds: 100), () {
              _scaffoldKey.currentState?.openEndDrawer();
            });
          }
        },
        // Rest of the implementation...
      ),
    );
  }
}
```

### 3. TestScreenHeader Integration

The TestScreenHeader dispatches the ViewTestProgressEvent when the fullscreen toggle button is clicked:

```dart
TestScreenHeader(
  currentQuestion: currentQuestionOrder,
  totalQuestions: totalQuestions,
  onFullscreenToggle: () {
    // Dispatch ViewTestProgressEvent to show progress summary
    context.read<TestsBloc>().add(ViewTestProgressEvent());
  },
),
```

### 4. Navigation Between Questions

When a question is selected in the drawer:

1. The drawer dispatches a ViewSelectedQuestionEvent with the selected question ID
2. The TestsBloc handles the navigation to the selected question
3. The drawer is closed

```dart
onQuestionSelected: (questionId) {
  // Use ViewSelectedQuestionEvent to directly navigate to the selected question
  context.read<TestsBloc>().add(
        ViewSelectedQuestionEvent(
          questionId: questionId,
        ),
      );

  // Close the drawer
  _scaffoldKey.currentState!.closeEndDrawer();
},
```

This approach is more direct and efficient than the previous implementation, which used FetchPreviousQuestionEvent or FetchNextQuestionEvent based on the relative position of the questions. Using ViewSelectedQuestionEvent allows for direct navigation to any question regardless of its position relative to the current question.

## Benefits of This Approach

1. **Consistent UX**: Uses the same drawer pattern as other parts of the app (like the WebHintAndSolutionDrawer in the Practice screen)
2. **Simpler Implementation**: Leverages Flutter's built-in drawer functionality
3. **Better Separation**: Clearly separates the test content from the progress panel
4. **Easier Maintenance**: Follows established patterns in the codebase
5. **Responsive**: Can easily adjust width based on screen size

## Comparison with Previous Implementation

The previous implementation used a custom overlay with a SlideInPanel that was part of the main widget tree. This new implementation:

1. **Uses Standard Flutter Widgets**: Uses the built-in Scaffold.endDrawer instead of a custom overlay
2. **Simplifies State Management**: No need to track whether the panel is open in the widget state
3. **Improves Code Organization**: Separates the drawer logic into its own widget
4. **Follows Existing Patterns**: Matches the approach used in the Practice screen

## Future Improvements

1. **Filtering**: Add options to filter questions by status (e.g., show only unanswered questions)
2. **Keyboard Navigation**: Add keyboard shortcuts for navigating between questions
3. **Responsive Design**: Improve the drawer's responsiveness on different screen sizes
4. **Animation**: Add smooth transitions when navigating between questions
