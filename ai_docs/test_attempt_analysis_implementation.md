# Test Attempt Analysis & Result Features Implementation

This document outlines the implementation of three related test analysis features:

1. **Test Attempt Analysis**: Detailed module-wise analysis of test performance based on question types
2. **Test Result Summary**: Overall test performance summary with timing and result breakdown
3. **Test Questions with Answer Status**: Complete question-wise breakdown showing individual answer status

## Latest Update: Enhanced Test Result Screen with Analysis Table

**Date**: Current
**Enhancement**: Added `FetchTestAttemptAnalysisEvent` to test result screen with analysis table display

### Changes Made:

1. **Triple Event Integration**: Enhanced test result screen to call three events simultaneously:

   - `FetchTestQuestionsWithAnswerStatusEvent`
   - `FetchTestResultSummaryEvent`
   - `FetchTestAttemptAnalysisEvent`

2. **Analysis Table Implementation**: Added question type analysis table with:

   - **Question Type** column (module names)
   - **No. of Questions** column (count per module)
   - **Accuracy** column (percentage with color coding)
   - Excluded: Avg. Time, Cohort Accuracy, Cohort Avg. Time (as requested)

3. **Enhanced State Management**:

   - Added `TestAttemptAnalysis? testAttemptAnalysis` to local state
   - Updated `_checkIfDataComplete()` to wait for all three data sources
   - Added error handling for analysis fetch failures

4. **UI Enhancements**:
   - Added `_buildTestAnalysisSection()` method for analysis table
   - Added `_buildPerformanceCards()` method for Total Percentage and Time Spent cards
   - Added `_buildTestResultsBreakdown()` method for test results breakdown with circular chart
   - Added `CircularChartPainter` custom painter for visual progress representation
   - Color-coded accuracy indicators (green ≥80%, orange ≥60%, red <60%)
   - Card-style layout for key metrics (Total Percentage: 75.6%, Time Spent: 48 min, 56s)
   - Test results breakdown with colored indicators and circular progress chart
   - Consistent styling with existing components
   - Responsive table layout with proper spacing

### File Modified:

- `lib/src/test/presentation/views/test_result_screen.dart`

### Key Features:

- **Comprehensive Data Display**: Shows summary, analysis, and question details
- **Smart Loading**: Waits for all three API calls to complete
- **Error Resilience**: Handles partial failures gracefully
- **Visual Consistency**: Matches existing design patterns
- **Performance Optimized**: Efficient state management with minimal rebuilds

## Overview

### Test Attempt Analysis

The Test Attempt Analysis feature allows users to view comprehensive performance analytics for a specific test attempt, including:

1. **Module-wise breakdown** of questions based on the 'module' field from question details
2. **Statistical analysis** showing correct, incorrect, and unattempted questions per module
3. **Accuracy percentages** for each question type/module
4. **Visual representation** similar to the provided screenshot with color-coded accuracy indicators
5. **Overall test performance** summary

### Test Result Summary

The Test Result Summary feature provides a high-level overview of test performance, including:

1. **Total percentage** achieved in the test
2. **Time spent** on the test (formatted as "48 min, 56s")
3. **Result breakdown** with color-coded indicators:
   - Green circle: Correct answers
   - Orange circle: Incorrect answers
   - Grey circle: Unanswered questions
4. **Test metadata** (name, subject, total questions)

### Test Questions with Answer Status

The Test Questions with Answer Status feature provides a detailed question-by-question breakdown, including:

1. **Complete question list** showing all questions from the test attempt in order
2. **Answer status indicators** for each question:
   - Green: Correct answers
   - Red: Incorrect answers
   - Grey: Unanswered questions
3. **Question details** including:
   - Question order and short description
   - Module/topic classification
   - Selected answer vs correct answer comparison
   - Visual status badges
4. **Summary statistics** (total correct, incorrect, unanswered counts)
5. **Color-coded question cards** for quick visual assessment

## Core Functionality

### Input

- **Test Attempt ID**: Unique identifier for a specific test attempt

### Output

- **Test Information**: Test name, subject, overall accuracy
- **Module Analysis**: For each question module/type:
  - Question Type (module name)
  - Total number of questions
  - Number of correct answers
  - Number of incorrect answers
  - Number of unattempted questions
  - Accuracy percentage

### Data Processing

1. Fetches test attempt status and question attempt data
2. Retrieves test structure to identify all questions
3. Fetches individual question details and parses the JSON `data` field to extract module information
4. Groups questions by module and calculates statistics
5. Handles unattempted questions by comparing attempted vs total questions

### Important Data Structure Note

- **Question documents in Firestore have a `data` field containing JSON**
- **The `module` field is INSIDE the `data` JSON, not at the document root level**
- **The implementation correctly parses `json.decode(questionDoc['data'])` to access the module**

## Architecture Implementation

### 1. Domain Layer

#### New Entities

**QuestionModuleAnalysis**

```dart
class QuestionModuleAnalysis extends Equatable {
  final String questionType; // Module name from question details
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unattemptedQuestions;
  final double accuracy; // Percentage of correct answers
}
```

**TestAttemptAnalysis**

```dart
class TestAttemptAnalysis extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final List<QuestionModuleAnalysis> moduleAnalysis;
  final int totalQuestions;
  final int totalCorrectAnswers;
  final int totalIncorrectAnswers;
  final int totalUnattemptedQuestions;
  final double overallAccuracy;
}
```

**TestResultSummary**

```dart
class TestResultSummary extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final double totalPercentage;
  final String timeSpent; // Format: "48 min, 56s"
  final int correctAnswers;
  final int incorrectAnswers;
  final int unansweredAnswers;
  final int totalQuestions;
  final DateTime? startTime;
  final DateTime? endTime;
}
```

**QuestionAnswerStatus (Enum)**

```dart
enum QuestionAnswerStatus {
  correct,
  incorrect,
  unanswered,
}
```

**QuestionWithAnswerStatus**

```dart
class QuestionWithAnswerStatus extends Equatable {
  final String questionId;
  final int questionOrder;
  final String shortDescription;
  final String selectedAnswer;
  final String correctAnswer;
  final QuestionAnswerStatus answerStatus;
  final String module;
}
```

**TestQuestionsWithAnswerStatus**

```dart
class TestQuestionsWithAnswerStatus extends Equatable {
  final String testAttemptId;
  final String testId;
  final String testName;
  final String subject;
  final List<QuestionWithAnswerStatus> questions;
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final int unansweredQuestions;
}
```

#### New Use Cases

- **FetchTestAttemptAnalysis**: Takes test attempt ID as parameter and returns detailed analysis data
- **FetchTestResultSummary**: Takes test attempt ID as parameter and returns summary data
- **FetchTestQuestionsWithAnswerStatus**: Takes test attempt ID as parameter and returns question-wise status data

#### Updated Repository Interface

- Added `fetchTestAttemptAnalysis(String testAttemptId)` method
- Added `fetchTestResultSummary(String testAttemptId)` method
- Added `fetchTestQuestionsWithAnswerStatus(String testAttemptId)` method

### 2. Data Layer

#### Enhanced Remote Datasource

**TestsDataRemoteDatasourceImpl.fetchTestAttemptAnalysis():**

1. **Fetch Test Attempt Status**: Gets basic test information
2. **Fetch Question Attempts**: Retrieves all attempted questions with results
3. **Fetch Test Structure**: Gets complete question list from test configuration
4. **Fetch Question Details**: Retrieves module information for each question by:
   - Fetching question document from `subjects/{subject}/questions/{questionId}`
   - Extracting the `data` field (which contains JSON)
   - Parsing the JSON with `json.decode()` to access the `module` field
   - Handling JSON parsing errors gracefully
5. **Group and Calculate**: Groups questions by module and computes statistics
6. **Handle Unattempted**: Identifies and includes unattempted questions

**TestsDataRemoteDatasourceImpl.fetchTestResultSummary():**

1. **Fetch Test Attempt Status**: Gets basic test information and statistics
2. **Fetch Test Data**: Gets test structure for total question count
3. **Fetch Question Attempts**: Retrieves attempted questions for detailed counts
4. **Extract Timing**: Gets start_time and end_time from test attempt document
5. **Calculate Duration**: Computes time spent and formats as "X min, Ys"
6. **Calculate Statistics**: Computes correct, incorrect, and unanswered counts
7. **Format Results**: Creates summary with percentage and formatted time

**TestsDataRemoteDatasourceImpl.fetchTestQuestionsWithAnswerStatus():**

1. **Fetch Test Attempt Status**: Gets basic test information
2. **Fetch Test Data**: Gets complete test structure from tests_master collection
3. **Fetch Question Attempts**: Retrieves all attempted questions with results
4. **Create Attempt Map**: Maps question IDs to attempt data for quick lookup
5. **Fetch Question Details**: For each question in the test:
   - Retrieves question document from `subjects/{subject}/questions/{questionId}`
   - Extracts short description, correct answer, and module from JSON data
   - Handles JSON parsing errors gracefully with fallback values
6. **Determine Answer Status**: For each question:
   - Checks if question was attempted using the attempt map
   - Sets status as correct/incorrect based on `isCorrect` field
   - Marks unattempted questions as `unanswered`
7. **Sort and Calculate**: Sorts questions by order and computes statistics
8. **Error Handling**: Provides fallback data for questions that can't be fetched

**Key Technical Detail**: The module information is stored as:

```
Firestore Document Structure:
subjects/{subject}/questions/{questionId}
├── data: "{\"module\": \"Comprehension\", \"question\": {...}, ...}"  // JSON string
└── other_fields: ...

Code Implementation:
String dataJson = questionDoc.data()!['data'];
Map<String, dynamic> dataMap = json.decode(dataJson);
String module = dataMap['module'] ?? 'Unknown';
```

#### Updated Repository Implementation

- Added method with proper error handling and profile ID injection

### 3. Presentation Layer

#### New BLoC Components

**Event**

```dart
class FetchTestAttemptAnalysisEvent extends TestsEvent {
  final String testAttemptId;
}
```

**States**

```dart
class FetchTestAttemptAnalysisState extends TestsStateInitial {
  final TestAttemptAnalysis testAttemptAnalysis;
}

class FetchTestAttemptAnalysisErrorState extends TestsStateInitial {
  final String message;
}
```

**Additional Events**

```dart
class FetchTestResultSummaryEvent extends TestsEvent {
  final String testAttemptId;
}

class FetchTestQuestionsWithAnswerStatusEvent extends TestsEvent {
  final String testAttemptId;
}
```

**Additional States**

```dart
class FetchTestResultSummaryState extends TestsStateInitial {
  final TestResultSummary testResultSummary;
}

class FetchTestResultSummaryErrorState extends TestsStateInitial {
  final String message;
}

class FetchTestQuestionsWithAnswerStatusState extends TestsStateInitial {
  final TestQuestionsWithAnswerStatus testQuestionsWithAnswerStatus;
}

class FetchTestQuestionsWithAnswerStatusErrorState extends TestsStateInitial {
  final String message;
}
```

**Event Handlers**

- `_fetchTestAttemptAnalysisEventHandler`: Processes analysis requests
- `_fetchTestResultSummaryEventHandler`: Processes summary requests
- `_fetchTestQuestionsWithAnswerStatusEventHandler`: Processes question status requests

#### Updated Dependency Injection

- Registered `FetchTestAttemptAnalysis` use case
- Registered `FetchTestResultSummary` use case
- Registered `FetchTestQuestionsWithAnswerStatus` use case
- Updated `TestsBloc` constructor with new dependencies

### 4. Testing UI

#### Enhanced Testing Interface

- **New Test Buttons**:

  - "FetchTestAttemptAnalysisEvent"
  - "FetchTestResultSummaryEvent"
  - "FetchTestQuestionsWithAnswerStatusEvent"

- **Analysis Display**: Shows module breakdown with:

  - Test name and subject
  - Overall accuracy
  - Module-wise statistics
  - Color-coded accuracy indicators:
    - Green: ≥80% accuracy
    - Orange: ≥60% accuracy
    - Red: <60% accuracy

- **Summary Display**: Shows high-level overview with:

  - Total percentage and time spent
  - Color-coded result breakdown
  - Test metadata

- **Questions Display**: Shows detailed question-by-question breakdown with:
  - Individual question cards with color-coded backgrounds
  - Question order, description, and module
  - Answer status badges (Correct/Incorrect/Unanswered)
  - Selected vs correct answer comparison

## Usage Examples

### Triggering Analysis

```dart
// Trigger detailed analysis for a specific test attempt
context.read<TestsBloc>().add(
  FetchTestAttemptAnalysisEvent(testAttemptId: "test_attempt_123")
);

// Trigger summary for a specific test attempt
context.read<TestsBloc>().add(
  FetchTestResultSummaryEvent(testAttemptId: "test_attempt_123")
);

// Trigger question-wise status for a specific test attempt
context.read<TestsBloc>().add(
  FetchTestQuestionsWithAnswerStatusEvent(testAttemptId: "test_attempt_123")
);
```

### Handling Results

```dart
BlocBuilder<TestsBloc, TestsStateInitial>(
  builder: (context, state) {
    // Handle detailed analysis
    if (state is FetchTestAttemptAnalysisState) {
      final analysis = state.testAttemptAnalysis;

      return Column(
        children: [
          Text('Test: ${analysis.testName}'),
          Text('Overall Accuracy: ${analysis.overallAccuracy.toStringAsFixed(1)}%'),

          // Module breakdown
          ...analysis.moduleAnalysis.map((module) =>
            ModuleAnalysisCard(
              questionType: module.questionType,
              totalQuestions: module.totalQuestions,
              accuracy: module.accuracy,
              // ... other properties
            )
          ),
        ],
      );
    }

    // Handle summary
    else if (state is FetchTestResultSummaryState) {
      final summary = state.testResultSummary;

      return Column(
        children: [
          Text('${summary.totalPercentage.toStringAsFixed(1)}%'),
          Text('Time: ${summary.timeSpent}'),
          Text('Correct: ${summary.correctAnswers}'),
          Text('Incorrect: ${summary.incorrectAnswers}'),
          Text('Unanswered: ${summary.unansweredAnswers}'),
        ],
      );
    }

    // Handle question-wise status
    else if (state is FetchTestQuestionsWithAnswerStatusState) {
      final questionsData = state.testQuestionsWithAnswerStatus;

      return Column(
        children: [
          Text('Test: ${questionsData.testName}'),
          Text('Total Questions: ${questionsData.totalQuestions}'),

          // Question list
          ...questionsData.questions.map((question) =>
            QuestionStatusCard(
              questionOrder: question.questionOrder,
              shortDescription: question.shortDescription,
              answerStatus: question.answerStatus,
              selectedAnswer: question.selectedAnswer,
              correctAnswer: question.correctAnswer,
              module: question.module,
            )
          ),
        ],
      );
    }

    // Handle errors
    else if (state is FetchTestAttemptAnalysisErrorState) {
      return Text('Analysis Error: ${state.message}');
    } else if (state is FetchTestResultSummaryErrorState) {
      return Text('Summary Error: ${state.message}');
    } else if (state is FetchTestQuestionsWithAnswerStatusErrorState) {
      return Text('Questions Error: ${state.message}');
    }

    return CircularProgressIndicator();
  },
)
```

## Data Flow

### Test Attempt Analysis Flow

1. **User Input**: Test attempt ID provided
2. **Event Dispatch**: `FetchTestAttemptAnalysisEvent` triggered
3. **Use Case Execution**: `FetchTestAttemptAnalysis.call(testAttemptId)`
4. **Repository Call**: `TestsDataRepo.fetchTestAttemptAnalysis(testAttemptId)`
5. **Data Source Processing**: Complex analysis logic in remote datasource
6. **State Emission**: Success or error state emitted
7. **UI Update**: Analysis displayed with module breakdown

### Test Result Summary Flow

1. **User Input**: Test attempt ID provided
2. **Event Dispatch**: `FetchTestResultSummaryEvent` triggered
3. **Use Case Execution**: `FetchTestResultSummary.call(testAttemptId)`
4. **Repository Call**: `TestsDataRepo.fetchTestResultSummary(testAttemptId)`
5. **Data Source Processing**: Summary calculation and time formatting
6. **State Emission**: Success or error state emitted
7. **UI Update**: Summary displayed with percentage and timing

### Test Questions with Answer Status Flow

1. **User Input**: Test attempt ID provided
2. **Event Dispatch**: `FetchTestQuestionsWithAnswerStatusEvent` triggered
3. **Use Case Execution**: `FetchTestQuestionsWithAnswerStatus.call(testAttemptId)`
4. **Repository Call**: `TestsDataRepo.fetchTestQuestionsWithAnswerStatus(testAttemptId)`
5. **Data Source Processing**: Question-wise status determination and sorting
6. **State Emission**: Success or error state emitted
7. **UI Update**: Questions displayed with individual status cards

## Key Features

### Module Detection

- Automatically extracts module information from question details collection
- Handles missing or unknown modules gracefully
- Groups questions by module for statistical analysis

### Statistical Accuracy

- Proper calculation of accuracy percentages
- Handles edge cases (division by zero, no questions)
- Includes unattempted questions in total counts

### Time Calculation & Formatting

- Accurate time calculation from start_time and end_time
- Human-readable time formatting ("48 min, 56s")
- Handles missing or invalid time data gracefully

### Question Status Determination

- Accurate mapping of question attempts to answer status
- Comprehensive question details extraction
- Proper handling of unanswered questions
- Color-coded visual indicators for quick assessment

### Error Handling

- Graceful handling of missing question data
- Fallback to "Unknown" module for questions without module information
- Comprehensive error messages for debugging
- Robust JSON parsing with fallback values

### Performance Optimization

- Efficient grouping algorithms
- Minimal database queries through batch processing
- Sorted results for consistent display
- Smart caching of question details during processing

### UI/UX Features

- Color-coded backgrounds and status indicators
- Professional card-based layouts
- Responsive design for different screen sizes
- Consistent styling across all three features

## Integration Points

### With Existing Test System

- Leverages existing test attempt and question data structures
- Compatible with current test flow and data models
- Reuses existing repository patterns and error handling

### With UI Components

- Designed for easy integration with existing BLoC patterns
- Provides structured data for various UI representations
- Supports both detailed and summary views

## Future Enhancements

### Potential Improvements

1. **Caching**: Cache analysis results for frequently accessed attempts
2. **Comparison**: Compare performance across multiple attempts
3. **Trends**: Track improvement over time for specific modules
4. **Recommendations**: Suggest areas for improvement based on weak modules
5. **Export**: Allow exporting analysis data for external review
6. **Question Review**: Deep-link to specific questions for detailed review
7. **Performance Insights**: Advanced analytics on time spent per question
8. **Study Plans**: Generate personalized study recommendations
9. **Progress Tracking**: Visual charts showing improvement over time
10. **Collaborative Features**: Share results with teachers or mentors

### Scalability Considerations

- Optimize for large numbers of questions per test
- Consider pagination for tests with many modules
- Implement efficient caching strategies for repeated analysis requests

## Testing

### Unit Tests

- Test entity creation and validation
- Test use case logic and error handling
- Test repository implementation

### Integration Tests

- Test complete flow from event to state emission
- Test with various test attempt scenarios
- Test error conditions and edge cases

### UI Tests

- Test analysis display components
- Test user interaction flows
- Test responsive design for different screen sizes

## Summary

This implementation provides a comprehensive foundation for test attempt analysis with three complementary features:

### Feature Comparison

| Feature                               | Purpose                           | Data Granularity            | Use Case                                    |
| ------------------------------------- | --------------------------------- | --------------------------- | ------------------------------------------- |
| **Test Attempt Analysis**             | Module-wise performance breakdown | Grouped by question modules | Understanding strengths/weaknesses by topic |
| **Test Result Summary**               | High-level overview with timing   | Overall statistics          | Quick performance assessment                |
| **Test Questions with Answer Status** | Question-by-question breakdown    | Individual question level   | Detailed review and learning                |

### Technical Architecture

- **Clean Architecture**: Follows domain-driven design principles
- **BLoC Pattern**: Consistent state management across all features
- **Error Handling**: Comprehensive error handling and fallback mechanisms
- **Performance**: Optimized data fetching and processing
- **Extensibility**: Easy to add new analysis features

### Production Readiness

- **Type Safety**: Strong typing with proper entity definitions
- **Error Recovery**: Graceful handling of edge cases and missing data
- **User Experience**: Professional UI with intuitive visual indicators
- **Maintainability**: Well-documented code with clear separation of concerns

This implementation provides a comprehensive foundation for test attempt analysis that can be extended and customized based on specific requirements and user feedback.

## Integration with Main Content Area

### **Router Configuration Changes**

- **Moved test result routes** from standalone to `ShellRoute` for web
- **Added routes**: `/test-result/:testAttemptId` and `/test-result/:testAttemptId/question/:questionIndex`
- **Maintained BlocProvider** setup with singleton `TestsBloc` instance

### **UI Structure Changes**

- **Removed Scaffold wrapper** from `TestResultScreen` to work within shell
- **Removed custom header** (`TestScreenHeader`) - now uses common `WebHeaderWidget`
- **Updated layout** to follow `PastTestsScreen` pattern with `SingleChildScrollView` and 40px padding
- **Simplified structure** to work within `WebMainContentAreaWidget`

### **Navigation & Breadcrumbs**

- **Added breadcrumb support** for test result pages:
  - Test Results: Home → Past Tests → Test Results
- **Question Details**: Now handled as standalone screens (outside shell route)
- **Dynamic header titles**: "Test Results" for main content area
- **Route-based updates** using `_updateHeaderForCurrentRoute()` method

### **Files Modified for Integration**

1. `lib/core/services/router.main.dart` - Moved test result route to ShellRoute, question detail route to standalone
2. `lib/src/test/presentation/views/test_result_screen.dart` - Removed Scaffold, updated layout
3. `lib/core/common/widgets/web/web_main_content_area.dart` - Added breadcrumb logic for test results
4. `lib/src/test/presentation/views/question_detail_screen.dart` - Maintained as standalone screen with own Scaffold

### **Question Detail Screen Architecture**

- **Standalone Screen**: Outside the shell route, similar to `test_screen.dart`
- **Own Scaffold**: Has its own `Scaffold` with `TestScreenHeader`
- **No Navigation Rail**: Full-screen experience without side navigation
- **Custom Header**: Uses `TestScreenHeader` with fullscreen toggle for question navigation
- **Question Navigation Drawer**: Side panel for navigating between questions
- **Data Passing**: Receives question list data via route `extra` parameter for optimal performance

### **Global Scrolling Implementation**

- **Moved SingleChildScrollView** to `WebMainContentAreaWidget` level for full-width scrolling
- **Enhanced UX**: Page scrolling now works from anywhere on the screen, not just within content constraints
- **Centered Content**: Content is centered with max-width constraints while maintaining full-width scrolling
- **Individual Page Simplification**: Pages now use simple `Padding` instead of managing their own scroll views

### **Benefits of Integration**

- **Consistent UI**: Test results now use the same header and navigation as other pages
- **Better UX**: Proper breadcrumb navigation for easy navigation back to past tests
- **Full-Width Scrolling**: Scrolling works across the entire screen width, even on large displays
- **Maintainability**: Follows the same architectural pattern as other main content pages
- **Responsive Design**: Works seamlessly within the constrained main content area
- **Improved Accessibility**: Better scrolling behavior for users with different input devices
