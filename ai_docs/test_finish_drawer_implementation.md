# Test Finish Drawer Implementation

This document outlines the implementation of the Test Finish Drawer, which provides a clean and modern confirmation step before submitting a test.

## Overview

The Test Finish Drawer is a modal drawer that slides in from the right side of the screen when the user clicks the "Finish" button in the TestFooter. It displays information about the test, asks for confirmation, and provides options to go back or submit the test.

## Implementation Details

### 1. TestFinishDrawer Widget

The `TestFinishDrawer` widget is a custom drawer that displays:

- A header with "Submit Test" title and close button
- Test information (test name and subject)
- Confirmation message and checklist questions
- Action buttons ("No Go Back" and "Yes Submit")

```dart
class TestFinishDrawer extends StatelessWidget {
  final String testName;
  final String testSubject;
  final VoidCallback onClose;
  final VoidCallback onGoBack;
  final VoidCallback onSubmit;

  const TestFinishDrawer({
    Key? key,
    required this.testName,
    required this.testSubject,
    required this.onClose,
    required this.onGoBack,
    required this.onSubmit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: MediaQuery.of(context).size.width * 0.85,
      child: Safe<PERSON>rea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and close button
            // Test information
            // Confirmation message and checklist
            // Action buttons
          ],
        ),
      ),
    );
  }
}
```

### 2. TestsBloc Integration

To support the Test Finish Drawer, we added:

- A new event: `ViewTestFinishSummaryEvent`
- A new state: `TestFinishSummaryState`
- A handler method: `_viewTestFinishSummaryEventHandler`

```dart
// Event
class ViewTestFinishSummaryEvent extends TestsEvent {}

// State
class TestFinishSummaryState extends TestsDataState {
  const TestFinishSummaryState({
    required super.test,
    required super.status,
    required super.testWithQuestionsData,
    required super.currentQuestion,
    required super.testAttemptId,
    required super.isLastQuestion,
    required super.isFirstQuestion,
    required super.flaggedQuestions,
  });
}

// Handler
FutureOr<void> _viewTestFinishSummaryEventHandler(
    ViewTestFinishSummaryEvent event, Emitter<TestsStateInitial> emit) async {
  TestsDataState currentState = state as TestsDataState;

  emit(
    TestFinishSummaryState(
      currentQuestion: currentState.currentQuestion,
      testAttemptId: currentState.testAttemptId,
      test: currentState.test,
      status: currentState.status,
      testWithQuestionsData: currentState.testWithQuestionsData,
      isLastQuestion: currentState.isLastQuestion,
      isFirstQuestion: currentState.isFirstQuestion,
      flaggedQuestions: currentState.flaggedQuestions,
    ),
  );
}
```

### 3. TestScreen Integration

The `TestScreen` was updated to:

- Handle the `TestFinishSummaryState` in its BlocBuilder
- Show the `TestFinishDrawer` when in `TestFinishSummaryState`
- Update the `_showSubmitConfirmationDialog` method to use the drawer instead of a dialog

```dart
// In the BlocBuilder
else if (state is TestFinishSummaryState) {
  return TestFinishDrawer(
    testName: state.test.description,
    testSubject: state.test.subject,
    onClose: () {
      _scaffoldKey.currentState!.closeEndDrawer();
    },
    onGoBack: () {
      _scaffoldKey.currentState!.closeEndDrawer();
    },
    onSubmit: () {
      _scaffoldKey.currentState!.closeEndDrawer();

      // Submit the test
      context.read<TestsBloc>().add(SubmitSectionalTestEvent());

      // Navigate to home
      context.go('/home');
    },
  );
}

// Updated _showSubmitConfirmationDialog method
void _showSubmitConfirmationDialog(BuildContext context, TestsBloc testBloc) {
  // Dispatch event to show finish drawer
  testBloc.add(ViewTestFinishSummaryEvent());

  // Open the drawer
  Future.delayed(const Duration(milliseconds: 100), () {
    _scaffoldKey.currentState?.openEndDrawer();
  });
}
```

## Answer Selection Improvements

As part of this implementation, we made two significant improvements to the answer selection handling:

### 1. BLoC-Based State Management

We improved the state management by:

1. Removing local state management for selected answers
2. Relying entirely on the BLoC state for tracking selected answers
3. Updating the UI based on the current question's selected answer from the BLoC state

```dart
// Before
selectedAnswerId: _selectedAnswerId,
onAnswerSelected: (answerId) {
  setState(() {
    _selectedAnswerId = answerId;
  });

  // Submit the answer
  context.read<TestsBloc>().add(
        AnswerAQuestionEvent(
          questionId: currentQuestion.questionId,
          selectedAnswer: answerId,
        ),
      );
},

// After
selectedAnswerId: currentQuestion.selectedAnswer.isNotEmpty
    ? currentQuestion.selectedAnswer
    : null,
onAnswerSelected: (answerId) {
  // Submit the answer directly to the bloc without local state update
  context.read<TestsBloc>().add(
        AnswerAQuestionEvent(
          questionId: currentQuestion.questionId,
          selectedAnswer: answerId,
        ),
      );
},
```

### 2. Optimized UI Updates with Dedicated Widget

We created a dedicated `AnswerOptionsWidget` that only rebuilds when the selected answer changes or when navigating between questions:

```dart
class AnswerOptionsWidget extends StatelessWidget {
  final List<AnswerOption> answerOptions;
  final Function(String) onAnswerSelected;

  // ...

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TestsBloc, TestsStateInitial>(
      buildWhen: (previous, current) {
        // Rebuild when:
        // 1. State type changes
        if (previous.runtimeType != current.runtimeType) {
          return true;
        }

        // 2. Current question changes or its selected answer changes
        if (previous is TestsDataState && current is TestsDataState) {
          return previous.currentQuestion.questionId != current.currentQuestion.questionId ||
                 previous.currentQuestion.selectedAnswer != current.currentQuestion.selectedAnswer;
        }

        return true;
      },
      builder: (context, state) {
        if (state is TestsDataState) {
          final currentQuestion = state.currentQuestion;

          // Get the selected answer directly from the current question
          final selectedAnswerId = currentQuestion.selectedAnswer.isNotEmpty
              ? currentQuestion.selectedAnswer
              : null;

          // Build answer options based on current state
          // ...
        }
      },
    );
  }
}
```

This approach ensures that:

1. The UI always reflects the current state from the BLoC
2. There's a single source of truth for the application state
3. The UI updates correctly when navigating between questions
4. Only the answer options section rebuilds when the selected answer changes, not the entire screen
5. Performance is improved by minimizing unnecessary rebuilds

## Benefits

1. **Improved User Experience**: The drawer provides a clear confirmation step before submitting a test
2. **Better State Management**: Relying on BLoC state ensures consistent UI updates
3. **Modern Design**: The drawer follows modern design principles with clean layout and clear actions
4. **Consistent Behavior**: The answer selection behavior is now consistent across all navigation methods

## Future Improvements

1. **Accessibility**: Add screen reader support and keyboard navigation
2. **Animation**: Add smooth animations for drawer entry and exit
3. **Responsive Design**: Improve the drawer layout for different screen sizes
4. **Offline Support**: Handle test submission in offline mode
5. **Analytics**: Add analytics to track user interactions with the drawer
