{"project_info": {"project_number": "363604231725", "project_id": "skillapp-eea7c", "storage_bucket": "skillapp-eea7c.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:363604231725:android:2f74e4828b0814e69e1aa4", "android_client_info": {"package_name": "au.com.selectiveninja"}}, "oauth_client": [{"client_id": "363604231725-20tssh958ar3j6u824v9ije0l7mjag5n.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "au.com.selectiveninja", "certificate_hash": "8fe27c4c80917e92a01e4c9f7fef22ae4708c11c"}}, {"client_id": "363604231725-bhidkl8crnfp7ka7juh95un6e152rht1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "au.com.selectiveninja", "certificate_hash": "fb485adfea99dfed4dcdc6503721f498524cffad"}}, {"client_id": "363604231725-mqupkjsn5q12408vn4nkjas7hdff2uo6.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "au.com.selectiveninja", "certificate_hash": "c9a4d41df1ece9de491500959b560733d1b24f16"}}, {"client_id": "363604231725-62u0ru88v5eu2vrckbcpmgh2btvidmqm.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB1n-VJLjjaxR6l4DEc9uxqTOjUzf14hus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "363604231725-62u0ru88v5eu2vrckbcpmgh2btvidmqm.apps.googleusercontent.com", "client_type": 3}, {"client_id": "363604231725-4clrm0vfg26q5d9ndmkc7dv7ubksfqhf.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "au.com.selectiveninja"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:363604231725:android:625a6ddf0c63a4b49e1aa4", "android_client_info": {"package_name": "com.example.skillapp"}}, "oauth_client": [{"client_id": "363604231725-22htfh1qs24k3ikauklb718j0t54qai1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.skillapp", "certificate_hash": "c9a4d41df1ece9de491500959b560733d1b24f16"}}, {"client_id": "363604231725-62u0ru88v5eu2vrckbcpmgh2btvidmqm.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB1n-VJLjjaxR6l4DEc9uxqTOjUzf14hus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "363604231725-62u0ru88v5eu2vrckbcpmgh2btvidmqm.apps.googleusercontent.com", "client_type": 3}, {"client_id": "363604231725-4clrm0vfg26q5d9ndmkc7dv7ubksfqhf.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "au.com.selectiveninja"}}]}}}], "configuration_version": "1"}