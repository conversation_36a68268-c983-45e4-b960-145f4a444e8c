# Test Module Documentation

This document outlines the complete implementation of the test module in the SkillApp project, including the flow, components, and key features.

## Test Flow

The test module follows this sequence:

1. **Dashboard** → User selects a test from the dashboard
2. **Test Instructions Screen** → User reads test instructions
3. **Test Confirmation Screen** → User confirms they've read the instructions
4. **Test Screen** → User takes the test
5. **Results Screen** → User views their results

## Components

### 1. Test Layout Widget

A reusable widget that provides a consistent header and layout for all test-related screens.

```dart
TestLayoutWidget(
  automaticallyImplyLeading: false,
  actions: [/* Optional actions */],
  child: /* Screen-specific content */,
)
```

### 2. Test Instructions Screen

Displays the test rules, duration, and number of questions.

- Shows test title and subject
- Displays detailed instructions
- Provides "Back" and "Continue" buttons
- Handles navigation to the confirmation screen

### 3. Test Confirmation Screen

Confirms that the user has read and understood the instructions.

- Shows test title and subject
- Asks "Are You Sure You Have Finished Reading The Instructions?"
- Warns that instructions won't be available during the test
- Provides "No" and "Yes" buttons
- Handles navigation to the actual test

### 4. Test Screen

The main screen where users take the test.

- Displays questions and answer options
- Shows navigation controls (Previous/Next)
- Tracks user progress
- Handles answer selection and submission
- Provides a submit button on the last question

## Implementation Details

### State Management

The test module uses BLoC pattern for state management:

- **TestsBloc**: Manages the test state, including:
  - Fetching test data
  - Navigating between questions
  - Recording answers
  - Submitting the test

### Key Events

- **FetchNewSectionalTestEvent**: Fetches a new test
- **MarkTestAsStartedEvent**: Marks the test as started
- **FetchTestDataEvent**: Loads test questions
- **FetchNextQuestionEvent**: Navigates to the next question
- **FetchPreviousQuestionEvent**: Navigates to the previous question
- **AnswerAQuestionEvent**: Records a user's answer
- **SubmitSectionalTestEvent**: Submits the completed test

### Error Handling

- Handles null state.extra parameters when navigating back
- Provides graceful fallbacks for error states
- Shows appropriate loading indicators during async operations

## Navigation

The test module uses GoRouter for navigation:

```dart
// Navigate to test instructions
context.push('/test-instructions', extra: {
  'test': test,
  'testAttemptId': testAttemptId,
  'subject': subject,
  'questionCount': questionCount,
  'durationMinutes': durationMinutes,
});

// Navigate to test confirmation
context.push('/test-confirmation', extra: {
  'test': test,
  'testAttemptId': testAttemptId,
});

// Navigate to test screen
context.go('/test/${test.testId}', extra: {
  'test': test,
  'testAttemptId': testAttemptId,
});
```

## Test Configuration

The test module uses a configuration system to manage test parameters:

```dart
class TestConfig {
  final TestTypes type;        // sectional, fullMocktest, etc.
  final String subject;        // maths, reading, etc.
  final TestConfigParameters parameters;
}

class TestConfigParameters {
  final int maxAllowedTime;    // Time in seconds
}
```

Test duration is dynamically fetched from the `TestsConfigCache` which loads configurations from Firebase:

```dart
// Get test configuration from cache
final testsConfigCache = di.sl<TestsConfigCache>();

// Get the test configuration for the sectional test type and subject
final testConfig = testsConfigCache.getTestConfig(
  TestTypes.sectional,
  'maths'
);

// Convert maxAllowedTime from seconds to minutes
int durationMinutes = testConfig.parameters.maxAllowedTime ~/ 60;
```

## Data Structures

### Test

```dart
class Test {
  final String testId;
  final String subject;
  final String description;
  final int questionCount;
  final int durationMinutes;
  // ...
}
```

### TestQuestionEntry

```dart
class TestQuestionEntry {
  final String questionId;
  final QuestionAndAnswers questionData;
  final bool attempted;
  final String selectedAnswer;
  final int attemptCount;
  final DateTime lastAttempted;
  final int order;
  // ...
}
```

### QuestionAndAnswers

Complex structure that contains:

- Question text and images
- Answer options
- Correct answer
- Explanation

## Fixed Issues

1. **Navigation Error**: Fixed error when hitting back button by handling null state.extra parameters
2. **TestsBloc Error**: Fixed "Cannot add new events after calling close" error by using BlocProvider.value
3. **Question Display**: Fixed test_screen.dart to properly handle complex question data structures

## Future Improvements

1. Add timer functionality to track remaining time
2. Implement question flagging for review
3. Add progress indicator to show completion percentage
4. Enhance results screen with detailed analytics
5. Add offline support for test-taking
