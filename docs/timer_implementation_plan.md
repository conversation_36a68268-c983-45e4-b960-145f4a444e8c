# Timer Implementation Plan for Test Screen

## Overview
This document outlines the implementation plan for adding a functional countdown timer to the test screen. The timer will start when questions are loaded, count down from a configurable duration, and track the total time taken when the test is submitted.

## Requirements

1. **Countdown Timer**: The timer should count down from a configurable starting time (e.g., 30 minutes) to zero
2. **Timer Start**: The timer should start automatically when questions are loaded
3. **Timer Stop**: The timer should stop when the test is submitted
4. **Time Tracking**: The total time taken should be recorded and available upon test submission
5. **Configurable Duration**: The starting time should be configurable through TestsConfigCache

## Implementation Steps

### 1. Update TestsBloc State
- Add timer-related fields to the TestsDataState:
  - `testStartTime`: DateTime when the test started
  - `remainingTime`: Duration of time remaining
  - `isTimerRunning`: Boolean to track if timer is active

### 2. Add Timer Logic to TestsBloc
- Implement a Timer using <PERSON><PERSON>'s Timer.periodic
- Add methods to start, pause, and stop the timer
- Update the state every second with the new remaining time
- Auto-submit the test when the timer reaches zero

### 3. Configure Test Duration
- Retrieve the test duration from TestsConfigCache
- Use this duration to initialize the timer when starting a test

### 4. Update TestScreenHeader
- Connect to the TestsBloc to display the current timer state
- Format the time display (hours, minutes, seconds)
- Add visual feedback (color changes) as time decreases

### 5. Handle Test Submission
- Stop the timer when the test is submitted
- Calculate and store the total time taken
- Include this information in the test result

### 6. Testing
- Test with different durations
- Test timer behavior when navigating between questions
- Test auto-submission when time expires

## Technical Details

### State Management
```dart
class TestsDataState extends TestsStateInitial {
  final TestWithQuestionsData testWithQuestionsData;
  final DateTime? testStartTime;
  final Duration? remainingTime;
  final bool isTimerRunning;
  
  // Constructor and copyWith method
}
```

### Timer Logic
```dart
// In TestsBloc
Timer? _timer;

void _startTimer() {
  _timer?.cancel();
  _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
    // Update remaining time
    // Check if time's up
    // Update state
  });
}
```

### Time Display
```dart
// Format time for display
String formatTime(Duration duration) {
  final hours = duration.inHours;
  final minutes = duration.inMinutes.remainder(60);
  final seconds = duration.inSeconds.remainder(60);
  
  if (hours > 0) {
    return "${hours.toString().padLeft(2, '0')} hr ${minutes.toString().padLeft(2, '0')} min";
  } else {
    return "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }
}
```

## UI Mockup
```
┌─────────────────────────────────────────────┐
│                                             │
│  ┌─────────────────┐  Question 1 of 10      │
│  │ 00 hr 29 min    │                        │
│  └─────────────────┘                        │
│                                             │
└─────────────────────────────────────────────┘
```

## Timeline
1. Update TestsBloc with timer state - 1 hour
2. Implement timer logic - 2 hours
3. Update UI components - 1 hour
4. Testing and refinement - 2 hours

Total estimated time: 6 hours
