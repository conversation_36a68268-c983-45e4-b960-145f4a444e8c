# Test Question Fetching Flow Analysis

This document analyzes how questions are fetched based on questionId in the test module of the SkillApp application.

## Data Flow Overview

The question fetching process involves multiple layers following the clean architecture pattern:

1. **Presentation Layer**: TestsBloc dispatches events
2. **Domain Layer**: Use cases coordinate the fetching process
3. **Data Layer**: Repository and data sources handle the actual data fetching
4. **External**: Firestore database stores the test and question data

## Detailed Flow

### 1. Initial Test Fetching

1. **TestsBloc** dispatches a `FetchNewSectionalTestEvent` with a subject (e.g., "maths")
2. This triggers `_fetchNewSectionalTestEventHandler` in the TestsBloc
3. The handler calls the `FetchNewSectionalTest` use case
4. The use case calls `TestsDataRepoImpl.fetchNewSectionalTest(subject)`
5. The repo implementation calls `TestsDataRemoteDatasourceImpl.fetchNewSectionalTest(level, subject)`
6. The remote datasource queries Firestore to get a test ID:
   ```dart
   QuerySnapshot testIdsSnapshot = await _firestore
       .collection('tests_mapping/sectional/levels/$level/subjects/$subject/test_ids')
       .get();
   ```
7. It returns a basic `Test` object with testId, description, and subject, but with an empty questionWithOrderList

### 2. Test Data Fetching

1. After getting the basic test info, TestsBloc dispatches a `FetchTestDataEvent` with the testId
2. This triggers `_fetchTestDataEventHandler` in the TestsBloc
3. The handler calls the `FetchTestsData` use case
4. The use case calls `TestsDataRepoImpl.fetchTestsData(testId)`
5. The repo implementation first calls `TestsDataRemoteDatasourceImpl.fetchTestsData(testId)` to get the test with question IDs:
   ```dart
   // First, get the test metadata
   DocumentSnapshot testSnapshot = await _firestore.collection('tests_master').doc(testId).get();
   
   // Then, get the question IDs associated with this test
   QuerySnapshot testQuestionSnapshot = await _firestore
       .collection('tests_master/$testId/question_ids')
       .get();
   ```
6. This returns a `Test` object with the `questionWithOrderList` populated with question IDs and their order

### 3. Question Data Fetching

1. For each question ID in the `questionWithOrderList`, the repo calls `_questionRepo.fetchSingleQuestionData(questionId, testId, subject)`
2. This calls `QuestionRepoImpl.fetchSingleQuestionData(questionId, bundleId, subjectId)`
3. The question repo calls `QuestionRemoteDataSourceImpl.fetchSingleQuestionData(questionId, bundleId, subject)`
4. The question remote datasource queries Firestore to get the actual question data:
   ```dart
   DocumentSnapshot<Map<String, dynamic>> doc = await _firestore
       .collection('subjects/$subject/questions')
       .doc(questionId)
       .get();
   ```
5. It returns a `QuestionModel` with the question data
6. The question repo converts this to a `QuestionAndAnswers` entity
7. Back in `TestsDataRepoImpl`, all the question data is collected and organized into a `SectionalTestData` object
8. This object contains the test metadata and a set of `TestQuestionEntry` objects, each containing a question and its metadata

### 4. Question Order and Sorting

1. The order of questions is determined by the `order` field in the `questionWithOrderList`
2. The `TestsDataRepoImpl` sorts the questions based on this order:
   ```dart
   testQuestionEntryList.sort((a, b) => a.order.compareTo(b.order));
   ```

## Firestore Collections Structure

Based on the code, the Firestore collections are structured as follows:

1. `tests_master` - Contains test metadata
   - `tests_master/{testId}` - Individual test document
   - `tests_master/{testId}/question_ids` - Subcollection with question IDs and their order

2. `subjects` - Contains subject-specific data
   - `subjects/{subject}/questions` - Contains the actual question data
   - `subjects/{subject}/questions/{questionId}` - Individual question document

3. `tests_mapping` - Maps tests to levels and subjects
   - `tests_mapping/sectional/levels/{level}/subjects/{subject}/test_ids` - Maps subjects to test IDs

4. `tests_user_attempts` - Tracks user attempts at tests
   - `tests_user_attempts/{profileId}/test_types/sectional/test_attempts` - Test attempt metadata
   - `tests_user_attempts/{profileId}/test_types/sectional/test_attempts/{testAttemptId}/question_ids` - User's answers to questions

## Key Classes and Their Roles

1. **TestsBloc**: Manages the state of the test and handles user interactions
2. **FetchNewSectionalTest**: Use case for fetching a new test
3. **FetchTestsData**: Use case for fetching test data with questions
4. **TestsDataRepo**: Repository interface for test data operations
5. **TestsDataRepoImpl**: Implementation of the repository interface
6. **TestsDataRemoteDatasource**: Data source interface for remote test data
7. **TestsDataRemoteDatasourceImpl**: Implementation of the data source interface
8. **QuestionRepo**: Repository interface for question data operations
9. **QuestionRepoImpl**: Implementation of the question repository interface

## Data Models

1. **Test**: Basic test metadata with question IDs and their order
2. **SectionalTestData**: Complete test data with questions
3. **TestQuestionEntry**: A question with its metadata in the context of a test
4. **QuestionWithOrder**: A question ID with its order in a test
5. **QuestionAndAnswers**: The actual question data
6. **QuestionModel**: The raw question data from Firestore

## Summary

The question fetching flow involves multiple steps:

1. Fetch a test ID based on subject and level
2. Fetch the test metadata and question IDs
3. For each question ID, fetch the actual question data from the subject-specific collection
4. Organize the questions based on their order
5. Return a complete test data object with all questions

This architecture separates test metadata from question data, allowing questions to be reused across different tests while maintaining test-specific ordering and organization.
