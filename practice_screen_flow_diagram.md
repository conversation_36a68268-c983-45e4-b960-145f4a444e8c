```mermaid
flowchart TD
    A[PracticeScreen] --> B{AdaptiveLayout}
    B -->|Mobile| C[PracticeScreenMobile]
    B -->|Tablet| D[PracticeScreenTablet]
    B -->|Desktop| E[PracticeScreenDesktop]
    
    %% Mobile Layout
    C --> CM1[BlocListener<PracticeBloc>]
    CM2 --> CM4[SingleChildScrollView]
    CM1 --> CM2[Scaffold]
    CM2 --> CM3[AppBarTemplate]
    CM2 --> CM4[SingleChildScrollView]
    CM4 --> CM5[QuestionAnswerWidget]
    CM5 --> CM6[QuestionBlocBuilder]
    CM5 --> CM7[AnswerOptionsBlocBuilder]
    CM2 --> CM8[BlocBuilder<PracticeBloc>]
    CM8 -->|Success/Failure/LastAttemptDone| CM9[HintAndSolutionWidget]
    CM8 -->|Other States| CM10[Container - Empty]
    CM2 --> CM11[PraticeScreenFooter]
    CM11 --> CM12[FlagPopupWidget]
    CM11 --> CM13[Submit/Retry Button]
    
    %% Desktop Layout
    E --> EM1[Scaffold]
    EM1 --> EM2[SingleChildScrollView]
    EM2 --> EM3[Row]
    EM3 --> EM4[Expanded - Main Content]
    EM4 --> EM5[WebQuestionBlocBuilder]
    EM4 --> EM6[WebAnswerOptionsBlocBuilder]
    EM3 --> EM7[Expanded - Side Panel]
    EM7 --> EM8[MoreActionsWidget]
    EM7 --> EM9[WebDailyTestBanner]
    EM1 --> EM10[endDrawer - WebHintAndSolutionDrawer]
    EM1 --> EM11[BlocBuilder<PracticeBloc>]
    EM11 -->|Success/Failure/LastAttemptDone| EM12[WebHintAndSolutionWidget]
    EM11 -->|Other States| EM13[Container - Empty]
    
    %% BlocBuilders and their logic
    CM6 --> BL1{State Type?}
    BL1 -->|Loading| BL2[QuestionLoadingWidget]
    BL1 -->|Not Error| BL3[QuestionWidget]
    BL1 -->|Error| BL4[QuestionErrorWidget]
    
    CM7 --> BL5{State Type?}
    BL5 -->|Loading| BL6[AnswerOptionsLoadingWidget]
    BL5 -->|Not Error| BL7[AnswerOptionsWidget]
    BL5 -->|Error| BL8[Container - Empty]
    
    EM5 --> BL9{State Type?}
    BL9 -->|Loading| BL10[WebQuestionLoadingWidget]
    BL9 -->|Not Error| BL11[WebQuestionWidget]
    BL9 -->|Error| BL12[WebQuestionErrorWidget]
    
    EM6 --> BL13{State Type?}
    BL13 -->|Loading| BL14[WebAnswerOptionsLoadingWidget]
    BL13 -->|Not Error| BL15[WebAnswerOptionsWidget]
    BL13 -->|Error| BL16[Container - Empty]
    
    %% PracticeBloc Events and State Flow
    subgraph "PracticeBloc State Management"
        PB1[PracticeBloc] --> PB2[Events]
        PB2 --> PB3[LoadQuestion]
        PB2 --> PB4[SubmitQuestionEvent]
        PB2 --> PB5[ResetQuestionForRetryEvent]
        PB2 --> PB6[SelectAnOption]
        PB2 --> PB7[NextButtonClicked]
        PB2 --> PB8[FlagUnflagQuestion]
        PB2 --> PB9[FetchSingleQuestionEvent]
        
        PB1 --> PB10[States]
        PB10 --> PB11[PracticeState]
        PB10 --> PB12[PracticeStateWithFlaggedStatus]
        PB10 --> PB13[PracticeQuestionLoadingState]
        PB10 --> PB14[PracticeSuccessState]
        PB10 --> PB15[PracticeCountIncrementedState]
        
        PB11 --> PB16[Properties]
        PB16 --> PB17[questionAndAnswer]
        PB16 --> PB18[lastAttemptStatus]
        PB16 --> PB19[retryCount]
        PB16 --> PB20[optionSelected]
        PB16 --> PB21[flagValue]
    end
    
    %% User Interaction Flow
    subgraph "User Interaction Flow"
        UI1[User Loads Practice Screen] --> UI2[LoadQuestion Event]
        UI2 --> UI3[Display Question and Options]
        UI3 --> UI4[User Selects an Option]
        UI4 --> UI5[SelectAnOption Event]
        UI5 --> UI6[User Clicks Submit]
        UI6 --> UI7[SubmitQuestionEvent]
        UI7 --> UI8{Correct Answer?}
        UI8 -->|Yes| UI9[Show Success State]
        UI8 -->|No| UI10{Max Retries?}
        UI10 -->|Yes| UI11[Show LastAttemptDone State]
        UI10 -->|No| UI12[Show Failure State]
        UI12 --> UI13[User Clicks Retry]
        UI13 --> UI14[ResetQuestionForRetryEvent]
        UI14 --> UI4
        UI9 --> UI15[User Clicks Next]
        UI15 --> UI16[NextButtonClicked Event]
        UI16 --> UI2
    end
    
    %% Component Relationships
    BL3 -.-> PB17
    BL7 -.-> PB17
    BL11 -.-> PB17
    BL15 -.-> PB17
    CM9 -.-> PB18
    EM12 -.-> PB18
    CM13 -.-> PB18
```
