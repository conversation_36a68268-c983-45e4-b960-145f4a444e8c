{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "16"}, "main": "index.js", "dependencies": {"firebase-admin": "^11.5.0", "firebase-functions": "^4.9.0"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.0.0"}, "private": true}