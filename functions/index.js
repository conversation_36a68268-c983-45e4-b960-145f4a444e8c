const functions = require("firebase-functions");

// // Create and deploy your first functions
// // https://firebase.google.com/docs/functions/get-started
//
// exports.helloWorld = functions.https.onRequest((request, response) => {
//   functions.logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });
// The Cloud Functions for Firebase SDK to create Cloud Functions and triggers.
const {logger} = require("firebase-functions");
const {onRequest} = require("firebase-functions/v2/https");
const {onDocumentCreated} = require("firebase-functions/v2/firestore");

// The Firebase Admin SDK to access Firestore.
const {initializeApp} = require("firebase-admin/app");
const {getFirestore} = require("firebase-admin/firestore");

initializeApp();

// Take the text parameter passed to this HTTP endpoint and insert it into
// Firestore under the path /messages/:documentId/original
exports.addmessage = onRequest(async (req, res) => {
  logger.log("onRequest", req.query.text);
    // Grab the text parameter.
    const original = req.query.text;
    // Push the new message into Firestore using the Firebase Admin SDK.
    const writeResult = await getFirestore()
        .collection("messages")
        .add({original: original});
    // Send back a message that we've successfully written the message
    res.json({result: `Message with ID: ${writeResult.id} added.`});
  });

  // Listens for new messages added to /messages/:documentId/original
// and saves an uppercased version of the message
// to /messages/:documentId/uppercase
exports.makeuppercase = onDocumentCreated("/messages/{documentId}", (event) => {
    // Grab the current value of what was written to Firestore.
    const original = event.data.data().original;
  
    // Access the parameter `{documentId}` with `event.params`
    logger.log("Uppercasing", event.params.documentId, original);
  
    const uppercase = original.toUpperCase();
  
    // You must return a Promise when performing
    // asynchronous tasks inside a function
    // such as writing to Firestore.
    // Setting an 'uppercase' field in Firestore document returns a Promise.
    return event.data.ref.set({uppercase}, {merge: true});
  });

  //const functions = require('firebase-functions');


exports.calculateXpPoints = functions.pubsub.schedule('every 24 hours').onRun(async (context) => {
  await processxpforallusers();
});

async function processxpforallusers() {
  console.log('This will be executed daily to calculate XP points!');
  //Write code to fetch all data from users collection and then iterate over each to fetch the profiles collection and then calculate the XP points for each user.
  try {
    const usersSnapshot = await getFirestore().collection('users').get();

    console.info('calculateXpPoints::Printing usersSnapshot:', usersSnapshot);
    const usersData = usersSnapshot.docs.map(doc => doc.data());

    for (const user of usersData) {
      console.info('calculateXpPoints::Printing user1:', user);
      const profilesSnapshot = await getFirestore().collection('users').doc(user.id).collection('profiles').get();
      console.info('calculateXpPoints::Printing user:', user);
      const profilesData = profilesSnapshot.docs.map(doc => doc.data());

      let totalXP = 0;
      for (const profile of profilesData) {
        console.info('calculateXpPoints::Printing profile:', profile);
        calculateXP(profile.id, profile['last_xp_check_document_id']??'');
      }
    }
  } catch (error) {
    console.error('Error calculating XP:', error);
  }
}

  async function calculateXP(profileId, lastCheckDocumentId) {
    try {
      //pass profileId in collection fetch below
      var attemptedQuestionsSnapshot;
      if(lastCheckDocumentId) {
        attemptedQuestionsSnapshot = await getFirestore().collection('attempted_questions/${profileId}/questions').orderBy('timeStamp')
        .startAfter(lastCheckDocumentId).get();
        
      } else {
        attemptedQuestionsSnapshot = await getFirestore().collection('attempted_questions/${profileId}/questions').orderBy('timeStamp').get();
      }
      const attemptedQuestionsData = attemptedQuestionsSnapshot.docs.map(doc => doc.data());

      for(const question of attemptedQuestionsData) {
        console.info('calculateXpPoints::calculateXP::question:', question);
      }

    } catch (error) {
      console.error('Error calculateXP XP:', error);
    }
  }

  exports.runxpcalculationtest = onRequest(async (req, res) => {
    console.error('inside runxpcalculationtest');
    await processxpforallusers();
    console.error('inside runxpcalculationtest');
    });
