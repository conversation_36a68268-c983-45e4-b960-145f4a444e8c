import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';

void main() {
  testWidgets("Practice app bar widget test", (widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp( 
        home: AppBarTemplate(appBarTitle: 'Maths', actionType: 'NA',)));

    final titleFinder = find.text('Maths');

    expect(titleFinder, findsOneWidget);
  });
}
