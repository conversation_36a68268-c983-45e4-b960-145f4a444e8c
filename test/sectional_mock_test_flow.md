# Sectional Mock Test Flow Guide

This document provides a step-by-step guide for implementing the sectional mock test flow in the application.

## Initial Test Selection and Setup

### Step 1: Fetch a New Sectional Test
**Event**: `FetchNewSectionalTestEvent`
```dart
context.read<TestsBloc>().add(FetchNewSectionalTestEvent(subject: 'maths'));
```

**What happens**:
- The `_fetchNewSectionalTestEventHandler` in the TestsBloc is triggered
- It calls the `FetchNewSectionalTest` use case
- The use case fetches a new test from the repository based on the subject

**Returns**: `NewSectionalTestFetchedState`
- Contains the basic test information (testId, description, subject)
- Includes a newly generated `testAttemptId` (using UUID)

**Next Action**: Display test information to the user (test description, subject, etc.)

### Step 2: Fetch Test Data (Questions)
**Event**: `FetchTestDataEvent`
```dart
context.read<TestsBloc>().add(FetchTestDataEvent(testId: test.testId));
```

**What happens**:
- The `_fetchTestDataEventHandler` in the TestsBloc is triggered
- It calls the `FetchTestsData` use case
- The use case fetches the detailed test data including all questions

**Returns**: `TestsDataState`
- Contains the complete test data with all questions
- Sets the first question as the current question
- Sets `isFirstQuestion` to true and `isLastQuestion` to false

**Next Action**: Display the test overview to the user before starting

### Step 3: Mark Test as Started
**Event**: `MarkTestAsStartedEvent`
```dart
context.read<TestsBloc>().add(MarkTestAsStartedEvent(testAttemptId: testAttemptId));
```

**What happens**:
- The `_markTestAsStartedEventHandler` in the TestsBloc is triggered
- It calls the `MarkTestAsStarted` use case
- The use case marks the test as started in the repository
- This records the start time and initializes the test attempt

**Returns**: `TestStartedState`
- Similar to TestsDataState but with status updated to `TestStatus.started`

**Next Action**: Begin the test by showing the first question

## Question Navigation and Interaction

### Step 4: Navigate Between Questions
**Events**: 
- `FetchNextQuestionEvent` (to go to the next question)
- `FetchPreviousQuestionEvent` (to go to the previous question)

```dart
// For next question
context.read<TestsBloc>().add(FetchNextQuestionEvent(currentOrder: currentQuestion.order));

// For previous question
context.read<TestsBloc>().add(FetchPreviousQuestionEvent(currentOrder: currentQuestion.order));
```

**What happens**:
- The respective event handler is triggered
- It updates the current question based on the order
- It checks if the new question is the first or last question

**Returns**: `TestsDataState` with updated:
- `currentQuestion`
- `isFirstQuestion` (true if at first question)
- `isLastQuestion` (true if at last question)

**Special States**:
- `AtFirstQuestionState` if trying to go before the first question
- `AtLastQuestionState` if trying to go past the last question

**Next Action**: Display the new current question to the user

### Step 5: Answer a Question
**Event**: `AnswerAQuestionEvent`
```dart
context.read<TestsBloc>().add(AnswerAQuestionEvent(
  questionId: currentQuestion.questionId,
  selectedAnswer: 'A', // The option selected by the user
));
```

**What happens**:
- The `_answerAQuestionEventHandler` in the TestsBloc is triggered
- It calls the `AnswerAQuestion` use case
- The use case records the answer in the repository
- It also determines if the answer is correct by comparing with the correct answer

**Returns**: `AnswererCapturedState`
- Contains the updated test data with the question marked as attempted
- Includes a message confirming the answer was recorded

**Next Action**: Update the UI to show the question has been answered

### Step 6: Flag a Question for Review (Optional)
**Event**: `FlagAQuestionEvent`
```dart
context.read<TestsBloc>().add(FlagAQuestionEvent(
  questionId: currentQuestion.questionId,
));
```

**What happens**:
- The `_flagAQuestionEventHandler` in the TestsBloc is triggered
- It adds the question to the flaggedQuestions list in the state

**Returns**: `TestsDataState` with updated `flaggedQuestions` list

**Next Action**: Update the UI to show the question is flagged

### Step 7: View Flagged Questions (Optional)
**Event**: `ViewFlaggedQuestionsEvent`
```dart
context.read<TestsBloc>().add(ViewFlaggedQuestionsEvent());
```

**What happens**:
- The `_viewFlaggedQuestionsEventHandler` in the TestsBloc is triggered
- It prepares a view of all flagged questions

**Returns**: `FlaggedQuestionsViewState`
- Contains the list of flagged questions for review

**Next Action**: Display the list of flagged questions to the user

### Step 8: View Test Progress (Optional)
**Event**: `ViewTestProgressEvent`
```dart
context.read<TestsBloc>().add(ViewTestProgressEvent());
```

**What happens**:
- The `_viewTestProgressEventHandler` in the TestsBloc is triggered
- It calculates the progress based on attempted questions

**Returns**: `TestProgressState`
- Contains a summary of the test progress
- Includes information about attempted, unattempted, and flagged questions

**Next Action**: Display the test progress to the user

## Test Completion

### Step 9: Submit the Test
**Event**: `SubmitSectionalTestEvent`
```dart
context.read<TestsBloc>().add(SubmitSectionalTestEvent());
```

**What happens**:
- The `_submitSectionalTestEventHandler` in the TestsBloc is triggered
- It calls the `SubmitSectionalTest` use case
- The use case finalizes the test attempt in the repository
- This records the end time and marks the test as completed

**Returns**: `TestSubmittedState`
- Indicates the test has been successfully submitted

**Next Action**: Navigate to the test results screen

### Step 10: View Test Results
**Event**: `FetchSectionalTestAttemptDetailsEvent`
```dart
context.read<TestsBloc>().add(FetchSectionalTestAttemptDetailsEvent(
  testAttemptId: testAttemptId,
));
```

**What happens**:
- The `_fetchSectionalTestAttemptDetailsEventHandler` in the TestsBloc is triggered
- It calls the `FetchSectionalTestAttemptDetails` use case
- The use case retrieves the detailed results from the repository

**Returns**: `TestAttemptDetailsState`
- Contains detailed information about the test attempt
- Includes all questions, the user's answers, and the correct answers
- Includes statistics like score, time taken, etc.

**Next Action**: Display the detailed test results to the user

## Implementation Checklist

When implementing the sectional mock test flow, ensure you:

- [ ] Initialize the TestsBloc with all required use cases
- [ ] Create UI components for displaying test information
- [ ] Create UI components for displaying questions and answer options
- [ ] Implement navigation controls (next/previous buttons)
- [ ] Implement answer selection mechanism
- [ ] Create UI for flagging questions and viewing flagged questions
- [ ] Implement a test progress indicator
- [ ] Create a test submission button
- [ ] Design a results screen to display test performance
- [ ] Handle error states appropriately throughout the flow

## Error Handling

Throughout the test flow, be prepared to handle these potential errors:

1. **Network errors** when fetching test data
2. **Server errors** when submitting answers or the test
3. **Validation errors** if the user tries to submit without answering required questions
4. **Session timeout** if the test duration expires

Each error should be handled gracefully with appropriate user feedback.

## State Management Tips

1. Always check the current state before dispatching events
2. Use BlocBuilder to react to state changes in the UI
3. Consider using BlocListener for one-time actions like navigation
4. Keep track of the testAttemptId throughout the flow
5. Maintain a consistent UI state even during transitions
