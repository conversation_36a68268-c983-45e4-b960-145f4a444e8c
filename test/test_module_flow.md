# Test Module Flow Diagram

This document provides a visual representation and explanation of how the test module works in the SkillApp application.

## Flow Diagram

```mermaid
graph TD
    A[User] -->|Selects Test| B[FetchNewSectionalTestEvent]
    B -->|TestsBloc| C[_fetchNewSectionalTestEventHandler]
    C -->|FetchNewSectionalTest| D[TestsDataRepo]
    D -->|API Call| E[TestsDataRemoteDatasource]
    E -->|Returns| F[Test Object]
    F -->|Emits| G[NewSectionalTestFetchedState]
    
    G -->|User Starts Test| H[MarkTestAsStartedEvent]
    H -->|TestsBloc| I[_markTestAsStartedEventHandler]
    I -->|MarkTestAsStarted| J[TestsDataRepo]
    J -->|API Call| K[TestsDataRemoteDatasource]
    K -->|Returns| L[Test Started]
    L -->|Emits| M[TestStartedState]
    
    M -->|Load Questions| N[FetchTestDataEvent]
    N -->|TestsBloc| O[_fetchTestDataEventHandler]
    O -->|FetchTestsData| P[TestsDataRepo]
    P -->|API Call| Q[TestsDataRemoteDatasource]
    Q -->|Returns| R[SectionalTestData]
    R -->|Emits| S[TestsDataState]
    
    S -->|Navigate Questions| T[Question Navigation]
    T -->|Next Question| U[FetchNextQuestionEvent]
    T -->|Previous Question| V[FetchPreviousQuestionEvent]
    U -->|TestsBloc| W[_fetchNextQuestionEventHandler]
    V -->|TestsBloc| X[_fetchPreviousQuestionEventHandler]
    W -->|Updates Current Question| Y[TestsDataState with new currentQuestion]
    X -->|Updates Current Question| Y
    
    S -->|Answer Question| Z[AnswerAQuestionEvent]
    Z -->|TestsBloc| AA[_answerAQuestionEventHandler]
    AA -->|AnswerAQuestion| AB[TestsDataRepo]
    AB -->|API Call| AC[TestsDataRemoteDatasource]
    AC -->|Returns| AD[Answer Recorded]
    AD -->|Emits| AE[AnswererCapturedState]
    
    S -->|Flag Question| AF[FlagAQuestionEvent]
    AF -->|TestsBloc| AG[_flagAQuestionEventHandler]
    AG -->|Updates flaggedQuestions| AH[TestsDataState with updated flaggedQuestions]
    
    S -->|View Flagged Questions| AI[ViewFlaggedQuestionsEvent]
    AI -->|TestsBloc| AJ[_viewFlaggedQuestionsEventHandler]
    AJ -->|Emits| AK[FlaggedQuestionsViewState]
    
    S -->|View Specific Question| AL[ViewSelectedQuestionEvent]
    AL -->|TestsBloc| AM[_viewSelectedQuestionEventHandler]
    AM -->|Emits| AN[SelectedQuestionViewState]
    
    S -->|Submit Test| AO[SubmitSectionalTestEvent]
    AO -->|TestsBloc| AP[_submitSectionalTestEventHandler]
    AP -->|SubmitSectionalTest| AQ[TestsDataRepo]
    AQ -->|API Call| AR[TestsDataRemoteDatasource]
    AR -->|Returns| AS[Test Submitted]
    AS -->|Emits| AT[TestSubmittedState]
    
    AT -->|View Results| AU[FetchSectionalTestAttemptDetailsEvent]
    AU -->|TestsBloc| AV[_fetchSectionalTestAttemptDetailsEventHandler]
    AV -->|FetchSectionalTestAttemptDetails| AW[TestsDataRepo]
    AW -->|API Call| AX[TestsDataRemoteDatasource]
    AX -->|Returns| AY[Test Attempt Details]
    AY -->|Emits| AZ[TestAttemptDetailsState]
    
    subgraph "Test Flow"
        B --> G --> H --> M --> N --> S
        S --> T
        S --> Z
        S --> AF
        S --> AI
        S --> AL
        S --> AO --> AT --> AU --> AZ
    end
    
    subgraph "Question Navigation"
        T --> U
        T --> V
        U --> W --> Y
        V --> X --> Y
    end
    
    subgraph "Answer Processing"
        Z --> AA --> AB --> AC --> AD --> AE
    end
```

## Test Module Flow Explanation

Based on analysis of the codebase, here's how the test module works:

### 1. Test Initialization
- The user selects a test (e.g., by subject like "maths")
- This triggers a `FetchNewSectionalTestEvent` in the `TestsBloc`
- The bloc calls the `FetchNewSectionalTest` use case
- The use case interacts with the `TestsDataRepo` to fetch a new test
- The repository makes an API call via the `TestsDataRemoteDatasource`
- The result is returned as a `Test` object
- The bloc emits a `NewSectionalTestFetchedState` with the test data

### 2. Starting the Test
- The user starts the test
- This triggers a `MarkTestAsStartedEvent` in the `TestsBloc`
- The bloc calls the `MarkTestAsStarted` use case
- The use case interacts with the `TestsDataRepo` to mark the test as started
- The repository makes an API call to record the test start
- The bloc emits a `TestStartedState`

### 3. Loading Test Questions
- The `FetchTestDataEvent` is triggered to load the test questions
- The bloc calls the `FetchTestsData` use case
- The use case interacts with the `TestsDataRepo` to fetch the test data
- The repository makes an API call to get the `SectionalTestData`
- The bloc emits a `TestsDataState` with the test questions

### 4. Question Navigation
- The user can navigate between questions:
  - `FetchNextQuestionEvent` to go to the next question
  - `FetchPreviousQuestionEvent` to go to the previous question
- The bloc updates the `currentQuestion` in the state
- Special states are emitted for first/last questions:
  - `AtFirstQuestionState` when at the first question
  - `AtLastQuestionState` when at the last question

### 5. Answering Questions
- The user selects an answer
- This triggers an `AnswerAQuestionEvent` in the `TestsBloc`
- The bloc calls the `AnswerAQuestion` use case
- The use case interacts with the `TestsDataRepo` to record the answer
- The repository makes an API call to save the answer
- The bloc emits an `AnswererCapturedState`

### 6. Flagging Questions
- The user can flag questions for review
- This triggers a `FlagAQuestionEvent` in the `TestsBloc`
- The bloc updates the `flaggedQuestions` list in the state
- The user can view flagged questions via `ViewFlaggedQuestionsEvent`

### 7. Test Submission
- The user submits the test
- This triggers a `SubmitSectionalTestEvent` in the `TestsBloc`
- The bloc calls the `SubmitSectionalTest` use case
- The use case interacts with the `TestsDataRepo` to submit the test
- The repository makes an API call to finalize the test
- The bloc emits a `TestSubmittedState`

### 8. Viewing Results
- The user can view test results
- This triggers a `FetchSectionalTestAttemptDetailsEvent`
- The bloc calls the `FetchSectionalTestAttemptDetails` use case
- The use case interacts with the `TestsDataRepo` to fetch the details
- The repository makes an API call to get the test attempt details
- The bloc emits a `TestAttemptDetailsState` with the results

### 9. Test Progress Tracking
- Throughout the test, the user can view their progress
- This is handled by the `ViewTestProgressEvent`
- The bloc calculates the progress based on attempted questions
- The bloc emits a `TestProgressState` with the progress summary

## Architecture Components

The test module follows a clean architecture pattern with the following components:

### Presentation Layer
- **TestsBloc**: Manages the state of the test and handles user interactions
- **Events**: User actions that trigger state changes (e.g., `FetchNewSectionalTestEvent`)
- **States**: Different states of the test (e.g., `TestsDataState`, `TestStartedState`)

### Domain Layer
- **Use Cases**: Business logic for specific actions (e.g., `FetchNewSectionalTest`, `AnswerAQuestion`)
- **Entities**: Core business objects (e.g., `Test`, `TestQuestionEntry`)
- **Repository Interfaces**: Define methods for data operations (e.g., `TestsDataRepo`)

### Data Layer
- **Repository Implementations**: Implement the repository interfaces (e.g., `TestsDataRepoImpl`)
- **Data Sources**: Handle API calls and data retrieval (e.g., `TestsDataRemoteDatasource`)

This architecture ensures separation of concerns and makes the code more maintainable and testable.
